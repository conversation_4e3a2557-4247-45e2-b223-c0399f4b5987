﻿using System;
using Fpi.Data.Config;
using Fpi.UI.Common.PC;
using Fpi.Util.EnumRelated;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using Sunny.UI;

namespace Fpi.WMS3000.Pollution.UI.ExterDevices
{
    /// <summary>
    /// 污染源数据存储配置
    /// </summary>
    public partial class FrmPollutionDataSaveConfig : UIForm
    {
        #region 构造

        public FrmPollutionDataSaveConfig()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void FrmPollutionDataSaveConfig_Load(object sender, EventArgs e)
        {
            InitControl();

            LoadConfig();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                Check();

                Save();

                FpiMessageBox.ShowInfo($"保存污染源数据存储配置成功！");

                // 记录系统操作日志
                SystemOpLogHelper.SaveLog(new SystemOpLogInfo($"修改污染源数据存储配置", eOpType.配置操作, eOpStyle.本地操作));

                this.Close();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitControl()
        {
            var valueNodeList = DataManager.GetInstance().GetAllValueNodes().ToArray();
            cmbTotalFlowNode.Items.AddRange(valueNodeList);

            EnumOperate.BandEnumToCmb(cmbDayDataCalculateMethod, typeof(eDataCalculateMethod));
            EnumOperate.BandEnumToCmb(cmbMinuteDataCalculateMethod, typeof(eDataCalculateMethod));
        }

        /// <summary>
        /// 加载当前配置
        /// </summary>
        private void LoadConfig()
        {
            cmbDayDataCalculateMethod.SelectedValue = ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.DayDataCalculateMethod;
            cmbMinuteDataCalculateMethod.SelectedValue = ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.MinuteDataCalculateMethod;

            cmbTotalFlowNode.SelectedItem = ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.TotalFlowNode;
        }

        private void Check()
        {
            if(cmbDayDataCalculateMethod.SelectedIndex == -1)
            {
                throw new Exception("请选择日数据计算方法！");
            }
            if(cmbMinuteDataCalculateMethod.SelectedIndex == -1)
            {
                throw new Exception("请选择分钟小时数据计算方法！");
            }
            if(cmbTotalFlowNode.SelectedIndex == -1)
            {
                throw new Exception("请选择累计流量因子！");
            }
        }

        private void Save()
        {
            ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.DayDataCalculateMethod = (eDataCalculateMethod)cmbDayDataCalculateMethod.SelectedValue;
            ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.MinuteDataCalculateMethod = (eDataCalculateMethod)cmbMinuteDataCalculateMethod.SelectedValue;
            ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.TotalFlowNodeId = (cmbTotalFlowNode.SelectedItem as VarNode).id;

            ExterEquipConfigManager.GetInstance().Save();
        }

        #endregion
    }
}