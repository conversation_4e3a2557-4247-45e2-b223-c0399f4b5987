﻿using Fpi.WMS3000.Equipment.WCS3900;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    /// <summary>
    /// 单个试剂状态显示
    /// </summary>
    public partial class UC_OneWCS3900LiquidInfo : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 对应的试剂信息类
        /// </summary>
        private OneWCS3900LiquidInfo _liquidInfo;

        private const string ErrorInfo = "— — — — —";

        #endregion

        #region 构造

        public UC_OneWCS3900LiquidInfo()
        {
            InitializeComponent();
        }

        public UC_OneWCS3900LiquidInfo(OneWCS3900LiquidInfo liquidInfo) : this()
        {
            _liquidInfo = liquidInfo;

            if(_liquidInfo != null)
            {
                gbMain.Text = _liquidInfo.LiquidName;
            }
        }

        #endregion

        #region 公共方法

        internal void RefreshUI()
        {
            if(_liquidInfo != null)
            {
                lblLiquidSurplusUsedDays.Text = _liquidInfo.LiquidSurplusUsedDays == -1 ? ErrorInfo : _liquidInfo.LiquidSurplusUsedDays.ToString();
                lblLiquidResidual.Text = float.IsNaN(_liquidInfo.LiquidResidual) ? ErrorInfo : _liquidInfo.LiquidResidual.ToString("F2");
            }
        }

        #endregion
    }
}