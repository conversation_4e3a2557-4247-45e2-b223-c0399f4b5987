﻿using System;
using Fpi.WMS3000.Equipment.SIA3900;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    public partial class UC_OneSIA3900ReagentInfo : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 对应的试剂信息类
        /// </summary>
        private SIA3900ReagentInfo _liquidInfo;

        private const string ErrorInfo = "— — — — —";

        #endregion

        #region 构造

        public UC_OneSIA3900ReagentInfo()
        {
            InitializeComponent();
        }

        public UC_OneSIA3900ReagentInfo(SIA3900ReagentInfo liquidInfo) : this()
        {
            _liquidInfo = liquidInfo;

            if(_liquidInfo != null)
            {
                gbReagent.Text = $"试剂{_liquidInfo.ValvePosition}信息";
            }
        }

        #endregion

        #region 公有方法

        internal void RefreshUI()
        {
            if(_liquidInfo != null)
            {
                lblName.Text = Enum.IsDefined(typeof(eSIA3900ReagentType), _liquidInfo.ReagentName) && _liquidInfo.ReagentName != eSIA3900ReagentType.无效
                    ? _liquidInfo.ReagentName.ToString() : ErrorInfo;
                lblRemain.Text = _liquidInfo.LiquidResidual == -1 ? ErrorInfo : _liquidInfo.LiquidResidual.ToString();
                lblTotal.Text = _liquidInfo.LiquidTotal == -1 ? ErrorInfo : _liquidInfo.LiquidTotal.ToString();
                lblRemainDay.Text = _liquidInfo.RemainDay == -1 ? ErrorInfo : _liquidInfo.RemainDay.ToString();
                lblGuaranteePeriod.Text = _liquidInfo.GuaranteePeriod == -1 ? ErrorInfo : _liquidInfo.GuaranteePeriod.ToString();
                lblPosition.Text = _liquidInfo.ValvePosition == -1 ? ErrorInfo : _liquidInfo.ValvePosition.ToString();
            }
        }

        #endregion
    }
}