﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using NLPutils.Audio;
using NLPutils.iflytek.Aikit;

namespace NLPutils.iflytek.Aikit
{
    public class SpeechAikit
    {
        const string XTTS_ABILITY = "e2e44feff";
        const string IVW_ABILITY = "e867a88f2";//唤醒
        const string CNENIVW_ABILITY = "e3c82b5c3";
        const string ESR_ABILITY = "e75f07b62";
        // 2. 使用安全的包装方法
        private static readonly AikitProxyDll.OutputCallback s_safeOutputCallback = OnOutput;
        private static readonly AikitProxyDll.EventCallback s_safeEventCallback = OnEvent;
        private static readonly AikitProxyDll.ErrorCallback s_safeErrorCallback = OnError;
        public delegate void IVWaikitHandler();
        public static event IVWaikitHandler IVWawaken;

        // 单独定义的回调方法
        private static void OnOutput(string abilityID, string key, IntPtr valuePtr, int len)
        {
            // 关键：使用UTF-8编码转换
            byte[] buffer = new byte[len];
            Marshal.Copy(valuePtr, buffer, 0, len);
            //key :func_wake_up
            switch(abilityID)
            {
                case IVW_ABILITY:
                    string value = Encoding.UTF8.GetString(buffer);
                    if(IVWawaken != null)
                    {
                        IVWawaken();
                    }
                    Fpi.Log.LogUtil.Debug("NLPutils", "唤醒成功");
                    break;
                case XTTS_ABILITY:
                    TTSAikit.listByte.AddRange(buffer);
                    break;
            }

        }

        private static void OnEvent(int eventType, string desc)
        {
            if(eventType == 2)//事件结束
            {

            }

            Console.WriteLine($"Event: {eventType}, Desc: {desc}");
        }

        private static void OnError(int errCode, string errDesc)
        {


            Console.WriteLine($"Error: {errCode}, Desc: {errDesc}");
        }
        public static void Init()
        {
            string appID = NLPSetting.GetInstance().GetSetting("ifytekAppid", "");
            string apiKey = NLPSetting.GetInstance().GetSetting("ifytekApiKey", "");
            string apiSecret = NLPSetting.GetInstance().GetSetting("ifytekApiSecret", "");

            AikitProxyDll.AIKIT_InitParam param = new AikitProxyDll.AIKIT_InitParam
            {
                appID = appID,
                apiKey = apiKey,
                apiSecret = apiSecret,
                workDir = "./iflytek/Aikit",
                resDir = "./iflytek/Aikit/res/resource",
                authType = 0,
                ability = IVW_ABILITY + ";" + XTTS_ABILITY + ";" + ESR_ABILITY,
                logLevel = (int)AikitProxyDll.AIKIT_LOG_LEVEL.LOG_LVL_INFO,
                logMode = (int)AikitProxyDll.AIKIT_LOG_MODE.LOG_FILE,
                logPath = "./iflytek/Aikit/aikit.log",
            };
            int ret = AikitProxyDll.Aikit_App_Init(ref param);
            if(ret != 0)
            {
                string mess = "SpeechAikit  Aikit_app:" + ret.ToString();
                Fpi.Log.LogUtil.Debug("NLPutils", mess);
                throw new Exception(mess);
            }

            AikitProxyDll.Aikit_RegisterCallbacks(s_safeOutputCallback, s_safeEventCallback, s_safeErrorCallback);
        }

        public static void UnInit()
        {
            AikitProxyDll.Aikit_Ivw_Uninit();
        }
    }
}
