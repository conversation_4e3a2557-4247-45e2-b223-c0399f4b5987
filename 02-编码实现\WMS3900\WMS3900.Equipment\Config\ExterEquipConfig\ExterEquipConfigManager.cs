﻿using System.Collections.Generic;
using Fpi.Json;

namespace Fpi.WMS3000.Equipment.Config
{
    /// <summary>
    /// 动环设备参数配置
    /// </summary>
    public class ExterEquipConfigManager : BaseJsonNode
    {
        #region 字段属性

        /// <summary>
        /// 设备选择配置
        /// </summary>
        public DeviceSelectConfig DeviceSelect { get; private set; } = new DeviceSelectConfig();

        /// <summary>
        /// 摄像机选择配置
        /// </summary>
        public CameraSelectConfig CameraSelect { get; private set; } = new CameraSelectConfig();

        /// <summary>
        /// 门禁选择
        /// </summary>
        public EntranceSelectConfig EntranceSelect { get; private set; } =new EntranceSelectConfig();

        /// <summary>
        /// Pdu设备通道配置
        /// </summary>
        public List<PduConfig> PduConfigList { get; private set; } = new List<PduConfig>();

        /// <summary>
        /// 称重设备通道配置
        /// </summary>
        public List<WeightConfig> WeightConfigList { get; private set; } = new List<WeightConfig>();

        /// <summary>
        /// 污染源数据存储配置
        /// </summary>
        public PollutionDataSaveConfig PollutionDataSaveConfigInfo { get; private set; } = new PollutionDataSaveConfig();

        /// <summary>
        /// 采水参数配置
        /// </summary>
        public WaterCollectionConfig WaterCollectionConfigInfo { get; private set; } = new WaterCollectionConfig();

        /// <summary>
        /// 预处理参数配置
        /// </summary>
        public WaterPretreatmentConfig WaterPretreatmentConfigInfo { get; private set; } = new WaterPretreatmentConfig();

        /// <summary>
        /// 配水参数配置
        /// </summary>
        public WaterDistributeConfig WaterDistributeConfigInfo { get; private set; } = new WaterDistributeConfig();

        /// <summary>
        /// 站房环境相关参数配置
        /// </summary>
        public StationEnvConfig StationEnvConfigInfo { get; private set; } = new StationEnvConfig();

        /// <summary>
        /// 除湿机运行逻辑配置
        /// </summary>
        public DehumidifierConfig DehumidifierConfigInfo { get; private set; } = new DehumidifierConfig();

        /// <summary>
        /// 换气扇运行逻辑配置
        /// </summary>
        public AirVentilatorConfig AirVentilatorConfigInfo { get; private set; } = new AirVentilatorConfig();

        /// <summary>
        /// 空调运行逻辑配置
        /// </summary>
        public AirConditioningConfig AirConditioningConfigInfo { get; private set; } = new AirConditioningConfig();

        #endregion

        #region 单例

        private static readonly object SyncObj = new object();
        private static ExterEquipConfigManager _instance = null;
        public static ExterEquipConfigManager GetInstance()
        {
            lock(SyncObj)
            {
                if(_instance == null)
                {
                    _instance = new ExterEquipConfigManager();
                }
            }
            return _instance;
        }

        private ExterEquipConfigManager()
        {
            loadJson();
            //Save();
        }

        #endregion
    }
}