﻿using System;
using System.Windows.Forms;
using Fpi.UI.Common.PC;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.Equipment.QCD3900;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    public partial class UC_OneElementLifeInfo : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 对应的器件信息类
        /// </summary>
        private OneQCD3900ElementParamInfo _Info;

        /// <summary>
        /// 对应设备
        /// </summary>
        private QCD3900Equip _device;

        /// <summary>
        /// 设备使用参数类型
        /// </summary>
        private bool _isTime;

        private const string ErrorInfo = "— — — — —";

        #endregion

        #region 构造

        public UC_OneElementLifeInfo()
        {
            InitializeComponent();
        }

        public UC_OneElementLifeInfo(QCD3900Equip device, OneQCD3900ElementParamInfo info, bool isTime = false) : this()
        {
            _device = device;
            _Info = info;
            _isTime = isTime;
            if(isTime)
            {
                lblTotalTitle.Text = "总时长";
                lblUsedTitle.Text = "使用时长";
            }
            if(_Info != null)
            {
                gbMain.Text = _Info.Name;
            }
            RefreshUI();
        }

        #endregion

        #region 公共方法

        internal void RefreshUI()
        {
            if(_Info != null)
            {
                lblChangeTime.Text = _Info.ChangeTime == DateTime.MinValue ? ErrorInfo :
                      _Info.ChangeTime.ToString(DbConfig.DATETIME_FORMAT);
                string desc = _isTime ? "秒" : "";
                lblLiquidTotal.Text = float.IsNaN(_Info.Total) ? ErrorInfo : _Info.Total.ToString("F0") + desc;
                lblLiquidResidual.Text = float.IsNaN(_Info.Residual) ? ErrorInfo : _Info.Residual.ToString("F0") + desc;
                lblGuaranteePeriod.Text = _Info.GuaranteePeriod == -1 ? ErrorInfo : _Info.GuaranteePeriod.ToString() + "天";
            }
        }

        #endregion

        #region 事件

        private void btnReplacement_Click(object sender, EventArgs e)
        {
            try
            {
                if(FpiMessageBox.ShowQuestion($"确认更换{_Info.Name}，重置器件使用信息？") == DialogResult.Yes)
                {
                    _device.WriteInt32ParamToDevice((byte)(_Info.StartIndex + 0x36), (int)DateTime.Now.SecondsSince1970());

                    FpiMessageBox.ShowInfo($"触发更换{_Info.Name}成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"触发更换{_Info.Name}出错:{ex.Message}");
            }
        }

        private void btnModifyLiquidTotal_Click(object sender, EventArgs e)
        {
            try
            {
                if(!int.TryParse(txtLiquidTotal.Text, out int total) || total <= 0)
                {
                    throw new Exception($"器件{lblTotalTitle.Text}信息输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置器件{lblTotalTitle.Text}为{total:F2}？") == DialogResult.Yes)
                {
                    _device.WriteInt32ParamToDevice(_Info.StartIndex, total);

                    FpiMessageBox.ShowInfo($"修改{_Info.Name}{lblTotalTitle.Text}成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"修改{_Info.Name}{lblTotalTitle.Text}出错:{ex.Message}");
            }
        }

        /// <summary>
        /// 更改保质期
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnUpdataGuaranteePeriod_Click(object sender, EventArgs e)
        {
            try
            {
                if(!int.TryParse(txtGuaranteePeriod.Text, out int data) || data <= 0)
                {
                    throw new Exception("保质期信息输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置保质期为{data:F2}？") == DialogResult.Yes)
                {
                    _device.WriteInt16ParamToDevice((byte)(0x51 + (_Info.StartIndex - 0x51) / 2 + 0x4E), (short)data);

                    FpiMessageBox.ShowInfo($"修改{_Info.Name}保质期成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"修改{_Info.Name}保质期出错:{ex.Message}");
            }
        }

        #endregion
    }
}