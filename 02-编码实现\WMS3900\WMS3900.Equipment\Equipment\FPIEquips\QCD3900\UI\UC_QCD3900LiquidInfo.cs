﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using Fpi.UI.Common.PC;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.Equipment.QCD3900;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    /// <summary>
    /// 试剂信息
    /// </summary>
    public partial class btnRefreshOther : UIUserControl
    {
        #region 字段属性

        private QCD3900Equip _device;

        private const string ErrorInfo = "— — — — —";

        /// <summary>
        /// 试剂信息集合
        /// </summary>
        private List<UC_OneQCD3900LiquidInfo> _liquidinfos = new List<UC_OneQCD3900LiquidInfo>();

        #endregion

        #region 构造

        public btnRefreshOther()
        {
            InitializeComponent();
        }

        #endregion

        #region 公共方法

        internal void SetTragetDevice(QCD3900Equip device)
        {
            _device = device;
            foreach(eQCD3900LiquidToAnsysisType item in Enum.GetValues(typeof(eQCD3900LiquidToAnsysisType)))
            {
                UC_OneQCD3900LiquidInfo liquidInfo;
                if(item == eQCD3900LiquidToAnsysisType.废液)
                {
                    liquidInfo = new UC_OneQCD3900LiquidInfo(_device, _device.ElementLifeInfos.LiquidParamInfos[item], true);
                }
                else if(item == eQCD3900LiquidToAnsysisType.纯水)
                {
                    liquidInfo = new UC_OneQCD3900LiquidInfo(_device, _device.ElementLifeInfos.LiquidParamInfos[item], false, true);
                }
                else
                {
                    liquidInfo = new UC_OneQCD3900LiquidInfo(_device, _device.ElementLifeInfos.LiquidParamInfos[item]);
                }

                _liquidinfos.Add(liquidInfo);
            }

            for(int i = 0; i < _liquidinfos.Count; i++)
            {
                tabLiquidInfo.Controls.Add(_liquidinfos[i], i, 0);
            }
            RefreshUI();
            btnRefreshOthers_Click(null, null);
        }

        internal void RefreshUI()
        {
            if(_device != null)
            {
                foreach(var item in _liquidinfos)
                {
                    item.RefreshUI();
                }
            }
        }

        #endregion

        #region 事件

        private void btnRefreshOthers_Click(object sender, EventArgs e)
        {
            lblLiquidAlarmLimit.Text = _device.ElementLifeInfos.ReagentAlarmLimit == -1 ? ErrorInfo : _device.ElementLifeInfos.ReagentAlarmLimit.ToString();
            lblCompLifeAlarmLimit.Text = _device.ElementLifeInfos.CompLifeAlarmLimit == -1 ? ErrorInfo : _device.ElementLifeInfos.CompLifeAlarmLimit.ToString();
            lblWaterTubeChangeTime.Text = _device.ElementLifeInfos.WaterTubeChangeTime == DateTime.MinValue ?
                ErrorInfo : _device.ElementLifeInfos.WaterTubeChangeTime.ToString(DbConfig.DATETIME_FORMAT);
            lblWaterTubePG.Text = _device.ElementLifeInfos.WTubeGuaranteePeriod == -1 ? ErrorInfo :
                _device.ElementLifeInfos.WTubeGuaranteePeriod.ToString() + "天";
            lblReceiverRingChangeTime.Text = _device.ElementLifeInfos.ReceiverRingChangeTime == DateTime.MinValue ?
                ErrorInfo : _device.ElementLifeInfos.ReceiverRingChangeTime.ToString(DbConfig.DATETIME_FORMAT);
            lblReceiverRingPG.Text = _device.ElementLifeInfos.RRingGuaranteePeriod == -1 ? ErrorInfo :
          _device.ElementLifeInfos.RRingGuaranteePeriod.ToString() + "天";
            lblLevelDetector1ChangeTime.Text = _device.ElementLifeInfos.LevelDetector1ChangeTime == DateTime.MinValue ?
                ErrorInfo : _device.ElementLifeInfos.LevelDetector1ChangeTime.ToString(DbConfig.DATETIME_FORMAT);
            lblLevelDetector2ChangeTime.Text = _device.ElementLifeInfos.LevelDetector2ChangeTime == DateTime.MinValue ?
             ErrorInfo : _device.ElementLifeInfos.LevelDetector2ChangeTime.ToString(DbConfig.DATETIME_FORMAT);
            lblLevelDetector1PG.Text = _device.ElementLifeInfos.LDetector1GuaranteePeriod == -1 ? ErrorInfo :
          _device.ElementLifeInfos.LDetector1GuaranteePeriod.ToString() + "天";
            lblLevelDetector2PG.Text = _device.ElementLifeInfos.LDetector2GuaranteePeriod == -1 ? ErrorInfo :
          _device.ElementLifeInfos.LDetector2GuaranteePeriod.ToString() + "天";
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            RefreshUI();
        }

        private void btnSetLiquidAlarmLimit_Click(object sender, EventArgs e)
        {
            try
            {
                if(!short.TryParse(txtLiquidAlarmLimit.Text, out short limit) || limit < 1 || limit > 100)
                {
                    throw new Exception("阈值信息输入不合法，应为1-100的整数！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置试剂报警阈值为{limit}%？") == DialogResult.Yes)
                {
                    _device.WriteInt16ParamToDevice(0x26, limit);

                    FpiMessageBox.ShowInfo($"修改试剂报警阈值成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"修改试剂报警阈值出错:{ex.Message}");
            }
        }

        /// <summary>
        /// 修改寿命诊断报警阈值(%)
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSetLifeAlarmLimit_Click(object sender, EventArgs e)
        {
            try
            {
                if(!short.TryParse(txtLifeAlarmLimit.Text, out short limit) || limit < 1 || limit > 100)
                {
                    throw new Exception("阈值信息输入不合法，应为1-100的整数！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置寿命诊断报警阈值为{limit}%？") == DialogResult.Yes)
                {
                    _device.WriteInt16ParamToDevice(0x27, limit);

                    FpiMessageBox.ShowInfo($"修改寿命诊断报警阈值成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"修改寿命诊断报警阈值出错:{ex.Message}");
            }
        }

        #region 更换器件

        /// <summary>
        /// 更换水样管
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnChangeWaterTube_Click(object sender, EventArgs e)
        {
            try
            {
                if(FpiMessageBox.ShowQuestion($"确认更换水样管，重置器件使用信息？") == DialogResult.Yes)
                {
                    _device.WriteInt32ParamToDevice(0x4B, (int)DateTime.Now.SecondsSince1970());

                    FpiMessageBox.ShowInfo($"触发更换水样管成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"触发更换水样管出错:{ex.Message}");
            }
        }

        /// <summary>
        /// 更换储液环
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnChangeReceiverRing_Click(object sender, EventArgs e)
        {
            try
            {
                if(FpiMessageBox.ShowQuestion($"确认更换储液环，重置器件使用信息？") == DialogResult.Yes)
                {
                    _device.WriteInt32ParamToDevice(0x4D, (int)DateTime.Now.SecondsSince1970());

                    FpiMessageBox.ShowInfo($"触发更换储液环成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"触发更换储液环出错:{ex.Message}");
            }
        }

        /// <summary>
        /// 更换液位检测器1
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnChangeLevelDetector1_Click(object sender, EventArgs e)
        {
            try
            {
                if(FpiMessageBox.ShowQuestion($"确认更换液位检测器1，重置器件使用信息？") == DialogResult.Yes)
                {
                    _device.WriteInt32ParamToDevice(0x81, (int)DateTime.Now.SecondsSince1970());

                    FpiMessageBox.ShowInfo($"触发更换液位检测器1成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"触发更换液位检测器1出错:{ex.Message}");
            }
        }

        /// <summary>
        /// 更换液位检测器2
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnChangeLevelDetector2_Click(object sender, EventArgs e)
        {
            try
            {
                if(FpiMessageBox.ShowQuestion($"确认更换液位检测器2，重置器件使用信息？") == DialogResult.Yes)
                {
                    _device.WriteInt32ParamToDevice(0x83, (int)DateTime.Now.SecondsSince1970());

                    FpiMessageBox.ShowInfo($"触发更换液位检测器2成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"触发更换液位检测器2出错:{ex.Message}");
            }
        }

        #endregion

        #region 更换保质期

        /// <summary>
        /// 修改水样管保质期
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnChangeWaterTubeGP_Click(object sender, EventArgs e)
        {
            try
            {
                if(!short.TryParse(txtWaterTubeGP.Text, out short data) || data <= 0)
                {
                    throw new Exception("水样管保质期输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置水样管保质期为{data}？") == DialogResult.Yes)
                {
                    _device.WriteInt16ParamToDevice(0x4F, data);

                    FpiMessageBox.ShowInfo($"修改水样管保质期成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"修改水样管保质期出错:{ex.Message}");
            }
        }

        /// <summary>
        /// 修改储液环保质期
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnChangeReceiverRingGP_Click(object sender, EventArgs e)
        {
            try
            {
                if(!short.TryParse(txtReceiverRingGP.Text, out short data) || data <= 0)
                {
                    throw new Exception("储液环保质期输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置储液环保质期为{data}？") == DialogResult.Yes)
                {
                    _device.WriteInt16ParamToDevice(0x50, data);

                    FpiMessageBox.ShowInfo($"修改储液环保质期成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"修改储液环保质期出错:{ex.Message}");
            }
        }

        /// <summary>
        /// 修改液位检测1器件保质期
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnChangeLevelDetector1GP_Click(object sender, EventArgs e)
        {
            try
            {
                if(!short.TryParse(txtLevelDetector1GP.Text, out short data) || data <= 0)
                {
                    throw new Exception("液位检测1器件保质期输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置液位检测1器件保质期为{data}？") == DialogResult.Yes)
                {
                    _device.WriteInt16ParamToDevice(0x85, data);

                    FpiMessageBox.ShowInfo($"修改液位检测1器件保质期成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"修改液位检测1器件保质期出错:{ex.Message}");
            }
        }

        /// <summary>
        /// 修改液位检测2器件保质期
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnChangeLevelDetector2GP_Click(object sender, EventArgs e)
        {
            try
            {
                if(!short.TryParse(txtLevelDetector2GP.Text, out short data) || data <= 0)
                {
                    throw new Exception("液位检测2器件保质期输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置液位检测2器件保质期为{data}？") == DialogResult.Yes)
                {
                    _device.WriteInt16ParamToDevice(0x86, data);

                    FpiMessageBox.ShowInfo($"修改液位检测2器件保质期成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"修改液位检测2器件保质期出错:{ex.Message}");
            }
        }

        #endregion

        #endregion
    }
}