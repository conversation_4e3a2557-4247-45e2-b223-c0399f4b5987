﻿namespace Fpi.WMS3000.Equipment.Interface
{
    /// <summary>
    /// 混采采样器操作接口
    /// </summary>
    public interface IMixedSampleDeviceOperation
    {
        /// <summary>
        /// 启动某种操作
        /// </summary>
        void StartOper(eMixedSampleDeviceOperType type);

        /// <summary>
        /// 获取指定桶采水量
        /// </summary>
        /// <returns></returns>
        int GetBucketWaterVolume(eSamplingBucket samplingBucket);
    }
}