﻿using System;
using System.Windows.Forms;
using Fpi.UI.Common.PC;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    /// <summary>
    /// 单个电极核查参数配置
    /// </summary>
    public partial class UC_OneWCS3900CheckParam : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 对应设备
        /// </summary>
        private WCS3900Equip _device;

        /// <summary>
        /// Modbus起始地址
        /// </summary>
        private short _startIndex;

        #endregion

        #region 构造

        public UC_OneWCS3900CheckParam()
        {
            InitializeComponent();
        }

        #endregion

        #region 公共方法

        internal void SetTragetElectrode(WCS3900Equip device, string ElectrodeName, short startIndex)
        {
            _device = device;
            _startIndex = startIndex;
            gbMain.Text = ElectrodeName;
        }

        #endregion

        #region 事件

        private void btnReadCheckTime_Click(object sender, EventArgs e)
        {
            try
            {
                var checkTime = _device.ReadInt16ParamFromDevice(_startIndex);
                txtCheckTime.Text = checkTime.ToString();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"读取核查时长出错:{ex.Message}");
            }
        }

        private void btnSetCheckTime_Click(object sender, EventArgs e)
        {
            try
            {
                if(!short.TryParse(txtCheckTime.Text, out short checkTime))
                {
                    throw new Exception("核查时长输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置核查时长为{checkTime}？") == DialogResult.Yes)
                {
                    _device.WriteInt16ParamToDevice(_startIndex, checkTime);

                    FpiMessageBox.ShowInfo($"修改核查时长成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"修改核查时长出错:{ex.Message}");
            }
        }

        private void btnReadCheckInterVal_Click(object sender, EventArgs e)
        {
            try
            {
                var checkInterVal = _device.ReadInt16ParamFromDevice((short)(_startIndex + 1));
                txtCheckInterVal.Text = checkInterVal.ToString();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"读取核查间隔出错:{ex.Message}");
            }
        }

        private void btnSetCheckInterVal_Click(object sender, EventArgs e)
        {
            try
            {
                if(!short.TryParse(txtCheckInterVal.Text, out short checkInterVal))
                {
                    throw new Exception("核查间隔输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置核查间隔为{checkInterVal}？") == DialogResult.Yes)
                {
                    _device.WriteInt16ParamToDevice((short)(_startIndex + 1), checkInterVal);

                    FpiMessageBox.ShowInfo($"修改核查间隔成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"修改核查间隔出错:{ex.Message}");
            }
        }

        private void btnReadCalcuCount_Click(object sender, EventArgs e)
        {
            try
            {
                var calcuCount = _device.ReadInt16ParamFromDevice((short)(_startIndex + 2));
                txtCalcuCount.Text = calcuCount.ToString();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"读取方差计算组数出错:{ex.Message}");
            }
        }

        private void btnSetCalcuCount_Click(object sender, EventArgs e)
        {
            try
            {
                if(!short.TryParse(txtCalcuCount.Text, out short calcuCount))
                {
                    throw new Exception("方差计算组数输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置方差计算组数为{calcuCount}？") == DialogResult.Yes)
                {
                    _device.WriteInt16ParamToDevice((short)(_startIndex + 2), calcuCount);

                    FpiMessageBox.ShowInfo($"修改方差计算组数成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"修改方差计算组数出错:{ex.Message}");
            }
        }

        #endregion
    }
}