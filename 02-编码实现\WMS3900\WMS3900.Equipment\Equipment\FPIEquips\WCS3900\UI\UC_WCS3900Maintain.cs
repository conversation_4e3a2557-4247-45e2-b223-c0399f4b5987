﻿using System;
using System.Collections.Generic;
using System.Linq;
using Fpi.UI.Common.PC;
using Fpi.Util.EnumRelated;
using Fpi.WMS3000.Equipment.WCS3900;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    public partial class UC_WCS3900Maintain : UIUserControl
    {
        #region 字段属性

        private WCS3900Equip _device;

        #endregion

        #region 构造

        public UC_WCS3900Maintain()
        {
            InitializeComponent();
            EnumOperate.BandEnumToCmb(cmbWorkModel, typeof(eWCS3900WorkModel));
        }

        #endregion

        #region 公共方法

        internal void SetTragetDevice(WCS3900Equip device)
        {
            _device = device;
            InitUI();
        }

        #endregion

        #region 事件

        #region 工作模式

        /// <summary>
        /// 工作模式切换
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSwitchWorkModel_Click(object sender, EventArgs e)
        {
            try
            {
                if(cmbWorkModel.SelectedIndex == -1)
                {
                    throw new Exception("请选择工作模式！");
                }

                _device.SwitchWorkModel((eWCS3900WorkModel)cmbWorkModel.SelectedValue);

                FpiMessageBox.ShowInfo($"切换工作模式成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"{ex.Message}");
            }
        }

        #endregion

        #region 时间校准

        /// <summary>
        /// 时间校准
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnTimeCalibrate_Click(object sender, EventArgs e)
        {
            try
            {
                _device.SetDeviceTime(DateTime.Now);

                FpiMessageBox.ShowInfo($"触发设备时间校准成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        #region 流程控制

        /// <summary>
        /// 核查
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnTempCheck_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eWCSOperType.核查, GetSelectParamType());

                FpiMessageBox.ShowInfo($"触发{eWCSOperType.核查}成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 比对
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCompare_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eWCSOperType.比对, GetSelectParamType());

                FpiMessageBox.ShowInfo($"触发{eWCSOperType.比对}成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 标定
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCalibrate_Click(object sender, EventArgs e)
        {
            try
            {
                Dictionary<eWCSNodeType, WCS39900OperParams> calibrateOperParams = new Dictionary<eWCSNodeType, WCS39900OperParams>
                {
                    { eWCSNodeType.w01001, new WCS39900OperParams(chkCalibratePH.Checked, 0x00) },
                    { eWCSNodeType.w01014, new WCS39900OperParams(chkCalibrateElectric.Checked, 0x00) },
                    { eWCSNodeType.w01009, new WCS39900OperParams(chkCalibrateOxygen.Checked, 0x00) },
                    { eWCSNodeType.w01003, new WCS39900OperParams(chkCalibrateTurbidity.Checked, 0x00) },
                };

                _device.StartOper(eWCSOperType.标定, calibrateOperParams);

                FpiMessageBox.ShowInfo($"触发{eWCSOperType.标定}成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #region 系统

        private void btnMeasure_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eWCSOperType.五参数测量);

                FpiMessageBox.ShowInfo($"触发{eWCSOperType.五参数测量}成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        private void btnEmpty_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eWCSOperType.复位排空清洗);

                FpiMessageBox.ShowInfo($"触发{eWCSOperType.复位排空清洗}成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        private void btnStop_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eWCSOperType.紧急停止);

                FpiMessageBox.ShowInfo($"触发{eWCSOperType.紧急停止}成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        #endregion

        #endregion

        #region 私有方法

        internal void InitUI()
        {
            if(_device != null)
            {
                pnlElementControl.Controls.Clear();

                foreach(eWCS3900ElementType item in Enum.GetValues(typeof(eWCS3900ElementType)))
                {
                    var uc = new UC_OneWCS3900ElementControl(_device, item);
                    pnlElementControl.Controls.Add(uc);
                }

                uc_PHCheckParam.SetTragetElectrode(_device, "PH", 0x1031);
                uc_ConduCheckParam.SetTragetElectrode(_device, "电导率", 0x201D);
                uc_OxyCheckParam.SetTragetElectrode(_device, "溶解氧", 0x3022);
                uc_TurbCheckParam.SetTragetElectrode(_device, "浊度", 0x4031);
                uc_TempCheckParam.SetTragetElectrode(_device, "水温", 0x5010);

                List<UICheckBox> chklists = new List<UICheckBox>() { chkPH, chkElectric, chkOxygen, chkTurbidity };
                foreach(var item in chklists)
                {
                    GetPanel(item.Text).Enabled = item.Checked;
                    item.CheckedChanged += (object sender, EventArgs e) =>
                    {
                        GetPanel(item.Text).Enabled = item.Checked;
                    };
                }
            }
        }

        /// <summary>
        /// 根据名称获取对应的panel
        /// </summary>
        /// <param name="text"></param>
        /// <returns></returns>
        private UIPanel GetPanel(string text)
        {
            UIPanel panel = null;
            switch(text)
            {
                case "PH":
                    panel = pnlPH;
                    break;
                case "电导率":
                    panel = pnlElectric;
                    break;
                case "溶解氧":
                    panel = pnlOxygen;
                    break;
                case "浊度":
                    panel = pnlTurbidity;
                    break;
            }
            return panel;
        }

        /// <summary>
        /// 获取核查、比对所需参数
        /// </summary>
        /// <returns></returns>
        private Dictionary<eWCSNodeType, WCS39900OperParams> GetSelectParamType()
        {
            return new Dictionary<eWCSNodeType, WCS39900OperParams>
            {
                { eWCSNodeType.w01001, new WCS39900OperParams(chkPH.Checked, CheckRadioSelect(pnlPH,chkPH)) },
                { eWCSNodeType.w01014, new WCS39900OperParams(chkElectric.Checked, CheckRadioSelect(pnlElectric,chkElectric)) },
                { eWCSNodeType.w01009, new WCS39900OperParams(chkOxygen.Checked, CheckRadioSelect(pnlOxygen,chkOxygen)) },
                { eWCSNodeType.w01003, new WCS39900OperParams(chkTurbidity.Checked, CheckRadioSelect(pnlTurbidity,chkTurbidity)) },
                { eWCSNodeType.w01010, new WCS39900OperParams(chkTemp.Checked, 0x00) }
            };
        }

        /// <summary>
        /// 检索选中的radiobutton控件
        /// </summary>
        /// <param name="panel">radiobutton外层panel</param>
        /// <param name="checkbox">radiobutton外层checkbox</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        private byte CheckRadioSelect(UIPanel panel, UICheckBox checkbox)
        {
            byte data = 0x00;
            var selectButtom = panel.Controls.OfType<UIRadioButton>().FirstOrDefault(a => a.Checked);
            if(selectButtom == null)
            {
                if(checkbox.Checked)
                {
                    throw new Exception("请选择相应核查参数");
                }
            }
            else
            {
                switch(selectButtom.Text)
                {
                    case "自动":
                        data = 0x00;
                        break;
                    case "核查1":
                        data = 0x01;
                        break;
                    case "核查2":
                        data = 0x02;
                        break;
                    case "核查3":
                        data = 0x03;
                        break;
                    case "无氧核查":
                        data = 0x01;
                        break;
                    case "饱和氧核查":
                        data = 0x02;
                        break;
                }
            }
            return data;
        }

        #endregion      
    }
}