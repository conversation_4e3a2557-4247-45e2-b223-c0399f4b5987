﻿using Fpi.WMS3000.Equipment.WCS3900;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    /// <summary>
    /// 单个液位状态显示
    /// </summary>
    public partial class UC_OneWCS3900LevelState : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 对应设备
        /// </summary>
        private WCS3900Equip _device;

        /// <summary>
        /// 因子类型
        /// </summary>
        private eWCSNodeType _eWCSNodeType;

        /// <summary>
        /// 对应的器件类型
        /// </summary>
        private eWCS3900LevelType _levelType = 0;

        private const string ErrorInfo = "— — —";

        #endregion

        #region 构造

        public UC_OneWCS3900LevelState()
        {
            InitializeComponent();
        }

        public UC_OneWCS3900LevelState(WCS3900Equip device, eWCSNodeType wcsNodeType, eWCS3900LevelType levelType) : this()
        {
            _device = device;
            _eWCSNodeType = wcsNodeType;
            _levelType = levelType;
            gbMain.Text = _levelType.ToString();
        }

        #endregion

        #region 公共方法

        internal void RefreshUI()
        {
            if(_device != null && _device.CommonParam.CommonParams.ContainsKey(_eWCSNodeType))
            {
                var oneCommonParam = _device.CommonParam.CommonParams[_eWCSNodeType];
                if(oneCommonParam.LevelState.ContainsKey(_levelType))
                {
                    lblState.Text = oneCommonParam.LevelState[_levelType] ? "高液位" : "低液位";
                }
                else
                {
                    lblState.Text = ErrorInfo;
                }
            }
            else
            {
                lblState.Text = ErrorInfo;
            }
        }

        #endregion
    }
}