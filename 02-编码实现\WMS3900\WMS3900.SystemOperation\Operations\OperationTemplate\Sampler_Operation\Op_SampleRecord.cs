﻿using System;
using System.Text;
using Fpi.Data.Config;
using Fpi.Devices;
using Fpi.Operations;
using Fpi.Operations.Interfaces;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.Equipment.Interface;
using Fpi.WMS3000.SystemConfig;

namespace Fpi.WMS3000.SystemOperation.OperationTemplate
{
    /// <summary>
    /// 采样信息记录
    /// </summary>
    public class Op_SampeRecord : CustomOperation
    {
        #region 字段属性

        private bool _isInited;

        private Device _sampleEquip;

        #endregion

        #region 公共方法（重写）

        public override string ToString()
        {
            return "超标留样信息记录";
        }

        public override object CustomDo(string instrumentId, bool manual, object inputData, IOperationListener operationListener)
        {
            try
            {
                if(!_isInited)
                {
                    InitProperty();
                }

                // 当前留样瓶编号
                int currentBottleNum;
                // 最大留样瓶编号
                int maxBottleNum = 0;
                try
                {
                    currentBottleNum = ((ISampleBottleNum)_sampleEquip).GetCurrentBottleNum();
                    maxBottleNum = ((ISampleBottleNum)_sampleEquip).GetMaxBottleNum();

                }
                catch(Exception)
                {
                    currentBottleNum = -1;
                }

                // 留到下一瓶，所以当前瓶号加1
                if(currentBottleNum < maxBottleNum)
                {
                    currentBottleNum += 1;
                }
                // 如果当前瓶号大于等于最大瓶号，则置为-1，因为实际上不会进行留样
                else
                {
                    currentBottleNum = -1;
                }

                var sb = new StringBuilder();
                foreach(ValueNode node in DataManager.GetInstance().GetAllValueNodes())
                {
                    // 因子周期值是否报警（跳过采样无水的因子）
                    if(node.CycleFlag != (int)eValueNodeState.Z && node.IsOverAlarmLimit(node.CycleValue))
                    {
                        sb.Append(node.id).Append(",");
                    }
                }
                // 超标因子列表
                string polidlist = sb.ToString().TrimEnd(',');

                // 保存超标记录
                SaveDataHelper.SaveSamplingRecord(SystemHelper.SampleTime != DateTime.MinValue
                    ? SystemHelper.SampleTime : DateTime.Now, DateTime.Now, polidlist, currentBottleNum);

                SystemLogHelper.WriteOperationLog("[" + GetOperationName() + "]操作完成！");
            }
            catch(Exception ex)
            {
                SystemLogHelper.WriteSystemErrorLog("[" + GetOperationName() + "]操作异常: " + ex.Message);
                SystemLogHelper.WriteOperationLog("[" + GetOperationName() + "]操作异常: " + ex.Message);
            }

            return null;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化参数
        /// </summary>
        private void InitProperty()
        {
            if(ExterEquipConfigManager.GetInstance().DeviceSelect.SampleDevice is not ISampleBottleNum)
            {
                throw new Exception($"请配置标准超标留样器！");
            }
            _sampleEquip = ExterEquipConfigManager.GetInstance().DeviceSelect.SampleDevice;

            _isInited = true;
        }

        #endregion
    }
}