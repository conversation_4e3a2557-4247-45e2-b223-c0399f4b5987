﻿using System;
using System.Windows.Forms;
using Fpi.UI.Common.PC;
using Fpi.WMS3000.Equipment.WCS3900;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    /// <summary>
    /// 单个器件状态显示、控制
    /// </summary>
    public partial class UC_OneWCS3900ElementControl : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 对应的器件类型
        /// </summary>
        private eWCS3900ElementType _elementType = 0;

        /// <summary>
        /// 对应设备
        /// </summary>
        private WCS3900Equip _device;

        #endregion

        #region 构造

        public UC_OneWCS3900ElementControl()
        {
            InitializeComponent();
        }

        public UC_OneWCS3900ElementControl(WCS3900Equip device, eWCS3900ElementType elementType) : this()
        {
            _device = device;
            _elementType = elementType;
            gbMain.Text = _elementType.ToString();
        }

        #endregion

        #region 事件

        private void btnOpen_Click(object sender, EventArgs e)
        {
            try
            {
                eWCSNodeType type = Check();
                if(FpiMessageBox.ShowQuestion($"确认打开器件{_elementType}？") == DialogResult.Yes)
                {
                    _device.ElementControl(_elementType, true, type);

                    FpiMessageBox.ShowInfo($"打开器件{_elementType}成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"打开器件{_elementType}出错:{ex.Message}");
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            try
            {
                eWCSNodeType type = Check();
                if(FpiMessageBox.ShowQuestion($"确认关闭器件{_elementType}？") == DialogResult.Yes)
                {
                    _device.ElementControl(_elementType, false, type);

                    FpiMessageBox.ShowInfo($"关闭器件{_elementType}成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"关闭器件{_elementType}出错:{ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        private eWCSNodeType Check()
        {
            if(!(rdbPH.Checked || rdbElectric.Checked || rdbOxygen.Checked || rdbTurbidity.Checked))
            {
                throw new Exception("请选择电极地址");
            }

            return rdbPH.Checked ? eWCSNodeType.w01001 : rdbElectric.Checked ? eWCSNodeType.w01014 : rdbOxygen.Checked ?
                 eWCSNodeType.w01009 : eWCSNodeType.w01003;
        }

        #endregion
    }
}