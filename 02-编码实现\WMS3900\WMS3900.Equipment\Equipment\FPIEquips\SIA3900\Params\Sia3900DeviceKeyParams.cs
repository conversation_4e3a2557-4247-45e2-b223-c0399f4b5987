﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using Fpi.Communication.Converter;
using Fpi.DB.Manager;
using Fpi.DB.SqlUtil;
using Fpi.Json;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.Equipment.Common.CustomAttribute;

namespace Fpi.WMS3000.Equipment.SIA3900
{
    /// <summary>
    /// SIA3900仪表关键参数
    /// </summary>
    public class SIA3900DeviceKeyParams
    {
        #region 字段属性

        #region 关键参数1

        /// <summary>
        /// 测量精度
        /// </summary>
        [Description("测量精度")]
        public int Accuracy { get; set; } = -1;

        /// <summary>
        /// 消解温度
        /// </summary>
        [Description("消解温度")]
        public int DigestionTemp { get; set; } = -1;

        /// <summary>
        /// 消解时间
        /// </summary>
        [Description("消解时间")]
        public int DigestionTime { get; set; } = -1;

        /// <summary>
        /// 量程下限
        /// </summary>
        [Description("量程下限")]
        public float RangeLower { get; set; } = float.NaN;

        /// <summary>
        /// 量程上限
        /// </summary>
        [Description("量程上限")]
        public float RangeUpper { get; set; } = float.NaN;

        /// <summary>
        /// 曲线斜率k
        /// </summary>
        [Description("曲线斜率k")]
        public float K { get; set; } = float.NaN;

        /// <summary>
        /// 曲线截距b
        /// </summary>
        [Description("曲线截距b")]
        public float B { get; set; } = float.NaN;

        /// <summary>
        /// 标定日期
        /// </summary>
        [Description("标定日期")]
        public DateTime CalibrateTime { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 标液一浓度
        /// </summary>
        [Description("标液一浓度")]
        public float CalibrateConcentration1 { get; set; } = float.NaN;

        /// <summary>
        /// 标液一测量过程值
        /// </summary>
        [Description("标液一测量过程值")]
        public float CalibrateMeasureData1 { get; set; } = float.NaN;

        /// <summary>
        /// 标液二浓度
        /// </summary>
        [Description("标液二浓度")]
        public float CalibrateConcentration2 { get; set; } = float.NaN;

        /// <summary>
        /// 标液二测量过程值
        /// </summary>
        [Description("标液二测量过程值")]
        public float CalibrateMeasureData2 { get; set; } = float.NaN;

        /// <summary>
        /// 标液三浓度
        /// </summary>
        [Description("标液三浓度")]
        public float CalibrateConcentration3 { get; set; } = float.NaN;

        /// <summary>
        /// 标液三测量过程值
        /// </summary>
        [Description("标液三测量过程值")]
        public float CalibrateMeasureData3 { get; set; } = float.NaN;

        /// <summary>
        /// 标液四浓度
        /// </summary>
        [Description("标液四浓度")]
        public float CalibrateConcentration4 { get; set; } = float.NaN;

        /// <summary>
        /// 标液四测量过程值
        /// </summary>
        [Description("标液四测量过程值")]
        public float CalibrateMeasureData4 { get; set; } = float.NaN;

        /// <summary>
        /// 标液五浓度
        /// </summary>
        [Description("标液五浓度")]
        public float CalibrateConcentration5 { get; set; } = float.NaN;

        /// <summary>
        /// 标液五测量过程值
        /// </summary>
        [Description("标液五测量过程值")]
        public float CalibrateMeasureData5 { get; set; } = float.NaN;

        /// <summary>
        /// 线性相关系数（R或R²）
        /// </summary>
        [Description("线性相关系数（R或R²）")]
        public float Coefficient { get; set; } = float.NaN;

        /// <summary>
        /// 试剂编号
        /// </summary>
        [Description("试剂编号")]
        public string ReagentCode { get; set; }

        /// <summary>
        /// 试剂余量
        /// </summary>
        [Description("试剂余量")]
        public int ReagentAllowance { get; set; } = -1;

        /// <summary>
        /// 测量过程值
        /// </summary>
        [Description("测量过程值")]
        public float MensurationValue { get; set; } = float.NaN;

        /// <summary>
        /// 空白校准时间
        /// </summary>
        [Description("空白校准时间")]
        public DateTime BlankSampleCheckTime { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 标样校准时间
        /// </summary>
        [Description("标样校准时间")]
        public DateTime GuideSampleCheckTime { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 检出限值
        /// </summary>
        [Description("检出限值")]
        public float DetectionLimit { get; set; } = float.NaN;

        /// <summary>
        /// 校准系数
        /// </summary>
        [Description("校准系数")]
        public float CalibrationCoefficient { get; set; } = float.NaN;

        /// <summary>
        /// 原序列号
        /// </summary>
        [Description("原序列号")]
        public string OriginalNumber { get; set; }

        /// <summary>
        /// 二次多项式系数
        /// </summary>
        [Description("二次多项式系数")]
        public float QuadraticCoefficient { get; set; } = float.NaN;

        /// <summary>
        /// 空白标定过程值
        /// </summary>
        [Description("空白标定过程值")]
        public float BlankCalibrationValue { get; set; } = float.NaN;

        /// <summary>
        /// 空白校准过程值
        /// </summary>
        [Description("空白校准过程值")]
        public float BlankCheckValue { get; set; } = float.NaN;

        /// <summary>
        /// 标样校准参考值
        /// </summary>
        [Description("标样校准参考值")]
        public float GuideCheckReferenceValue { get; set; } = float.NaN;

        /// <summary>
        /// 标样校准过程值
        /// </summary>
        [Description("标样校准过程值")]
        public float GuideCheckValue { get; set; } = float.NaN;

        /// <summary>
        /// 显色温度
        /// </summary>
        [Description("显色温度")]
        public int ColorationTemp { get; set; } = -1;

        /// <summary>
        /// 显色时间
        /// </summary>
        [Description("显色时间")]
        public int ColorationTime { get; set; } = -1;

        /// <summary>
        /// 设备序列号
        /// </summary>
        [Description("设备序列号")]
        public string EquipmentNumber { get; set; }

        /// <summary>
        /// 零点液余量
        /// </summary>
        [Description("零点液余量")]
        public int ZeroFluidAllowance { get; set; } = -1;

        /// <summary>
        /// 废液桶余量
        /// </summary>
        [Description("废液桶余量")]
        public int WasteLiquidAllowance { get; set; } = -1;

        /// <summary>
        /// 稀释倍数
        /// </summary>
        [Description("稀释倍数")]
        public float DulutionRatio { get; set; } = float.NaN;

        #endregion

        #region 关键参数2

        /// <summary>
        /// 仪表当前动作
        /// </summary>
        [Description("仪表当前动作")]
        public string CurrentInstrumentAction { get; set; }

        #endregion

        #region 关键参数3

        /// <summary>
        /// 核查误差
        /// </summary>
        [Description("核查误差")]
        public float CheckError { get; set; } = float.NaN;

        /// <summary>
        /// 核查结果
        /// </summary>
        [Description("核查结果")]
        public float CheckResult { get; set; } = float.NaN;

        #endregion

        #region 其他区域补充（数据上传用）

        /// <summary>
        /// 测量间隔
        /// </summary>
        [Description("测量间隔")]
        [Visible(false)]
        public int MeasureSpan { get; set; } = -1;

        #endregion

        [NonSerialized]
        private static readonly object lockObj = new object();

        #endregion

        #region 公共方法

        #region 参数解析

        /// <summary>
        /// 解析关键参数1
        /// </summary>
        /// <param name="data"></param>
        /// <param name="startIndex"></param>
        public void UpdateKeyParam1(byte[] data, int startIndex)
        {
            if(data.Length < startIndex + 168)
            {
                throw new Exception("读取关键参数1回应数据不完整！");
            }

            Accuracy = DataConverter.GetInstance().ToInt32(data, startIndex);
            DigestionTemp = DataConverter.GetInstance().ToInt32(data, startIndex + 2);
            DigestionTime = DataConverter.GetInstance().ToInt32(data, startIndex + 4);
            RangeLower = DataConvertHelper.ParseFloatValue2143(data, startIndex + 6);
            RangeUpper = DataConvertHelper.ParseFloatValue2143(data, startIndex + 10);
            K = DataConvertHelper.ParseFloatValue2143(data, startIndex + 14);
            B = DataConvertHelper.ParseFloatValue2143(data, startIndex + 18);
            CalibrateTime = DataConvertHelper.ParseDateTimeFromBCD(data, startIndex + 22);
            CalibrateConcentration1 = DataConvertHelper.ParseFloatValue2143(data, startIndex + 28);
            CalibrateMeasureData1 = DataConvertHelper.ParseFloatValue2143(data, startIndex + 32);
            CalibrateConcentration2 = DataConvertHelper.ParseFloatValue2143(data, startIndex + 36);
            CalibrateMeasureData2 = DataConvertHelper.ParseFloatValue2143(data, startIndex + 40);
            CalibrateConcentration3 = DataConvertHelper.ParseFloatValue2143(data, startIndex + 44);
            CalibrateMeasureData3 = DataConvertHelper.ParseFloatValue2143(data, startIndex + 48);
            CalibrateConcentration4 = DataConvertHelper.ParseFloatValue2143(data, startIndex + 52);
            CalibrateMeasureData4 = DataConvertHelper.ParseFloatValue2143(data, startIndex + 56);
            CalibrateConcentration5 = DataConvertHelper.ParseFloatValue2143(data, startIndex + 60);
            CalibrateMeasureData5 = DataConvertHelper.ParseFloatValue2143(data, startIndex + 64);
            Coefficient = DataConvertHelper.ParseFloatValue2143(data, startIndex + 68);
            ReagentCode = DataConverter.GetInstance().ToString(data, startIndex + 72, 2);
            ReagentAllowance = DataConverter.GetInstance().ToInt32(data, startIndex + 74);
            MensurationValue = DataConvertHelper.ParseFloatValue2143(data, startIndex + 76);
            BlankSampleCheckTime = DataConvertHelper.ParseDateTimeFromBCD(data, startIndex + 80);
            GuideSampleCheckTime = DataConvertHelper.ParseDateTimeFromBCD(data, startIndex + 86);
            DetectionLimit = DataConvertHelper.ParseFloatValue2143(data, startIndex + 92);
            CalibrationCoefficient = DataConvertHelper.ParseFloatValue2143(data, startIndex + 96);
            OriginalNumber = DataConverter.GetInstance().ToString(data, startIndex + 100, 12);
            QuadraticCoefficient = DataConvertHelper.ParseFloatValue2143(data, startIndex + 112);
            BlankCalibrationValue = DataConvertHelper.ParseFloatValue2143(data, startIndex + 116);
            BlankCheckValue = DataConvertHelper.ParseFloatValue2143(data, startIndex + 120);
            GuideCheckReferenceValue = DataConvertHelper.ParseFloatValue2143(data, startIndex + 124);
            GuideCheckValue = DataConvertHelper.ParseFloatValue2143(data, startIndex + 128);
            ColorationTemp = DataConverter.GetInstance().ToInt32(data, startIndex + 132);
            ColorationTime = DataConverter.GetInstance().ToInt32(data, startIndex + 134);
            EquipmentNumber = DataConverter.GetInstance().ToString(data, startIndex + 136, 24);
            ZeroFluidAllowance = DataConverter.GetInstance().ToInt32(data, startIndex + 160);
            WasteLiquidAllowance = DataConverter.GetInstance().ToInt32(data, startIndex + 162);
            DulutionRatio = DataConvertHelper.ParseFloatValue2143(data, startIndex + 164);
        }

        /// <summary>
        /// 解析仪表当前动作
        /// </summary>
        /// <param name="data"></param>
        /// <param name="startIndex"></param>
        public void UpdateKeyParam2(byte[] data, int startIndex)
        {
            if(data.Length < startIndex + 40)
            {
                throw new Exception("读取仪表当前动作回应数据不完整！");
            }

            CurrentInstrumentAction = DataConvertHelper.GetChineseOrNumberOrWord(DataConverter.GetInstance().ToString(data, startIndex, 40));
        }

        /// <summary>
        /// 解析关键参数3
        /// </summary>
        /// <param name="data"></param>
        /// <param name="startIndex"></param>
        public void UpdateKeyParam3(byte[] data, int startIndex)
        {
            if(data.Length < startIndex + 6)
            {
                throw new Exception("读取关键参数3回应数据不完整！");
            }

            CheckError = DataConvertHelper.ParseFloatValue2143(data, startIndex);
            CheckResult = DataConverter.GetInstance().ToInt32(data, startIndex + 4);
        }

        #endregion

        #region 数据读写

        /// <summary>
        /// 保存数据到数据库中
        /// </summary>
        /// <param name="sourceId"></param>
        /// <param name="dataTime"></param>
        public void SaveToDb(string sourceId, DateTime dataTime)
        {
            if(!string.IsNullOrWhiteSpace(sourceId))
            {
                WriteSIA3900KeyParamsToDb(sourceId, dataTime, FpiJsonHelper.ModelToJson(this));
            }
        }

        #region 静态辅助方法

        /// <summary>
        /// 写SIA3900关键参数到数据库
        /// </summary>
        /// <param name="sourceId"></param>
        /// <param name="dataTime"></param>
        /// <param name="keyparams"></param>
        public static void WriteSIA3900KeyParamsToDb(string sourceId, DateTime dataTime, string keyparams)
        {
            FpiTable table = FpiDataBase.GetInstance().FindTableByName(DbConfig.DEVICE_PARAM_TABLE);
            if(table == null)
            {
                throw new Exception("设备关键参数表不存在！");
            }

            lock(lockObj)
            {
                FpiRow row = new FpiRow();
                row.SetFieldValue("sourceid", sourceId);
                row.SetFieldValue("datatime", dataTime);
                row.SetFieldValue("keyparams", keyparams);
                table.AddRecord(row);
            }
        }

        /// <summary>
        /// 读取SIA3900关键参数
        /// </summary>
        /// <param name="sourceId"></param>
        /// <param name="dataTime"></param>
        public static SIA3900DeviceKeyParams ReadSIA3900KeyParamsFormDb(string sourceId, DateTime dataTime)
        {
            SIA3900DeviceKeyParams sIA3900DeviceKeyParams = null;

            FpiTable table = FpiDataBase.GetInstance().FindTableByName(DbConfig.DEVICE_PARAM_TABLE);
            if(table != null)
            {
                lock(lockObj)
                {
                    try
                    {
                        SearchConditionCollection condition = new SearchConditionCollection
                        {
                            new SearchCondition("sourceid", new ColumnComparison(SqlOperator.Equal, sourceId)),
                            new SearchCondition("datatime", new ColumnComparison(SqlOperator.Equal, dataTime))
                        };
                        var result = table.Search(condition);
                        if(result != null && result.Count > 0)
                        {
                            FpiRow row = result[0];
                            sIA3900DeviceKeyParams = FpiJsonHelper.JsonToModel<SIA3900DeviceKeyParams>(Convert.ToString(row.GetFieldValue("keyparams")));
                        }
                    }
                    catch
                    {
                        // Ignored
                    }
                }
            }

            return sIA3900DeviceKeyParams;
        }

        /// <summary>
        /// 获取此时间段内的所有关键参数
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public static List<(string, DateTime, SIA3900DeviceKeyParams)> GetAllKeyParamsFromDB(DateTime startTime, DateTime endTime)
        {
            var keyParamsList = new List<(string, DateTime, SIA3900DeviceKeyParams)>();

            FpiTable table = FpiDataBase.GetInstance().FindTableByName(DbConfig.DEVICE_PARAM_TABLE);
            if(table != null)
            {
                lock(lockObj)
                {
                    try
                    {
                        SearchConditionCollection condition = new SearchConditionCollection
                        {
                            new SearchCondition("datatime", new ColumnComparison(SqlOperator.Between, startTime,endTime))
                        };
                        var result = table.Search(condition);
                        if(result != null)
                        {
                            foreach(var row in result)
                            {
                                try
                                {
                                    var nodeId = Convert.ToString(row.GetFieldValue("sourceid"));
                                    var dateTime = Convert.ToDateTime(row.GetFieldValue("datatime"));
                                    var keyParams = FpiJsonHelper.JsonToModel<SIA3900DeviceKeyParams>(Convert.ToString(row.GetFieldValue("keyparams")));
                                    keyParamsList.Add((nodeId, dateTime, keyParams));
                                }
                                catch
                                {
                                }
                            }
                        }
                    }
                    catch
                    {
                        // Ignored
                    }
                }
            }

            return keyParamsList;
        }

        #endregion

        #endregion

        #endregion
    }
}