﻿namespace Fpi.WMS3000.Equipment.UI
{
    partial class UC_HDZSCIXCDeviceOperControl
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if(disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.gbFlowControl = new Sunny.UI.UIGroupBox();
            this.uiGroupBox6 = new Sunny.UI.UIGroupBox();
            this.uiGroupBox8 = new Sunny.UI.UIGroupBox();
            this.btnDrainB = new Sunny.UI.UIButton();
            this.btnRetainB = new Sunny.UI.UIButton();
            this.btnSupplyB = new Sunny.UI.UIButton();
            this.btnWaterSamplingB = new Sunny.UI.UIButton();
            this.uiGroupBox7 = new Sunny.UI.UIGroupBox();
            this.btnDrainA = new Sunny.UI.UIButton();
            this.btnRetainA = new Sunny.UI.UIButton();
            this.btnSupplyA = new Sunny.UI.UIButton();
            this.btnWaterSamplingA = new Sunny.UI.UIButton();
            this.btnSupplyPipeDrain = new Sunny.UI.UIButton();
            this.btnABSamplingVolumeSet = new Sunny.UI.UIButton();
            this.txtABSamplingVolume = new Sunny.UI.UITextBox();
            this.txtABSupplyTime = new Sunny.UI.UITextBox();
            this.btnABSupplyTimeSet = new Sunny.UI.UIButton();
            this.btnPassiveRetainVolumeSet = new Sunny.UI.UIButton();
            this.txtPassiveRetainVolume = new Sunny.UI.UITextBox();
            this.uiGroupBox5 = new Sunny.UI.UIGroupBox();
            this.btnAllBottlesDrain = new Sunny.UI.UIButton();
            this.btnAllBottlesRinse = new Sunny.UI.UIButton();
            this.txtBottleNumber = new Sunny.UI.UITextBox();
            this.btnSpecificBottleRinse = new Sunny.UI.UIButton();
            this.btnSpecificBottleDrain = new Sunny.UI.UIButton();
            this.txtRetainVolume = new Sunny.UI.UITextBox();
            this.txtExceedWaitTime = new Sunny.UI.UITextBox();
            this.txtParallelSamples = new Sunny.UI.UITextBox();
            this.cmbRetainMode = new Sunny.UI.UIComboBox();
            this.btnStopAndReset = new Sunny.UI.UIButton();
            this.btnTimeCalibrate = new Sunny.UI.UIButton();
            this.cmbStartSignal = new Sunny.UI.UIComboBox();
            this.txtInstantRetainVolume = new Sunny.UI.UITextBox();
            this.txtSupplyTime = new Sunny.UI.UITextBox();
            this.cmbSupplyMode = new Sunny.UI.UIComboBox();
            this.gbFlowControl.SuspendLayout();
            this.uiGroupBox6.SuspendLayout();
            this.uiGroupBox8.SuspendLayout();
            this.uiGroupBox7.SuspendLayout();
            this.uiGroupBox5.SuspendLayout();
            this.SuspendLayout();
            // 
            // gbFlowControl
            // 
            this.gbFlowControl.Controls.Add(this.uiGroupBox6);
            this.gbFlowControl.Controls.Add(this.uiGroupBox5);
            this.gbFlowControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbFlowControl.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbFlowControl.Location = new System.Drawing.Point(1, 1);
            this.gbFlowControl.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbFlowControl.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbFlowControl.Name = "gbFlowControl";
            this.gbFlowControl.Padding = new System.Windows.Forms.Padding(0, 32, 0, 0);
            this.gbFlowControl.Size = new System.Drawing.Size(1614, 894);
            this.gbFlowControl.TabIndex = 0;
            this.gbFlowControl.Text = "采样器控制";
            this.gbFlowControl.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiGroupBox6
            // 
            this.uiGroupBox6.Controls.Add(this.btnTimeCalibrate);
            this.uiGroupBox6.Controls.Add(this.uiGroupBox8);
            this.uiGroupBox6.Controls.Add(this.uiGroupBox7);
            this.uiGroupBox6.Controls.Add(this.btnSupplyPipeDrain);
            this.uiGroupBox6.Controls.Add(this.btnABSamplingVolumeSet);
            this.uiGroupBox6.Controls.Add(this.txtABSamplingVolume);
            this.uiGroupBox6.Controls.Add(this.txtABSupplyTime);
            this.uiGroupBox6.Controls.Add(this.btnABSupplyTimeSet);
            this.uiGroupBox6.Controls.Add(this.btnPassiveRetainVolumeSet);
            this.uiGroupBox6.Controls.Add(this.btnStopAndReset);
            this.uiGroupBox6.Controls.Add(this.txtPassiveRetainVolume);
            this.uiGroupBox6.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiGroupBox6.Location = new System.Drawing.Point(4, 35);
            this.uiGroupBox6.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiGroupBox6.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiGroupBox6.Name = "uiGroupBox6";
            this.uiGroupBox6.Padding = new System.Windows.Forms.Padding(0, 32, 0, 0);
            this.uiGroupBox6.Size = new System.Drawing.Size(356, 562);
            this.uiGroupBox6.TabIndex = 0;
            this.uiGroupBox6.Text = "混采控制(全被动模式)";
            this.uiGroupBox6.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiGroupBox8
            // 
            this.uiGroupBox8.Controls.Add(this.btnDrainB);
            this.uiGroupBox8.Controls.Add(this.btnRetainB);
            this.uiGroupBox8.Controls.Add(this.btnSupplyB);
            this.uiGroupBox8.Controls.Add(this.btnWaterSamplingB);
            this.uiGroupBox8.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiGroupBox8.Location = new System.Drawing.Point(181, 315);
            this.uiGroupBox8.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiGroupBox8.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiGroupBox8.Name = "uiGroupBox8";
            this.uiGroupBox8.Padding = new System.Windows.Forms.Padding(0, 32, 0, 0);
            this.uiGroupBox8.Size = new System.Drawing.Size(169, 240);
            this.uiGroupBox8.TabIndex = 8;
            this.uiGroupBox8.Text = "B桶控制";
            this.uiGroupBox8.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // btnDrainB
            // 
            this.btnDrainB.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnDrainB.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnDrainB.Location = new System.Drawing.Point(20, 191);
            this.btnDrainB.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnDrainB.Name = "btnDrainB";
            this.btnDrainB.Size = new System.Drawing.Size(128, 35);
            this.btnDrainB.TabIndex = 3;
            this.btnDrainB.Text = "B桶排空";
            this.btnDrainB.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnDrainB.Click += new System.EventHandler(this.btnDrainB_Click);
            // 
            // btnRetainB
            // 
            this.btnRetainB.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnRetainB.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnRetainB.Location = new System.Drawing.Point(20, 140);
            this.btnRetainB.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnRetainB.Name = "btnRetainB";
            this.btnRetainB.Size = new System.Drawing.Size(128, 35);
            this.btnRetainB.TabIndex = 2;
            this.btnRetainB.Text = "B桶留样";
            this.btnRetainB.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnRetainB.Click += new System.EventHandler(this.btnRetainB_Click);
            // 
            // btnSupplyB
            // 
            this.btnSupplyB.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnSupplyB.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSupplyB.Location = new System.Drawing.Point(20, 89);
            this.btnSupplyB.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnSupplyB.Name = "btnSupplyB";
            this.btnSupplyB.Size = new System.Drawing.Size(128, 35);
            this.btnSupplyB.TabIndex = 1;
            this.btnSupplyB.Text = "B桶供样";
            this.btnSupplyB.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSupplyB.Click += new System.EventHandler(this.btnSupplyB_Click);
            // 
            // btnWaterSamplingB
            // 
            this.btnWaterSamplingB.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnWaterSamplingB.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnWaterSamplingB.Location = new System.Drawing.Point(20, 38);
            this.btnWaterSamplingB.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnWaterSamplingB.Name = "btnWaterSamplingB";
            this.btnWaterSamplingB.Size = new System.Drawing.Size(128, 35);
            this.btnWaterSamplingB.TabIndex = 0;
            this.btnWaterSamplingB.Text = "B桶采水";
            this.btnWaterSamplingB.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnWaterSamplingB.Click += new System.EventHandler(this.btnWaterSamplingB_Click);
            // 
            // uiGroupBox7
            // 
            this.uiGroupBox7.Controls.Add(this.btnDrainA);
            this.uiGroupBox7.Controls.Add(this.btnRetainA);
            this.uiGroupBox7.Controls.Add(this.btnSupplyA);
            this.uiGroupBox7.Controls.Add(this.btnWaterSamplingA);
            this.uiGroupBox7.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiGroupBox7.Location = new System.Drawing.Point(4, 315);
            this.uiGroupBox7.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiGroupBox7.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiGroupBox7.Name = "uiGroupBox7";
            this.uiGroupBox7.Padding = new System.Windows.Forms.Padding(0, 32, 0, 0);
            this.uiGroupBox7.Size = new System.Drawing.Size(169, 240);
            this.uiGroupBox7.TabIndex = 7;
            this.uiGroupBox7.Text = "A桶控制";
            this.uiGroupBox7.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // btnDrainA
            // 
            this.btnDrainA.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnDrainA.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnDrainA.Location = new System.Drawing.Point(20, 191);
            this.btnDrainA.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnDrainA.Name = "btnDrainA";
            this.btnDrainA.Size = new System.Drawing.Size(128, 35);
            this.btnDrainA.TabIndex = 3;
            this.btnDrainA.Text = "A桶排空";
            this.btnDrainA.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnDrainA.Click += new System.EventHandler(this.btnDrainA_Click);
            // 
            // btnRetainA
            // 
            this.btnRetainA.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnRetainA.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnRetainA.Location = new System.Drawing.Point(20, 140);
            this.btnRetainA.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnRetainA.Name = "btnRetainA";
            this.btnRetainA.Size = new System.Drawing.Size(128, 35);
            this.btnRetainA.TabIndex = 2;
            this.btnRetainA.Text = "A桶留样";
            this.btnRetainA.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnRetainA.Click += new System.EventHandler(this.btnRetainA_Click);
            // 
            // btnSupplyA
            // 
            this.btnSupplyA.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnSupplyA.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSupplyA.Location = new System.Drawing.Point(20, 89);
            this.btnSupplyA.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnSupplyA.Name = "btnSupplyA";
            this.btnSupplyA.Size = new System.Drawing.Size(128, 35);
            this.btnSupplyA.TabIndex = 1;
            this.btnSupplyA.Text = "A桶供样";
            this.btnSupplyA.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSupplyA.Click += new System.EventHandler(this.btnSupplyA_Click);
            // 
            // btnWaterSamplingA
            // 
            this.btnWaterSamplingA.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnWaterSamplingA.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnWaterSamplingA.Location = new System.Drawing.Point(20, 38);
            this.btnWaterSamplingA.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnWaterSamplingA.Name = "btnWaterSamplingA";
            this.btnWaterSamplingA.Size = new System.Drawing.Size(128, 35);
            this.btnWaterSamplingA.TabIndex = 0;
            this.btnWaterSamplingA.Text = "A桶采水";
            this.btnWaterSamplingA.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnWaterSamplingA.Click += new System.EventHandler(this.btnWaterSamplingA_Click);
            // 
            // btnSupplyPipeDrain
            // 
            this.btnSupplyPipeDrain.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnSupplyPipeDrain.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSupplyPipeDrain.Location = new System.Drawing.Point(201, 210);
            this.btnSupplyPipeDrain.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnSupplyPipeDrain.Name = "btnSupplyPipeDrain";
            this.btnSupplyPipeDrain.Size = new System.Drawing.Size(128, 35);
            this.btnSupplyPipeDrain.TabIndex = 6;
            this.btnSupplyPipeDrain.Text = "供样管路排空";
            this.btnSupplyPipeDrain.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSupplyPipeDrain.Click += new System.EventHandler(this.btnSupplyPipeDrain_Click);
            // 
            // btnABSamplingVolumeSet
            // 
            this.btnABSamplingVolumeSet.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnABSamplingVolumeSet.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnABSamplingVolumeSet.Location = new System.Drawing.Point(201, 33);
            this.btnABSamplingVolumeSet.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnABSamplingVolumeSet.Name = "btnABSamplingVolumeSet";
            this.btnABSamplingVolumeSet.Size = new System.Drawing.Size(128, 35);
            this.btnABSamplingVolumeSet.TabIndex = 1;
            this.btnABSamplingVolumeSet.Text = "采水量设置";
            this.btnABSamplingVolumeSet.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnABSamplingVolumeSet.Click += new System.EventHandler(this.btnABSamplingVolumeSet_Click);
            // 
            // txtABSamplingVolume
            // 
            this.txtABSamplingVolume.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtABSamplingVolume.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtABSamplingVolume.Location = new System.Drawing.Point(32, 36);
            this.txtABSamplingVolume.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtABSamplingVolume.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtABSamplingVolume.Name = "txtABSamplingVolume";
            this.txtABSamplingVolume.Padding = new System.Windows.Forms.Padding(5);
            this.txtABSamplingVolume.ShowText = false;
            this.txtABSamplingVolume.Size = new System.Drawing.Size(150, 29);
            this.txtABSamplingVolume.TabIndex = 0;
            this.txtABSamplingVolume.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtABSamplingVolume.Watermark = "采水量 ml";
            // 
            // txtABSupplyTime
            // 
            this.txtABSupplyTime.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtABSupplyTime.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtABSupplyTime.Location = new System.Drawing.Point(32, 95);
            this.txtABSupplyTime.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtABSupplyTime.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtABSupplyTime.Name = "txtABSupplyTime";
            this.txtABSupplyTime.Padding = new System.Windows.Forms.Padding(5);
            this.txtABSupplyTime.ShowText = false;
            this.txtABSupplyTime.Size = new System.Drawing.Size(150, 29);
            this.txtABSupplyTime.TabIndex = 2;
            this.txtABSupplyTime.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtABSupplyTime.Watermark = "供样时间 分钟";
            // 
            // btnABSupplyTimeSet
            // 
            this.btnABSupplyTimeSet.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnABSupplyTimeSet.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnABSupplyTimeSet.Location = new System.Drawing.Point(201, 92);
            this.btnABSupplyTimeSet.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnABSupplyTimeSet.Name = "btnABSupplyTimeSet";
            this.btnABSupplyTimeSet.Size = new System.Drawing.Size(128, 35);
            this.btnABSupplyTimeSet.TabIndex = 3;
            this.btnABSupplyTimeSet.Text = "供样时间设置";
            this.btnABSupplyTimeSet.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnABSupplyTimeSet.Click += new System.EventHandler(this.btnABSupplyTimeSet_Click);
            // 
            // btnPassiveRetainVolumeSet
            // 
            this.btnPassiveRetainVolumeSet.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnPassiveRetainVolumeSet.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnPassiveRetainVolumeSet.Location = new System.Drawing.Point(201, 151);
            this.btnPassiveRetainVolumeSet.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnPassiveRetainVolumeSet.Name = "btnPassiveRetainVolumeSet";
            this.btnPassiveRetainVolumeSet.Size = new System.Drawing.Size(128, 35);
            this.btnPassiveRetainVolumeSet.TabIndex = 5;
            this.btnPassiveRetainVolumeSet.Text = "留样量设置";
            this.btnPassiveRetainVolumeSet.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnPassiveRetainVolumeSet.Click += new System.EventHandler(this.btnPassiveRetainVolumeSet_Click);
            // 
            // txtPassiveRetainVolume
            // 
            this.txtPassiveRetainVolume.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtPassiveRetainVolume.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtPassiveRetainVolume.Location = new System.Drawing.Point(32, 154);
            this.txtPassiveRetainVolume.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtPassiveRetainVolume.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtPassiveRetainVolume.Name = "txtPassiveRetainVolume";
            this.txtPassiveRetainVolume.Padding = new System.Windows.Forms.Padding(5);
            this.txtPassiveRetainVolume.ShowText = false;
            this.txtPassiveRetainVolume.Size = new System.Drawing.Size(150, 29);
            this.txtPassiveRetainVolume.TabIndex = 4;
            this.txtPassiveRetainVolume.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtPassiveRetainVolume.Watermark = "留样量 ml";
            // 
            // uiGroupBox5
            // 
            this.uiGroupBox5.Controls.Add(this.btnAllBottlesDrain);
            this.uiGroupBox5.Controls.Add(this.btnAllBottlesRinse);
            this.uiGroupBox5.Controls.Add(this.txtBottleNumber);
            this.uiGroupBox5.Controls.Add(this.btnSpecificBottleRinse);
            this.uiGroupBox5.Controls.Add(this.btnSpecificBottleDrain);
            this.uiGroupBox5.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiGroupBox5.Location = new System.Drawing.Point(4, 607);
            this.uiGroupBox5.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiGroupBox5.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiGroupBox5.Name = "uiGroupBox5";
            this.uiGroupBox5.Padding = new System.Windows.Forms.Padding(0, 32, 0, 0);
            this.uiGroupBox5.Size = new System.Drawing.Size(356, 284);
            this.uiGroupBox5.TabIndex = 1;
            this.uiGroupBox5.Text = "留样瓶控制";
            this.uiGroupBox5.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // btnAllBottlesDrain
            // 
            this.btnAllBottlesDrain.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnAllBottlesDrain.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.btnAllBottlesDrain.Location = new System.Drawing.Point(201, 43);
            this.btnAllBottlesDrain.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnAllBottlesDrain.Name = "btnAllBottlesDrain";
            this.btnAllBottlesDrain.Size = new System.Drawing.Size(128, 35);
            this.btnAllBottlesDrain.TabIndex = 1;
            this.btnAllBottlesDrain.Text = "全部留样瓶排空";
            this.btnAllBottlesDrain.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnAllBottlesDrain.Click += new System.EventHandler(this.btnAllBottlesDrain_Click);
            // 
            // btnAllBottlesRinse
            // 
            this.btnAllBottlesRinse.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnAllBottlesRinse.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.btnAllBottlesRinse.Location = new System.Drawing.Point(43, 43);
            this.btnAllBottlesRinse.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnAllBottlesRinse.Name = "btnAllBottlesRinse";
            this.btnAllBottlesRinse.Size = new System.Drawing.Size(128, 35);
            this.btnAllBottlesRinse.TabIndex = 0;
            this.btnAllBottlesRinse.Text = "全部留样瓶润洗";
            this.btnAllBottlesRinse.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnAllBottlesRinse.Click += new System.EventHandler(this.btnAllBottlesRinse_Click);
            // 
            // txtBottleNumber
            // 
            this.txtBottleNumber.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtBottleNumber.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.txtBottleNumber.Location = new System.Drawing.Point(43, 105);
            this.txtBottleNumber.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtBottleNumber.Maximum = 24D;
            this.txtBottleNumber.Minimum = 1D;
            this.txtBottleNumber.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtBottleNumber.Name = "txtBottleNumber";
            this.txtBottleNumber.Padding = new System.Windows.Forms.Padding(5);
            this.txtBottleNumber.ShowText = false;
            this.txtBottleNumber.Size = new System.Drawing.Size(128, 29);
            this.txtBottleNumber.TabIndex = 2;
            this.txtBottleNumber.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtBottleNumber.Watermark = "瓶号1-24";
            // 
            // btnSpecificBottleRinse
            // 
            this.btnSpecificBottleRinse.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnSpecificBottleRinse.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.btnSpecificBottleRinse.Location = new System.Drawing.Point(201, 220);
            this.btnSpecificBottleRinse.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnSpecificBottleRinse.Name = "btnSpecificBottleRinse";
            this.btnSpecificBottleRinse.Size = new System.Drawing.Size(128, 35);
            this.btnSpecificBottleRinse.TabIndex = 4;
            this.btnSpecificBottleRinse.Text = "指定瓶润洗";
            this.btnSpecificBottleRinse.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSpecificBottleRinse.Click += new System.EventHandler(this.btnSpecificBottleRinse_Click);
            // 
            // btnSpecificBottleDrain
            // 
            this.btnSpecificBottleDrain.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnSpecificBottleDrain.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.btnSpecificBottleDrain.Location = new System.Drawing.Point(201, 161);
            this.btnSpecificBottleDrain.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnSpecificBottleDrain.Name = "btnSpecificBottleDrain";
            this.btnSpecificBottleDrain.Size = new System.Drawing.Size(128, 35);
            this.btnSpecificBottleDrain.TabIndex = 3;
            this.btnSpecificBottleDrain.Text = "指定瓶排空";
            this.btnSpecificBottleDrain.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSpecificBottleDrain.Click += new System.EventHandler(this.btnSpecificBottleDrain_Click);
            // 
            // txtRetainVolume
            // 
            this.txtRetainVolume.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtRetainVolume.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtRetainVolume.Location = new System.Drawing.Point(32, 105);
            this.txtRetainVolume.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtRetainVolume.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtRetainVolume.Name = "txtRetainVolume";
            this.txtRetainVolume.Padding = new System.Windows.Forms.Padding(5);
            this.txtRetainVolume.ShowText = false;
            this.txtRetainVolume.Size = new System.Drawing.Size(150, 29);
            this.txtRetainVolume.TabIndex = 2;
            this.txtRetainVolume.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtRetainVolume.Watermark = "留样量 ml";
            // 
            // txtExceedWaitTime
            // 
            this.txtExceedWaitTime.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtExceedWaitTime.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtExceedWaitTime.Location = new System.Drawing.Point(32, 164);
            this.txtExceedWaitTime.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtExceedWaitTime.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtExceedWaitTime.Name = "txtExceedWaitTime";
            this.txtExceedWaitTime.Padding = new System.Windows.Forms.Padding(5);
            this.txtExceedWaitTime.ShowText = false;
            this.txtExceedWaitTime.Size = new System.Drawing.Size(150, 29);
            this.txtExceedWaitTime.TabIndex = 4;
            this.txtExceedWaitTime.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtExceedWaitTime.Watermark = "等待时间 分钟";
            // 
            // txtParallelSamples
            // 
            this.txtParallelSamples.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtParallelSamples.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtParallelSamples.Location = new System.Drawing.Point(32, 223);
            this.txtParallelSamples.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtParallelSamples.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtParallelSamples.Name = "txtParallelSamples";
            this.txtParallelSamples.Padding = new System.Windows.Forms.Padding(5);
            this.txtParallelSamples.ShowText = false;
            this.txtParallelSamples.Size = new System.Drawing.Size(150, 29);
            this.txtParallelSamples.TabIndex = 6;
            this.txtParallelSamples.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtParallelSamples.Watermark = "一次留几瓶";
            // 
            // cmbRetainMode
            // 
            this.cmbRetainMode.DataSource = null;
            this.cmbRetainMode.DropDownAutoWidth = true;
            this.cmbRetainMode.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbRetainMode.FillColor = System.Drawing.Color.White;
            this.cmbRetainMode.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cmbRetainMode.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbRetainMode.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbRetainMode.Location = new System.Drawing.Point(32, 46);
            this.cmbRetainMode.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbRetainMode.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbRetainMode.Name = "cmbRetainMode";
            this.cmbRetainMode.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbRetainMode.Size = new System.Drawing.Size(150, 29);
            this.cmbRetainMode.SymbolSize = 24;
            this.cmbRetainMode.TabIndex = 0;
            this.cmbRetainMode.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbRetainMode.Watermark = "";
            // 
            // btnStopAndReset
            // 
            this.btnStopAndReset.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnStopAndReset.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnStopAndReset.Location = new System.Drawing.Point(201, 269);
            this.btnStopAndReset.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnStopAndReset.Name = "btnStopAndReset";
            this.btnStopAndReset.Size = new System.Drawing.Size(128, 35);
            this.btnStopAndReset.TabIndex = 9;
            this.btnStopAndReset.Text = "复位排空";
            this.btnStopAndReset.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnStopAndReset.Click += new System.EventHandler(this.btnStopAndReset_Click);
            // 
            // btnTimeCalibrate
            // 
            this.btnTimeCalibrate.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnTimeCalibrate.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnTimeCalibrate.Location = new System.Drawing.Point(32, 269);
            this.btnTimeCalibrate.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnTimeCalibrate.Name = "btnTimeCalibrate";
            this.btnTimeCalibrate.Size = new System.Drawing.Size(128, 35);
            this.btnTimeCalibrate.TabIndex = 10;
            this.btnTimeCalibrate.Text = "校时";
            this.btnTimeCalibrate.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnTimeCalibrate.Click += new System.EventHandler(this.btnTimeCalibrate_Click);
            // 
            // cmbStartSignal
            // 
            this.cmbStartSignal.DataSource = null;
            this.cmbStartSignal.DropDownAutoWidth = true;
            this.cmbStartSignal.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbStartSignal.FillColor = System.Drawing.Color.White;
            this.cmbStartSignal.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cmbStartSignal.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbStartSignal.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbStartSignal.Location = new System.Drawing.Point(32, 36);
            this.cmbStartSignal.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbStartSignal.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbStartSignal.Name = "cmbStartSignal";
            this.cmbStartSignal.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbStartSignal.Size = new System.Drawing.Size(150, 29);
            this.cmbStartSignal.SymbolSize = 24;
            this.cmbStartSignal.TabIndex = 0;
            this.cmbStartSignal.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbStartSignal.Watermark = "";
            // 
            // txtInstantRetainVolume
            // 
            this.txtInstantRetainVolume.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtInstantRetainVolume.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtInstantRetainVolume.Location = new System.Drawing.Point(32, 111);
            this.txtInstantRetainVolume.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtInstantRetainVolume.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtInstantRetainVolume.Name = "txtInstantRetainVolume";
            this.txtInstantRetainVolume.Padding = new System.Windows.Forms.Padding(5);
            this.txtInstantRetainVolume.ShowText = false;
            this.txtInstantRetainVolume.Size = new System.Drawing.Size(150, 29);
            this.txtInstantRetainVolume.TabIndex = 2;
            this.txtInstantRetainVolume.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtInstantRetainVolume.Watermark = "留样量 ml";
            // 
            // txtSupplyTime
            // 
            this.txtSupplyTime.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtSupplyTime.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtSupplyTime.Location = new System.Drawing.Point(32, 111);
            this.txtSupplyTime.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtSupplyTime.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtSupplyTime.Name = "txtSupplyTime";
            this.txtSupplyTime.Padding = new System.Windows.Forms.Padding(5);
            this.txtSupplyTime.ShowText = false;
            this.txtSupplyTime.Size = new System.Drawing.Size(150, 29);
            this.txtSupplyTime.TabIndex = 2;
            this.txtSupplyTime.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtSupplyTime.Watermark = "供样时间 分钟";
            // 
            // cmbSupplyMode
            // 
            this.cmbSupplyMode.DataSource = null;
            this.cmbSupplyMode.DropDownAutoWidth = true;
            this.cmbSupplyMode.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbSupplyMode.FillColor = System.Drawing.Color.White;
            this.cmbSupplyMode.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cmbSupplyMode.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbSupplyMode.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbSupplyMode.Location = new System.Drawing.Point(32, 36);
            this.cmbSupplyMode.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbSupplyMode.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbSupplyMode.Name = "cmbSupplyMode";
            this.cmbSupplyMode.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbSupplyMode.Size = new System.Drawing.Size(150, 29);
            this.cmbSupplyMode.SymbolSize = 24;
            this.cmbSupplyMode.TabIndex = 0;
            this.cmbSupplyMode.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbSupplyMode.Watermark = "";
            // 
            // UC_HDZSCIXCDeviceOperControl
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.Controls.Add(this.gbFlowControl);
            this.Name = "UC_HDZSCIXCDeviceOperControl";
            this.Padding = new System.Windows.Forms.Padding(1);
            this.Size = new System.Drawing.Size(1616, 896);
            this.gbFlowControl.ResumeLayout(false);
            this.uiGroupBox6.ResumeLayout(false);
            this.uiGroupBox8.ResumeLayout(false);
            this.uiGroupBox7.ResumeLayout(false);
            this.uiGroupBox5.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private Sunny.UI.UIGroupBox gbFlowControl;
        private Sunny.UI.UIButton btnWaterSamplingA;
        private Sunny.UI.UIButton btnWaterSamplingB;
        private Sunny.UI.UIButton btnSupplyA;
        private Sunny.UI.UIButton btnSupplyB;
        private Sunny.UI.UIButton btnRetainA;
        private Sunny.UI.UIButton btnRetainB;
        private Sunny.UI.UIButton btnDrainA;
        private Sunny.UI.UIButton btnDrainB;
        private Sunny.UI.UIButton btnSupplyPipeDrain;
        private Sunny.UI.UIButton btnAllBottlesDrain;
        private Sunny.UI.UIButton btnAllBottlesRinse;
        private Sunny.UI.UIButton btnSpecificBottleDrain;
        private Sunny.UI.UIButton btnSpecificBottleRinse;
        private Sunny.UI.UITextBox txtBottleNumber;
        private Sunny.UI.UITextBox txtABSamplingVolume;
        private Sunny.UI.UIButton btnABSamplingVolumeSet;
        private Sunny.UI.UITextBox txtABSupplyTime;
        private Sunny.UI.UIButton btnABSupplyTimeSet;
        private Sunny.UI.UITextBox txtPassiveRetainVolume;
        private Sunny.UI.UIButton btnPassiveRetainVolumeSet;
        private Sunny.UI.UIGroupBox uiGroupBox6;
        private Sunny.UI.UIGroupBox uiGroupBox5;
        private Sunny.UI.UIGroupBox uiGroupBox8;
        private Sunny.UI.UIGroupBox uiGroupBox7;
        private Sunny.UI.UIButton btnTimeCalibrate;
        private Sunny.UI.UIButton btnStopAndReset;
        private Sunny.UI.UITextBox txtRetainVolume;
        private Sunny.UI.UITextBox txtExceedWaitTime;
        private Sunny.UI.UITextBox txtParallelSamples;
        private Sunny.UI.UIComboBox cmbRetainMode;
        private Sunny.UI.UIComboBox cmbStartSignal;
        private Sunny.UI.UITextBox txtInstantRetainVolume;
        private Sunny.UI.UITextBox txtSupplyTime;
        private Sunny.UI.UIComboBox cmbSupplyMode;
    }
}