﻿using System;
using System.Windows.Forms;
using Fpi.UI.Common.PC;
using Fpi.Util.EnumRelated;
using Fpi.WMS3000.Equipment.QCD3900;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    /// <summary>
    /// 单个器件状态显示、控制
    /// </summary>
    public partial class UC_OneQCD3900ElementControl : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 对应的器件类型
        /// </summary>
        private eQCD3900ElementType _elementType = 0;

        /// <summary>
        /// 对应设备
        /// </summary>
        private QCD3900Equip _device;

        private const string ErrorInfo = "— — —";

        #endregion

        #region 构造

        public UC_OneQCD3900ElementControl()
        {
            InitializeComponent();
        }

        public UC_OneQCD3900ElementControl(QCD3900Equip device, eQCD3900ElementType elementType) : this()
        {
            _device = device;
            _elementType = elementType;
            gbMain.Text = EnumOperate.GetEnumDesc(_elementType);
        }

        #endregion

        #region 公共方法

        internal void RefreshUI()
        {
            if(_device != null && _device.DeviceStateParams.ElementState.ContainsKey(_elementType))
            {
                lblState.Text = _device.DeviceStateParams.ElementState[_elementType] ? "开" : "关";
            }
            else
            {
                lblState.Text = ErrorInfo;
            }
        }

        #endregion

        #region 事件

        private void btnOpen_Click(object sender, EventArgs e)
        {
            try
            {
                if(FpiMessageBox.ShowQuestion($"确认打开器件{_elementType}？") == DialogResult.Yes)
                {
                    _device.ElementControl(_elementType, true);

                    FpiMessageBox.ShowInfo($"打开器件{_elementType}成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"打开器件{_elementType}出错:{ex.Message}");
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            try
            {
                if(FpiMessageBox.ShowQuestion($"确认关闭器件{_elementType}？") == DialogResult.Yes)
                {
                    _device.ElementControl(_elementType, false);

                    FpiMessageBox.ShowInfo($"关闭器件{_elementType}成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"关闭器件{_elementType}出错:{ex.Message}");
            }
        }

        #endregion
    }
}