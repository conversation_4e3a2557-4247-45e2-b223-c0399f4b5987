<?xml version="1.0"?>
<configuration>
	<!-- Register a section handler for the log4net section -->
	<configSections>
		<section name="log4net" type="System.Configuration.IgnoreSectionHandler"/>
	</configSections>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<probing privatePath="bin"/>
		</assemblyBinding>
	</runtime>
	<appSettings>
		<!-- To enable internal log4net logging specify the following appSettings key -->
		<!--<add key="log4net.Internal.Debug" value="true"/>-->
	</appSettings>

	<!--log4net的核心配置代码-->
	<log4net>
		<!--日志对象根设置。定义日志对象使用的appender,通过appeder-ref来注册-->
		<root>
			<!--(高) OFF > FATAL > ERROR > WARN > INFO > DEBUG > ALL (低) -->
			<level value="ALL"/>
			<!--自定义文本输出-->
			<appender-ref ref="PatternFileAppender"/>
			<!--自定义界面输出-->
			<appender-ref ref="PatternViewAppender"/>
			<!--自定义数据库输出-->
			<appender-ref ref="PatternDBAppender"/>
		</root>

		<!--自定义文本输出 配置-->
		<appender name="PatternFileAppender" type="Fpi.Log.PatternFileAppender, Fpi.Log">
			<!--文件名-->
			<file value="log\%date{yyyy-MM-dd}\%level\%property{logtype}.txt"/>
			<layout type="log4net.Layout.PatternLayout" value="[%date] %message%newline"/>
		</appender>
		<!--自定义界面输出 配置-->
		<appender name="PatternViewAppender" type="Fpi.Log.PatternViewAppender, Fpi.Log"/>
		<!--自定义数据库输出 配置-->
		<appender name="PatternDBAppender" type="Fpi.HB.Business.PatternDBAppender,Fpi.HB.Business"/>
	</log4net>
	<startup useLegacyV2RuntimeActivationPolicy="true">
		<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8"/>
	</startup>
</configuration>
