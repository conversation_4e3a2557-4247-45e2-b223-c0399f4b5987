﻿using System;
using System.Collections.Generic;
using Fpi.WMS3000.Equipment.QCD3900;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    public partial class UC_QCD3900ElementControl : UIUserControl
    {
        #region 字段属性

        private QCD3900Equip _device;

        private List<UC_OneQCD3900ElementControl> _ucElementControlList = new List<UC_OneQCD3900ElementControl>();

        #endregion

        #region 构造

        public UC_QCD3900ElementControl()
        {
            InitializeComponent();
        }

        #endregion

        #region 公共方法

        internal void SetTragetDevice(QCD3900Equip device)
        {
            _device = device;
            InitUI();
        }

        internal void RefreshUI()
        {
            foreach(var uc in _ucElementControlList)
            {
                uc.RefreshUI();
            }
        }

        #endregion

        #region 事件

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            RefreshUI();
        }

        #endregion

        #region 私有方法

        internal void InitUI()
        {
            if(_device != null)
            {
                pnlMain.Controls.Clear();

                foreach(eQCD3900ElementType item in Enum.GetValues(typeof(eQCD3900ElementType)))
                {
                    var uc = new UC_OneQCD3900ElementControl(_device, item);
                    _ucElementControlList.Add(uc);
                    pnlMain.Controls.Add(uc);
                }
            }
        }

        #endregion
    }
}