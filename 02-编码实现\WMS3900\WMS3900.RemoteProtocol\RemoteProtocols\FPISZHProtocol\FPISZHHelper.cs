﻿using System;
using System.Collections.Generic;
using System.Text;
using Fpi.Data.Config;
using Fpi.DB;
using Fpi.Json;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.WCS3900;
using Fpi.WMS3000.Remote.GJDBS.Config;

namespace Fpi.WMS3000.Remote.GJDBS.GJDBSDataFrame
{
    /// <summary>
    /// 聚光科技数智化水站数据传输协议-数据处理帮助类
    /// </summary>
    public static class FPISZHHelper
    {
        #region 五参数核查数据组装

        /// <summary>
        /// 五参数核查数据组装
        /// </summary>
        /// <param name="gjdbsDesc"></param>
        /// <param name="beginTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="checkType">核查数据类型</param>
        /// <param name="dataCount"></param>
        /// <param name="qnDatetime">命令编码</param>
        /// <returns></returns>
        public static List<GJDBSCommand> BuildFiveParamCheckData(GJDBSProtocolDesc gjdbsDesc,
            DateTime beginTime, DateTime endTime, eWCS3900DetailDataType checkType, eGetDataCount dataCount, string qnDatetime = "")
        {
            var cmdList = new List<GJDBSCommand>();
            var gjdbsSingleCfg = gjdbsDesc.GjdbsSingleCfg;

            if(gjdbsSingleCfg != null && gjdbsSingleCfg.PolNodes.GetCount() > 0)
            {
                #region 查询语句

                // 查询语句
                string sqlStr;

                // 取最新一组数据
                if(dataCount == eGetDataCount.最新一条数据)
                {
                    var selectTime = DateTime.MinValue;
                    sqlStr = $"select datatime from {DbConfig.FIVEPARAM_CHECK_DATA_TABLE} where datatime>='{beginTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatime<='{endTime.ToString(DbConfig.DATETIME_FORMAT)}' and checktype='{(int)checkType}' order by datatime Desc limit 0,1";

                    using(var reader = DbAccess.ExecuteQueryReturnDataReader(sqlStr))
                    {
                        while(reader.Read()) //没有符合条件的查询记录
                        {
                            selectTime = Convert.ToDateTime(reader["datatime"]);
                        }
                    }

                    if(selectTime != DateTime.MinValue)
                    {
                        sqlStr = $"select * from {DbConfig.FIVEPARAM_CHECK_DATA_TABLE} where datatime='{selectTime.ToString(DbConfig.DATETIME_FORMAT)}' and checktype='{(int)checkType}'";
                    }
                }
                // 按时间筛选数据
                else
                {
                    sqlStr = $"select * from {DbConfig.FIVEPARAM_CHECK_DATA_TABLE} where datatime>='{beginTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatime<='{endTime.ToString(DbConfig.DATETIME_FORMAT)}' and checktype='{(int)checkType}' order by datatime Desc";
                }

                #endregion

                if(!string.IsNullOrEmpty(sqlStr))
                {
                    using(var reader = DbAccess.ExecuteQueryReturnDataReader(sqlStr))
                    {
                        while(reader.Read()) //没有符合条件的查询记录
                        {
                            try
                            {
                                // 因子
                                eWCSNodeType nodetype = (eWCSNodeType)Convert.ToInt16(reader["nodetype"]);
                                var polid = nodetype.ToString();
                                // 核查数据
                                WCS3900CheckDataBase checkData = null;
                                string jsonData = Convert.ToString(reader["checkdata"]);
                                switch(nodetype)
                                {
                                    case eWCSNodeType.w01001:
                                        checkData = FpiJsonHelper.JsonToModel<WCS3900PHCheckData>(jsonData);
                                        break;
                                    case eWCSNodeType.w01014:
                                        checkData = FpiJsonHelper.JsonToModel<WCS3900ConduCheckData>(jsonData);
                                        break;
                                    case eWCSNodeType.w01009:
                                        checkData = FpiJsonHelper.JsonToModel<WCS3900OxyCheckData>(jsonData);
                                        break;
                                    case eWCSNodeType.w01003:
                                        checkData = FpiJsonHelper.JsonToModel<WCS3900TurbCheckData>(jsonData);
                                        break;
                                    case eWCSNodeType.w01010:
                                        checkData = FpiJsonHelper.JsonToModel<WCS3900TempCheckData>(jsonData);
                                        break;
                                }

                                GJDBSCommand gbCmd = new GJDBSCommand
                                {
                                    QN = DateTime.Now.ToString(GJDBSProtocolDesc.QNDateTimeFormat),
                                    ST = gjdbsDesc.ST,
                                    PW = gjdbsDesc.PW,
                                    MN = gjdbsDesc.MN,
                                    Flag = gjdbsSingleCfg.Flag,
                                    CP = new GJDBSCommandParameter()
                                };

                                if(!string.IsNullOrWhiteSpace(qnDatetime))
                                {
                                    gbCmd.QN = qnDatetime;
                                }

                                gbCmd.CP.DataTime = checkData.CheckTime.ToString(GJDBSProtocolDesc.DateTimeFormat);

                                var valueNode = DataManager.GetInstance().GetValueNodeById(polid);
                                if(valueNode == null || !(gjdbsSingleCfg.PolNodes.FindNode(polid) is GJDBSPolNode gjdbsPolNode))
                                {
                                    continue;
                                }

                                var polParameter = new GJDBSPolParameter
                                {
                                    PolId = gjdbsPolNode.GbID,
                                    Format = "F" + gjdbsPolNode.Format
                                };

                                switch(checkType)
                                {
                                    case eWCS3900DetailDataType.核查数据:
                                        gbCmd.CN = "2062";
                                        break;
                                    case eWCS3900DetailDataType.水样比对数据:
                                        gbCmd.CN = "2067";
                                        break;
                                }

                                polParameter.StandardValue = UnitManager.GetInstance()
                                    .TransFromSelfUnitValue(valueNode, checkData.CheckStandardValue, gjdbsPolNode.UnitID);
                                polParameter.Check = UnitManager.GetInstance()
                                    .TransFromSelfUnitValue(valueNode, checkData.CheckValue, gjdbsPolNode.UnitID);
                                polParameter.Flag = eValueNodeState.N.ToString();
                                gbCmd.CP.PolCmdParameter.Add(polParameter);

                                // 核查数据
                                cmdList.Add(gbCmd);

                                //// 溯源过程数据
                                //List<GJDBSCommand> detailList = GJDBSHelper.GetFiveParamDetailData(gjdbsDesc, nodetype, checkData, checkType);

                                //if(detailList != null || detailList.Count > 0)
                                //{
                                //    cmdList.AddRange(detailList);
                                //}
                            }
                            catch
                            {
                                // ignored
                            }
                        }
                    }
                }
            }

            foreach(var gjdbsCmd in cmdList)
            {
                GJDBSHelper.ChangeGbPolParameterFlag(gjdbsCmd);
            }

            return cmdList;
        }

        #endregion

        #region 核查数据组装

        /// <summary>
        /// 盲样测试数据组装
        /// </summary>
        /// <param name="beginTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="gjdbsDesc"></param>
        /// <param name="dataCount"></param>
        /// <returns></returns>
        public static List<GJDBSCommand> BuildBlindCheckData(GJDBSProtocolDesc gjdbsDesc, DateTime beginTime, DateTime endTime, eGetDataCount dataCount, string qnDatetime = "")
        {
            return GJDBSHelper.BuildCheckData(eCheckType.盲样测试.GetHashCode(), "2068", gjdbsDesc, beginTime, endTime, dataCount, qnDatetime);
        }

        /// <summary>
        /// 空白测试数据组装
        /// </summary>
        /// <param name="beginTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="gjdbsDesc"></param>
        /// <param name="dataCount"></param>
        /// <returns></returns>
        public static List<GJDBSCommand> BuildBlankTestData(GJDBSProtocolDesc gjdbsDesc, DateTime beginTime, DateTime endTime, eGetDataCount dataCount, string qnDatetime = "")
        {
            return GJDBSHelper.BuildCheckData(eCheckType.空白测试.GetHashCode(), "2069", gjdbsDesc, beginTime, endTime, dataCount, qnDatetime);
        }

        /// <summary>
        /// 任意浓度核查数据组装
        /// </summary>
        /// <param name="beginTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="gjdbsDesc"></param>
        /// <param name="dataCount"></param>
        /// <returns></returns>
        public static List<GJDBSCommand> BuildArbitraryCheckData(GJDBSProtocolDesc gjdbsDesc, DateTime beginTime, DateTime endTime, eGetDataCount dataCount, string qnDatetime = "")
        {
            return GJDBSHelper.BuildCheckData(eCheckType.任意浓度核查.GetHashCode(), "2069", gjdbsDesc, beginTime, endTime, dataCount, qnDatetime);
        }

        #endregion

        #region 多点线性数据

        /// <summary>
        /// 多点线性核查数据组装
        /// </summary>
        /// <param name="beginTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="endTime"></param>
        /// <param name="dataCount"></param>
        /// <returns></returns>
        public static List<GJDBSCommand> BuildMultiPointCheckData(GJDBSProtocolDesc gjdbsDesc, DateTime beginTime, DateTime endTime, eGetDataCount dataCount, string qnDatetime = "")
        {
            var cmdList = new List<GJDBSCommand>();
            var gjdbsSingleCfg = gjdbsDesc.GjdbsSingleCfg;

            if(gjdbsSingleCfg != null && gjdbsSingleCfg.PolNodes.GetCount() > 0)
            {
                #region 查询语句

                // 查询语句
                var sqlStr = new StringBuilder();
                // 取最新一组数据
                if(dataCount == eGetDataCount.最新一条数据)
                {
                    var selectTime = DateTime.MinValue;
                    var sqlSt = new StringBuilder("select datatime from ").Append(DbConfig.MULTPOINT_CHECK_DATA_TABLE)
                        .Append(" where datatime>='").Append(beginTime.ToString(DbConfig.DATETIME_FORMAT)).Append("' and datatime<='").Append(endTime.ToString(DbConfig.DATETIME_FORMAT))
                        .Append("' order by datatime Desc limit 0,1");
                    using(var reader = DbAccess.ExecuteQueryReturnDataReader(sqlSt.ToString()))
                    {
                        while(reader.Read()) //没有符合条件的查询记录
                        {
                            selectTime = Convert.ToDateTime(reader["datatime"]);
                        }
                    }

                    if(selectTime != DateTime.MinValue)
                    {
                        sqlStr.Append("select * from ").Append(DbConfig.MULTPOINT_CHECK_DATA_TABLE)
                            .Append(" where datatime='").Append(selectTime.ToString(DbConfig.DATETIME_FORMAT)).Append("'");
                    }
                }
                // 按时间筛选数据
                else
                {
                    sqlStr.Append("select * from ").Append(DbConfig.MULTPOINT_CHECK_DATA_TABLE)
                        .Append(" where datatime>='").Append(beginTime.ToString(DbConfig.DATETIME_FORMAT))
                        .Append("' and datatime<='").Append(endTime.ToString(DbConfig.DATETIME_FORMAT)).Append("' order by datatime Desc");
                }

                #endregion

                using(var reader = DbAccess.ExecuteQueryReturnDataReader(sqlStr.ToString()))
                {
                    while(reader.Read()) //没有符合条件的查询记录
                    {
                        try
                        {
                            var gbCmd = new GJDBSCommand
                            {
                                QN = DateTime.Now.ToString(GJDBSProtocolDesc.QNDateTimeFormat),
                                MN = gjdbsDesc.MN,
                                ST = gjdbsDesc.ST,
                                CN = "2071",
                                PW = gjdbsDesc.PW,
                                Flag = gjdbsSingleCfg.Flag,
                                CP = new GJDBSCommandParameter()
                            };
                            if(!string.IsNullOrWhiteSpace(qnDatetime))
                            {
                                gbCmd.QN = qnDatetime;
                            }

                            var recordTime = Convert.ToDateTime(reader["datatime"]);
                            gbCmd.CP.DataTime = recordTime.ToString(GJDBSProtocolDesc.DateTimeFormat);

                            // 因子ID
                            var polid = Convert.ToString(reader["polid"]);

                            var valueNode = DataManager.GetInstance().GetValueNodeById(polid);
                            if(valueNode == null || !(gjdbsSingleCfg.PolNodes.FindNode(polid) is GJDBSPolNode gjdbsPolNode))
                            {
                                continue;
                            }

                            var polParameter = new GJDBSPolParameter
                            {
                                PolId = gjdbsPolNode.GbID,
                                Format = "F" + gjdbsPolNode.Format
                            };

                            // A点核查时间
                            DateTime a_checktime = Convert.ToDateTime(reader["a_checktime"]);
                            polParameter.ACheckTime = a_checktime.ToString(GJDBSProtocolDesc.DateTimeFormat);
                            // A点标样浓度
                            float a_standard_value = Convert.ToSingle(reader["a_standard_value"]);
                            polParameter.AStandardValue = UnitManager.GetInstance()
                                .TransFromSelfUnitValue(valueNode, a_standard_value, gjdbsPolNode.UnitID);
                            // A点标样测量值
                            float a_check_value = Convert.ToSingle(reader["a_check_value"]);
                            polParameter.ACheckValue = UnitManager.GetInstance()
                                .TransFromSelfUnitValue(valueNode, a_check_value, gjdbsPolNode.UnitID);

                            // B点核查时间
                            DateTime b_checktime = Convert.ToDateTime(reader["b_checktime"]);
                            polParameter.BCheckTime = b_checktime.ToString(GJDBSProtocolDesc.DateTimeFormat);
                            // B点标样浓度
                            float b_standard_value = Convert.ToSingle(reader["b_standard_value"]);
                            polParameter.BStandardValue = UnitManager.GetInstance()
                                .TransFromSelfUnitValue(valueNode, b_standard_value, gjdbsPolNode.UnitID);
                            // B点标样测量值
                            float b_check_value = Convert.ToSingle(reader["b_check_value"]);
                            polParameter.BCheckValue = UnitManager.GetInstance()
                                .TransFromSelfUnitValue(valueNode, b_check_value, gjdbsPolNode.UnitID);

                            // C点核查时间
                            DateTime c_checktime = Convert.ToDateTime(reader["c_checktime"]);
                            polParameter.CCheckTime = c_checktime.ToString(GJDBSProtocolDesc.DateTimeFormat);
                            // C点标样浓度
                            float c_standard_value = Convert.ToSingle(reader["c_standard_value"]);
                            polParameter.CStandardValue = UnitManager.GetInstance()
                                .TransFromSelfUnitValue(valueNode, c_standard_value, gjdbsPolNode.UnitID);
                            // C点标样测量值
                            float c_check_value = Convert.ToSingle(reader["c_check_value"]);
                            polParameter.CCheckValue = UnitManager.GetInstance()
                                .TransFromSelfUnitValue(valueNode, c_check_value, gjdbsPolNode.UnitID);

                            // D点核查时间
                            DateTime d_checktime = Convert.ToDateTime(reader["d_checktime"]);
                            polParameter.DCheckTime = d_checktime.ToString(GJDBSProtocolDesc.DateTimeFormat);
                            // D点标样浓度
                            float d_standard_value = Convert.ToSingle(reader["d_standard_value"]);
                            polParameter.DStandardValue = UnitManager.GetInstance()
                                .TransFromSelfUnitValue(valueNode, d_standard_value, gjdbsPolNode.UnitID);
                            // D点标样测量值
                            float d_check_value = Convert.ToSingle(reader["d_check_value"]);
                            polParameter.DCheckValue = UnitManager.GetInstance()
                                .TransFromSelfUnitValue(valueNode, d_check_value, gjdbsPolNode.UnitID);

                            // 线性相关系数
                            polParameter.Coefficient = Convert.ToSingle(reader["r_coefficient"]);

                            gbCmd.CP.PolCmdParameter.Add(polParameter);

                            // 核查数据
                            cmdList.Add(gbCmd);
                        }
                        catch
                        {
                        }
                    }
                }
            }

            foreach(var gjdbsCmd in cmdList)
            {
                GJDBSHelper.ChangeGbPolParameterFlag(gjdbsCmd);
            }

            return cmdList;
        }

        #endregion
    }
}