﻿using Fpi.WMS3000.Equipment.SIA3900;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    public partial class UC_SIA3900CheckFluidInfo : UIUserControl
    {
        #region 字段属性

        private SIA3900Equipment _device;

        private const string ErrorInfo = "— — — — —";

        #endregion

        #region 构造

        public UC_SIA3900CheckFluidInfo()
        {
            InitializeComponent();
        }

        public UC_SIA3900CheckFluidInfo(SIA3900Equipment device) : this()
        {
            _device = device;
        }

        #endregion

        #region 公有方法

        internal void RefreshUI()
        {
            if(_device != null)
            {
                SIA3900CheckFluidInfo sIA3900CheckFluidInfo1 = _device.ReagentMaintenance.CheckFluidInfos[eSIA3900CheckFluidType.零点核查液];
                lblRemain1.Text = sIA3900CheckFluidInfo1.LiquidResidual == -1 ? ErrorInfo : sIA3900CheckFluidInfo1.LiquidResidual.ToString();
                lblTotal1.Text = sIA3900CheckFluidInfo1.LiquidTotal == -1 ? ErrorInfo : sIA3900CheckFluidInfo1.LiquidTotal.ToString();
                lblRemainDay1.Text = sIA3900CheckFluidInfo1.RemainDay == -1 ? ErrorInfo : sIA3900CheckFluidInfo1.RemainDay.ToString();
                lblGuaranteePeriod1.Text = sIA3900CheckFluidInfo1.GuaranteePeriod == -1 ? ErrorInfo : sIA3900CheckFluidInfo1.GuaranteePeriod.ToString();

                SIA3900CheckFluidInfo sIA3900CheckFluidInfo2 = _device.ReagentMaintenance.CheckFluidInfos[eSIA3900CheckFluidType.跨度核查液];
                lblRemain2.Text = sIA3900CheckFluidInfo2.LiquidResidual == -1 ? ErrorInfo : sIA3900CheckFluidInfo2.LiquidResidual.ToString();
                lblTotal2.Text = sIA3900CheckFluidInfo2.LiquidTotal == -1 ? ErrorInfo : sIA3900CheckFluidInfo2.LiquidTotal.ToString();
                lblRemainDay2.Text = sIA3900CheckFluidInfo2.RemainDay == -1 ? ErrorInfo : sIA3900CheckFluidInfo2.RemainDay.ToString();
                lblGuaranteePeriod2.Text = sIA3900CheckFluidInfo2.GuaranteePeriod == -1 ? ErrorInfo : sIA3900CheckFluidInfo2.GuaranteePeriod.ToString();
            }
        }

        #endregion    
    }
}