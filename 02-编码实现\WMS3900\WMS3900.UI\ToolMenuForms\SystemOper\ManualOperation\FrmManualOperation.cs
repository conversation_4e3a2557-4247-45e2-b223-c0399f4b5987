﻿using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Fpi.Operations.Config;
using Fpi.Operations.Exceptions;
using Fpi.Operations.Interfaces;
using Fpi.UI.Common.PC;
using Fpi.UI.PC.DockForms;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.SystemConfig;

namespace Fpi.WMS3000.UI
{
    public partial class FrmManualOperation : BaseWindow, IOperationListener
    {
        private bool hasInit = false;

        public FrmManualOperation()
        {
            InitializeComponent();
            this.dgvOperation.CellContentClick += dgOperation_CellContentClick;
        }

        private void FrmManualOperation_Load(object sender, EventArgs e)
        {
            if(!hasInit)
            {
                hasInit = true;
                InitdgOperation();
                OperationManager.GetInstance().ResetOperationListEvent += () => { InitdgOperation(); };
            }
        }

        private void InitdgOperation()
        {
            try
            {
                this.dgvOperation.Rows.Clear();

                foreach(int i in OperationManager.GetInstance().Operations.Cast<Operation>().Where(op => op.allowManual).Select(op => this.dgvOperation.Rows.Add(op.id, op, "空闲中", "执行")))
                {
                    this.dgvOperation.Rows[i].Height = 45;
                }
            }
            catch
            {
                // 防止保存流程时导致界面报错，使得流程保存失败
            }
        }

        private void dgOperation_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {

                if(e.ColumnIndex != 3)
                {
                    return;
                }

                if(SystemManager.GetInstance().CurSysRunMode != eSysRunMode.MaintainMode)
                {
                    throw new InvalidOperationException("当前系统运行模式不允许手动操作流程，请切换到维护模式后再试！");
                }

                string opStr = this.dgvOperation.Rows[e.RowIndex].Cells[3].Value.ToString();
                if(FpiMessageBox.ShowQuestion(@"是否确定" + opStr + @"当前操作?") == DialogResult.Yes)
                {
                    if(this.dgvOperation.Rows[e.RowIndex].Cells[1].Value is Operation op)
                    {
                        this.dgvOperation.Rows[e.RowIndex].Cells[4].Value = "";
                        if(this.dgvOperation.Rows[e.RowIndex].Cells[3].Value.ToString() == "执行")
                        {
                            this.dgvOperation.Rows[e.RowIndex].Cells[3].Value = "取消";
                            this.dgvOperation.Rows[e.RowIndex].Cells[2].Style.ForeColor = Color.Black;
                            this.dgvOperation.Rows[e.RowIndex].Cells[2].Value = "任务执行中";
                            OperationManager.GetInstance().StartDo(op.id, "self", true, null, this);

                            // 记录系统操作日志
                            SystemOpLogHelper.SaveLog(new SystemOpLogInfo($"启动流程操作[{op.name}]", eOpType.控制操作, eOpStyle.本地操作));
                        }
                        else if(this.dgvOperation.Rows[e.RowIndex].Cells[3].Value.ToString() == "取消")
                        {
                            OperationManager.GetInstance().Cancel(op.id, "self");

                            // 记录系统操作日志
                            SystemOpLogHelper.SaveLog(new SystemOpLogInfo($"取消流程操作[{op.name}]", eOpType.控制操作, eOpStyle.本地操作));
                        }
                    }
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"手动操作流程失败：{ex.Message}");
            }
        }

        #region IOperationListener 成员

        public void OnOperationDone(string operationId, string instrumentId, object result, Exception ex)
        {
            for(int i = 0; i < this.dgvOperation.Rows.Count; i++)
            {
                if(this.dgvOperation.Rows[i].Cells[0].Value.ToString() == operationId)
                {
                    if(ex != null)
                    {
                        if(ex is OperationCancelException)
                        {
                            this.dgvOperation.Rows[i].Cells[2].Value = "任务被取消";
                            this.dgvOperation.Rows[i].Cells[2].Style.ForeColor = Color.Orange;
                            this.dgvOperation.Rows[i].Cells[4].Value = "任务被取消或复位";
                        }
                        else
                        {
                            this.dgvOperation.Rows[i].Cells[2].Value = "任务异常中断";
                            this.dgvOperation.Rows[i].Cells[2].Style.ForeColor = Color.Red;
                            this.dgvOperation.Rows[i].Cells[4].Value = ex.Message;
                        }
                    }
                    else
                    {
                        this.dgvOperation.Rows[i].Cells[2].Value = "任务已完成";
                        this.dgvOperation.Rows[i].Cells[2].Style.ForeColor = Color.Green;
                        this.dgvOperation.Rows[i].Cells[4].Value = "";
                    }

                    this.dgvOperation.Rows[i].Cells[3].Value = "执行";
                }
            }
        }

        #endregion
    }
}