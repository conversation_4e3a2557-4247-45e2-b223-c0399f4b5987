﻿using System;
using Sunny.UI;

namespace Fpi.Util.Extensions
{
    /// <summary>
    /// 数据类型的拓展方法
    /// </summary>
    public static class DataExtension
    {
        private const string ErrorStr = "-";

        /// <summary>
        /// 整数打印
        /// </summary>
        /// <param name="message"></param>
        public static string ToDisplayFormat(this short value, string unit = "", string prefix = "")
        {
            return value == -1 ? ErrorStr : $"{prefix}{value}{unit}";
        }

        /// <summary>
        /// 整数打印
        /// </summary>
        /// <param name="message"></param>
        public static string ToDisplayFormat(this int value, string unit = "", string prefix = "")
        {
            return value == -1 ? ErrorStr : $"{prefix}{value}{unit}";
        }

        /// <summary>
        /// 整数打印
        /// </summary>
        /// <param name="message"></param>
        public static string ToDisplayFormat(this long value, string unit = "", string prefix = "")
        {
            return value == -1 ? ErrorStr : $"{prefix}{value}{unit}";
        }

        /// <summary>
        /// 浮点数打印
        /// </summary>
        /// <param name="message"></param>
        public static string ToDisplayFormat(this float value, string format = "F2", string unit = "", string prefix = "")
        {
            return float.IsNaN(value) ? ErrorStr : $"{prefix}{value.ToString(format)}{unit}";
        }

        /// <summary>
        /// 浮点数打印
        /// </summary>
        /// <param name="message"></param>
        public static string ToDisplayFormat(this float? value, string format = "F2", string unit = "", string prefix = "")
        {
            return value == null ? ErrorStr : ((float)value).ToDisplayFormat(format, unit, prefix);
        }

        /// <summary>
        /// 浮点数打印
        /// </summary>
        /// <param name="message"></param>
        public static string ToDisplayFormat(this double value, string format = "F2", string unit = "", string prefix = "")
        {
            return double.IsNaN(value) ? ErrorStr : $"{prefix}{value.ToString(format)}{unit}";
        }

        /// <summary>
        /// 浮点数打印
        /// </summary>
        /// <param name="message"></param>
        public static string ToDisplayFormat(this double? value, string format = "F2", string unit = "", string prefix = "")
        {
            return value == null ? ErrorStr : ((double)value).ToDisplayFormat(format, unit, prefix);
        }

        /// <summary>
        /// 时间打印,到秒
        /// </summary>
        /// <param name="message"></param>
        public static string ToDisplayTime(this DateTime time)
        {
            return time == DateTime.MinValue ? ErrorStr : time.ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// 日期打印，到日
        /// </summary>
        /// <param name="message"></param>
        public static string ToDisplayData(this DateTime time)
        {
            return time == DateTime.MinValue ? ErrorStr : time.ToString("yyyy-MM-dd");
        }

        /// <summary>
        /// 时间打印,到秒
        /// </summary>
        /// <param name="message"></param>
        public static string ToDisplayTime(this DateTime? time)
        {
            if(time != null)
            {
                return time == DateTime.MinValue ? ErrorStr : ((DateTime)time).ToString("yyyy-MM-dd HH:mm:ss");
            }
            else
            {
                return ErrorStr;
            }
        }

        /// <summary>
        /// 日期打印，到日
        /// </summary>
        /// <param name="message"></param>
        public static string ToDisplayData(this DateTime? time)
        {
            if(time != null)
            {
                return time == DateTime.MinValue ? ErrorStr : ((DateTime)time).ToString("yyyy-MM-dd");
            }
            else
            {
                return ErrorStr;
            }
        }

        /// <summary>
        /// 浮点数保留指定精度。默认3位小数。
        /// </summary>
        /// <param name="message"></param>
        public static float ToCalculationAccuracy(this float? value, int format = 3)
        {
            return value == null ? float.NaN : ((float)value).ToCalculationAccuracy(format);
        }

        /// <summary>
        /// 浮点数保留指定精度。默认3位小数。
        /// </summary>
        /// <param name="message"></param>
        public static float ToCalculationAccuracy(this float value, int format = 3)
        {
            return float.IsInfinity(value) ? float.NaN : (float)Math.Round(((float)value), format);
        }

        /// <summary>
        /// 浮点数保留指定精度。默认3位小数。
        /// </summary>
        /// <param name="message"></param>
        public static double ToCalculationAccuracy(this double? value, int format = 3)
        {
            return value == null ? double.NaN : ((double)value).ToCalculationAccuracy(format);
        }

        /// <summary>
        /// 浮点数保留指定精度。默认3位小数。
        /// </summary>
        /// <param name="message"></param>
        public static double ToCalculationAccuracy(this double value, int format = 3)
        {
            return double.IsInfinity((double)value) ? double.NaN : Math.Round(((double)value), format);
        }

    }
}