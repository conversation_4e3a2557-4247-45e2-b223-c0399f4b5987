﻿using System;
using Fpi.UI.Common.PC;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    public partial class UC_HDZSCIXCDeviceOperControl : UIUserControl
    {
        #region 字段属性

        private HDZSC_IXCSampleEquip _device;

        #endregion

        public UC_HDZSCIXCDeviceOperControl()
        {
            InitializeComponent();
        }

        #region 公共方法

        internal void SetTragetDevice(HDZSC_IXCSampleEquip device)
        {
            _device = device;
        }

        #endregion

        #region 流程控制

        #region 混采控制

        /// <summary>
        /// 采水量设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnABSamplingVolumeSet_Click(object sender, EventArgs e)
        {
            try
            {
                if(!int.TryParse(txtABSamplingVolume.Text, out int volume))
                {
                    throw new Exception("采水量输入不合法！");
                }
                _device.SetABSamplingVolume(volume);
                FpiMessageBox.ShowInfo($"采水量设置成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 供样时间设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnABSupplyTimeSet_Click(object sender, EventArgs e)
        {
            try
            {
                if(!int.TryParse(txtABSupplyTime.Text, out int time))
                {
                    throw new Exception("供样时间输入不合法！");
                }
                _device.SetABSupplyTime(time);
                FpiMessageBox.ShowInfo($"供样时间设置成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 留样量设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnPassiveRetainVolumeSet_Click(object sender, EventArgs e)
        {
            try
            {
                if(!int.TryParse(txtPassiveRetainVolume.Text, out int volume))
                {
                    throw new Exception("留样量输入不合法！");
                }
                _device.SetPassiveRetainVolume(volume);
                FpiMessageBox.ShowInfo($"留样量设置成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #region A/B桶操作

        /// <summary>
        /// A桶采水
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnWaterSamplingA_Click(object sender, EventArgs e)
        {
            try
            {
                _device.SetImmediateWaterSamplingA();
                FpiMessageBox.ShowInfo($"触发A桶采水成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// B桶采水
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnWaterSamplingB_Click(object sender, EventArgs e)
        {
            try
            {
                _device.SetImmediateWaterSamplingB();
                FpiMessageBox.ShowInfo($"触发B桶采水成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// A桶供样
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSupplyA_Click(object sender, EventArgs e)
        {
            try
            {
                _device.SetImmediateSupplyA();
                FpiMessageBox.ShowInfo($"触发A桶供样成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// B桶供样
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSupplyB_Click(object sender, EventArgs e)
        {
            try
            {
                _device.SetImmediateSupplyB();
                FpiMessageBox.ShowInfo($"触发B桶供样成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// A桶留样
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRetainA_Click(object sender, EventArgs e)
        {
            try
            {
                _device.SetImmediateRetainA();
                FpiMessageBox.ShowInfo($"触发A桶留样成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// B桶留样
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRetainB_Click(object sender, EventArgs e)
        {
            try
            {
                _device.SetImmediateRetainB();
                FpiMessageBox.ShowInfo($"触发B桶留样成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// A桶排空
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDrainA_Click(object sender, EventArgs e)
        {
            try
            {
                _device.SetBucketADrain();
                FpiMessageBox.ShowInfo($"触发A桶排空成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// B桶排空
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDrainB_Click(object sender, EventArgs e)
        {
            try
            {
                _device.SetBucketBDrain();
                FpiMessageBox.ShowInfo($"触发B桶排空成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        #endregion

        #region 留样瓶操作

        /// <summary>
        /// 全部留样瓶排空
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnAllBottlesDrain_Click(object sender, EventArgs e)
        {
            try
            {
                _device.SetAllBottlesDrain();
                FpiMessageBox.ShowInfo($"触发全部留样瓶排空成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 全部留样瓶润洗
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnAllBottlesRinse_Click(object sender, EventArgs e)
        {
            try
            {
                _device.SetAllBottlesRinse();
                FpiMessageBox.ShowInfo($"触发全部留样瓶润洗成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 指定留样瓶排空
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSpecificBottleDrain_Click(object sender, EventArgs e)
        {
            try
            {
                if(!int.TryParse(txtBottleNumber.Text, out int bottleNumber))
                {
                    throw new Exception("留样瓶号输入不合法！");
                }
                _device.SetSpecificBottleDrain(bottleNumber);
                FpiMessageBox.ShowInfo($"触发指定留样瓶排空成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 指定留样瓶润洗
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSpecificBottleRinse_Click(object sender, EventArgs e)
        {
            try
            {
                if(!int.TryParse(txtBottleNumber.Text, out int bottleNumber))
                {
                    throw new Exception("留样瓶号输入不合法！");
                }
                _device.SetSpecificBottleRinse(bottleNumber);
                FpiMessageBox.ShowInfo($"触发指定留样瓶润洗成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        #region 基础控制

        /// <summary>
        /// 供样管路排空
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSupplyPipeDrain_Click(object sender, EventArgs e)
        {
            try
            {
                _device.SetSupplyPipeDrain();
                FpiMessageBox.ShowInfo($"触发供样管路排空成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 复位排空
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnStopAndReset_Click(object sender, EventArgs e)
        {
            try
            {
                _device.SetImmediateStopAndReset();
                FpiMessageBox.ShowInfo($"触发复位排空成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 校时
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnTimeCalibrate_Click(object sender, EventArgs e)
        {
            try
            {
                _device.SetDeviceTime(DateTime.Now);
                FpiMessageBox.ShowInfo($"触发设备时间校准成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        #endregion
    }
}