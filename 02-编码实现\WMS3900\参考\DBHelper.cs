﻿//==================================================================================================
//类名：     DBHelper   
//创建人:    hongbing_mao
//创建时间:  2012-11-14 15:00:05
//
//修改人    修改时间    修改后版本              修改内容
//
//
//==================================================================================================
using System;
using System.Collections.Generic;
using System.Text;
using Fpi.Data.Config;
using Fpi.DB;
using System.Data;
using System.Collections;
using Fpi.DB.Manager;
using Fpi.DB.SqlUtil;
using Fpi.HB.Business.HisData;
using Fpi.Xml;
using Fpi.HB.Business.Protocols.GB.GBDataFrame;
using Fpi.Log;

namespace Fpi.HB.Business.DB
{
    public class DBHelper
    {/// <summary>
        /// 保存数据库测量项数据到数据库
        /// </summary>
        public static void SaveData(DateTime time)
        {
            if(DataManager.GetInstance().dataGroups != null)
            {
                foreach(DataGroup mp in DataManager.GetInstance().dataGroups)
                {
                    SaveData(DbCreator.TABLE_NAME_PRIX + mp.id, mp.GetAllVarNodes(), time);
                }
            }
            if(DataManager.GetInstance().varNodes != null && DataManager.GetInstance().varNodes.GetCount() > 0)
            {
                SaveData(DbCreator.TABLE_NAME_PRIX + "sys", DataManager.GetInstance().varNodes.List, time);
            }

        }
        //判断当前时间的数据有没有保存
        private static bool HasData(FpiTable table, DateTime time)
        {
            SearchConditionCollection condition = new SearchConditionCollection();
            condition.Add(new SearchCondition("datetime", new ColumnComparison(SqlOperator.Equal, time)));
            return table.ExistRecord(condition);
        }

        public static DateTime FormatTime(DateTime time)
        {
            if (time.Minute != 0)
            {
                time = time.AddMinutes(-time.Minute);
            }
            if (time.Second != 0)
            {
                time = time.AddSeconds(-time.Second);
            }
            return time;
        }

        /// <summary>
        /// 保存数据库测量项数据到数据库
        /// </summary>
        public static void SaveData(string tableName, ArrayList nodes, DateTime time)
        {
            if(nodes == null || nodes.Count < 1) return;
            FpiTable table = FpiDataBase.GetInstance().FindTableByName(tableName);
            if(table == null) return;
            if(HasData(table, time)) return;
            FpiRow row = new FpiRow();
            row.SetFieldValue("datetime", time);
            foreach(VarNode varNode in nodes)
            {
                if(varNode is ValueNode)
                {
                    float val = float.NaN;
                    val = (float)( varNode as ValueNode ).GetValue();
                    if(float.IsNaN(val) || float.IsInfinity(val))
                    {
                        row.SetFieldValue(DbCreator.COLUMN_NAME_PRIX + varNode.id, DBNull.Value);
                    }
                    else
                    {
                        row.SetFieldValue(DbCreator.COLUMN_NAME_PRIX + varNode.id, val);
                    }
                }
                else if(varNode is StateNode)
                {
                    bool savestat = false;
                    bool.TryParse(VarConfig.GetValue("SaveStatNode"), out savestat);
                    if(savestat)
                    {
                        int stat = 0;
                        if(( varNode as StateNode ).GetValue())
                        {
                            stat = 1;
                        }
                        row.SetFieldValue(DbCreator.COLUMN_NAME_PRIX + varNode.id, stat);
                    }
                }
            }
            table.AddRecord(row);
        }

        public static void SaveFlowData(string tableName,ValueNode node,ValueNode totalNode, DateTime time)
        {
          
            FpiTable table = FpiDataBase.GetInstance().FindTableByName(tableName);
            if (table == null) return;
            if (HasData(table, time)) return;
            FpiRow row = new FpiRow();
            row.SetFieldValue("datetime", time);
            float val = float.NaN;
            if (!double.IsNaN(node.GetValue()))
            {
                double flowValue = UnitManager.GetInstance().TransFromSelfUnitValue(node, (double)node.GetValue(), "l/s");
                val = (float)flowValue;
            }
            //添加流量值
            if (float.IsNaN(val) || float.IsInfinity(val))
            {
                row.SetFieldValue("flow", DBNull.Value);
            }
            else
            {
                row.SetFieldValue("flow", val);
            }
            //添加流量状态
            row.SetFieldValue("flow_flag", node.state);

            if (totalNode != null)
            {
                if (!double.IsNaN(node.GetValue()))
                {
                    val = (float)(totalNode.GetValue());
                }
                //添加累计流量值
                if (float.IsNaN(val) || float.IsInfinity(val))
                {
                    row.SetFieldValue("totalFlow", DBNull.Value);
                }
                else
                {
                    row.SetFieldValue("totalFlow", val);
                }
                //添加累计流量状态
                row.SetFieldValue("totalFlow_flag", totalNode.state);
            }
            table.AddRecord(row);
        }

        public static bool JudgeFlowMethod()
        {
            String flowFun = VarConfig.GetValue("FlowFun");
            if (String.IsNullOrEmpty(flowFun))
            {
                ValueNode totalFlowNode = null;
                foreach (VarNode node in DataManager.GetInstance().varNodes)
                {
                    if (node.id.ToUpper().Equals("B21"))
                    {
                        totalFlowNode = node as ValueNode;
                    }
                }

                //有累计流量的使用差减法
                if (totalFlowNode != null)
                {
                    if (!double.IsNaN(totalFlowNode.GetValue()))
                    {
                        return true;
                    }
                }

                //无累计流量使用累加法
                return false;
            }
            else
            {
                if (flowFun.Equals("Reduce"))
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
        }

        public static void SaveMinData(string tableName, ArrayList nodes, DateTime time)
        {
            if (nodes == null || nodes.Count < 1) return;
            FpiTable table = FpiDataBase.GetInstance().FindTableByName(tableName);
            if (table == null) return;
            if (HasData(table, time)) return;
            FpiRow row = new FpiRow();
            row.SetFieldValue("datetime", time);
            //流量数据单独处理
            if (JudgeFlowMethod())
            {
                //使用差减法
                String lastflowSql = "select totalFlow from fpi_sys_flow where datetime<= '" +
                                     ToDB_DateTimeFormat(time.AddMinutes(-1)) + "' order by datetime desc limit 0,1";

                String nextflowSql = "select totalFlow from fpi_sys_flow where datetime<= '" +
                                     ToDB_DateTimeFormat(time) + "' order by datetime desc limit 0,1";

                float lastTotalFlow = DbAccess.ExecuteQueryReturnFloatValue(lastflowSql);
                float nextTotalFlow = DbAccess.ExecuteQueryReturnFloatValue(nextflowSql);
                ValueNode b21Node = DataManager.GetInstance().GetVarNode("B21") as ValueNode;
           
                //添加值m^3 转成l/s
                row.SetFieldValue(DbCreator.COLUMN_NAME_PRIX + "B01", ( nextTotalFlow - lastTotalFlow )*1000/60);
                //添加状态
                ValueNode b01Node = DataManager.GetInstance().GetVarNode("B01") as ValueNode;
                row.SetFieldValue(DbCreator.COLUMN_NAME_PRIX + "B01" + DbCreator.POSTFIX, b01Node.state);

                //添加值
                row.SetFieldValue(DbCreator.COLUMN_NAME_PRIX + "B21", nextTotalFlow);
                //添加状态
                row.SetFieldValue(DbCreator.COLUMN_NAME_PRIX + "B21" + DbCreator.POSTFIX, b21Node.state);
            }
            else
            {
                //使用累加法
                float flowValue=0;
                String flowSql = "select flow from fpi_sys_flow where datetime> '" +
                                        ToDB_DateTimeFormat(time.AddMinutes(-1)) + "' and  datetime<= '" + ToDB_DateTimeFormat(time)+ "'  order by datetime desc";
                using (IDataReader reader = DbAccess.ExecuteQueryReturnDataReader(flowSql))
                {
                    while (reader.Read())
                    {
                        if (!(reader["flow"] is DBNull))
                        {
                            flowValue = flowValue + ( Convert.ToSingle(reader["flow"]));
                        }
                    }
                }
                flowValue = flowValue / 6;
                //添加值
                row.SetFieldValue(DbCreator.COLUMN_NAME_PRIX + "B01", flowValue);
                //添加状态
                ValueNode b01Node = DataManager.GetInstance().GetVarNode("B01") as ValueNode;
                row.SetFieldValue(DbCreator.COLUMN_NAME_PRIX + "B01" + DbCreator.POSTFIX, b01Node.state);

                String nextflowSql = "select totalFlow from fpi_sys_flow where datetime<= '" +
                                     ToDB_DateTimeFormat(time) + "' order by datetime desc limit 0,1";
                float nextTotalFlow = DbAccess.ExecuteQueryReturnFloatValue(nextflowSql);
                if (!float.IsNaN(nextTotalFlow))
                {
                    //添加值
                    row.SetFieldValue(DbCreator.COLUMN_NAME_PRIX + "B21", nextTotalFlow);
                    //添加状态
                    row.SetFieldValue(DbCreator.COLUMN_NAME_PRIX + "B21" + DbCreator.POSTFIX, b01Node.state);
                }
            }


            foreach (VarNode varNode in nodes)
            {
                if (varNode.id.ToUpper().Equals("B21") || varNode.id.ToUpper().Equals("B01"))
                {
                    continue;
                }
                //其他因子
                if (varNode is ValueNode)
                {
                    ValueNode valueNode = varNode as ValueNode;
                    QueryNode queryNode = GetQueryNodeById(valueNode.id);
                    float val = float.NaN;
                    if (queryNode != null)
                    {
                        val = (float)UnitManager.GetInstance().TransFromSelfUnitValue(valueNode, valueNode.GetValue(), queryNode.unitId);
                    }
                    //添加值
                    if (float.IsNaN(val) || float.IsInfinity(val))
                    {
                        row.SetFieldValue(DbCreator.COLUMN_NAME_PRIX + varNode.id, DBNull.Value);
                    }
                    else
                    {
                        row.SetFieldValue(DbCreator.COLUMN_NAME_PRIX + varNode.id, val);
                    }
                    //添加状态
                    row.SetFieldValue(DbCreator.COLUMN_NAME_PRIX + valueNode.id + DbCreator.POSTFIX, valueNode.state);
                }

            }
            table.AddRecord(row);
        }

        private static QueryNode GetQueryNodeById(string nodeId)
        {
            foreach (QueryGroup group in ReportManager.GetInstance().queryGroups)
            {
                foreach (QueryNode node in group.queryNodes)
                {
                    if (node.id == nodeId)
                    {
                        return node;
                    }
                }
            }
            return null;
        }
       




        private static void SaveHourData(string tableName, ArrayList nodes, DateTime time,bool useCycle)
        {
            try
            {
                String searchSql = "select * from fpi_sys_min where DateTime<='" + ToDB_DateTimeFormat(time) +
                                   "' and DateTime>'" + ToDB_DateTimeFormat(time.AddHours(-1)) + "'";
                //流量
                ValueNode flow = new ValueNode(); 
                //PH
                ValueNode ph = new ValueNode();
              
                //其他因子
                List<ValueNode> list = new List<ValueNode>(); 
                foreach (VarNode node in nodes)
                {
                    if (node is ValueNode)
                    {
                        ValueNode valueNode = node as ValueNode;
                        if (node.id.ToUpper().Equals("B01"))
                        {
                            flow = valueNode;
                        }
                        if (node.id.ToUpper().Equals("302"))
                        {
                            ph = valueNode;
                        }
                        if (!node.id.ToUpper().Equals("B01") && !node.id.ToUpper().Equals("302"))
                        {
                            list.Add(valueNode);
                        }
                    }
                }
                //查询条数
                int count = 0;
                double sumF = 0,sumTF=0;
                int countF = 0, countPH = 0;
                int countFD = 0;
                ph.SumValue = 0;
                ph.FlowValue = 0;
                ph.countD = 0;
                ph.countM = 0;
                ph.countC = 0;
                ph.countT = 0;
                ph.countN = 0;
                List<double> phList=new List<double>();
                //初始化
                foreach (ValueNode node in list)
                {
                    node.SumValue = 0;
                    node.FlowValue = 0;
                    node.TSumValue = 0;
                    node.AvgValue = 0;
                    node.countC = 0;
                    node.countD = 0;
                    node.countM = 0;
                    node.countT = 0;
                    node.countN = 0;
                    node.Tcount = 0;
                }
                using (IDataReader reader = DbAccess.ExecuteQueryReturnDataReader(searchSql))
                {
                    while (reader.Read())
                    {
                        if (!(reader["FB01"] is DBNull) && !(reader["FB01_flag"] is DBNull))
                        {
                            int flag = Convert.ToInt32(reader["FB01_flag"]);
                            
                            if (flag == 'N')
                            {
                                sumF = sumF + Convert.ToDouble(reader["FB01"]);
                                foreach (ValueNode node in list)
                                {
                                    if (!(reader["F" + node.id] is DBNull) &&Convert.ToInt32( reader["F" + node.id + "_flag"])!='C')
                                    {
                                        node.SumValue += Convert.ToDouble(reader["F" + node.id])*
                                                         Convert.ToDouble(reader["FB01"]);
                                        node.FlowValue = node.FlowValue + Convert.ToDouble(reader["FB01"]);
                                        node.countN++;

                                    }
                                
                                }
                                countF++;
                            }

                            if (flag == 'D')
                            {
                                countFD++;
                            }

                            sumTF = sumTF + Convert.ToDouble(reader["FB01"]);
                            foreach (ValueNode node in list)
                            {
                                if (!(reader["F" + node.id] is DBNull))
                                {
                                    node.TSumValue += Convert.ToDouble(reader["F" + node.id]);
                                    node.Tcount++;
                                }
                            }

                        }

                        if ( !(reader["F302_flag"] is DBNull))
                        {
                            int flag = Convert.ToInt32(reader["F302_flag"]);
                             if (flag == 'N'|| flag == 'O')
                             {
                                 countPH++;
                             }
                        }

                        foreach (ValueNode node in list)
                        {
                            if (!(reader["F" + node.id+"_flag"] is DBNull))
                            {
                                int flag = Convert.ToInt32(reader["F" + node.id + "_flag"]);
                                if (flag == 'D')
                                {
                                    node.countD++;
                                }
                                if (flag == 'M')
                                {
                                    node.countM++;
                                }
                                if (flag == 'C')
                                {
                                    node.countC++;
                                }
                                if (flag == 'T')
                                {
                                    node.countT++;
                                }
                            }
                        }

                        //PH值反算
                        //if (!(reader["F302"] is DBNull) && !(reader["FB01"] is DBNull))
                        //{
                        //    int flag = Convert.ToInt32(reader["F302_flag"]);
                        //    if (flag == 'D')
                        //    {
                        //        ph.countD++;
                        //    }
                        //    if (flag == 'M')
                        //    {
                        //        ph.countM++;
                        //    }
                        //    if (flag == 'C')
                        //    {
                        //        ph.countC++;
                        //    }
                        //    if (flag == 'T')
                        //    {
                        //        ph.countT++;
                        //    }
                        //    phList.Add(Convert.ToDouble(reader["F302"]));
                        //    int flowflag = Convert.ToInt32(reader["FB01_flag"]);
                        //    if (flowflag == 'N')
                        //    {
                        //        if (flag != 'C')
                        //        {
                        //            double phs = Convert.ToDouble(reader["F302"]);
                        //            double flows = Convert.ToDouble(reader["FB01"]);
                        //            if (phs < 7)
                        //            {
                        //                ph.SumValue += Math.Pow(10, -phs)*flows*60;
                        //            }
                        //            else
                        //            {
                        //                ph.SumValue -= Math.Pow(10, -(14 - phs))*flows*60;
                        //            }
                        //            ph.FlowValue += flows*60;
                        //        }
                        //    }
                        //}

                        //现场经常反馈PH计算存在问题，PH进行修正
                        if(!( reader["F302"] is DBNull ) && !( reader["FB01"] is DBNull ))
                        {
                            int flag = Convert.ToInt32(reader["F302_flag"]);

                            if(flag == 'D')
                            {
                                ph.countD++;
                            }
                            if(flag == 'M')
                            {
                                ph.countM++;
                            }
                            if(flag == 'C')
                            {
                                ph.countC++;
                            }
                            if(flag == 'T')
                            {
                                ph.countT++;
                            }
                            phList.Add(Convert.ToDouble(reader["F302"]));
                            //int flowflag = Convert.ToInt32(reader["FB01_flag"]);
                            //if(flowflag == 'N')
                            //{
                                if(flag != 'C')
                                {
                                    double phs = Convert.ToDouble(reader["F302"]);
                                    ph.SumValue += phs;
                                    ph.countN++;
                                }
                            //}
                        }


                        count++;
                    }
                }

                //流量均值与状态（单位m^3/h）
                QueryNode flowNode = GetQueryNodeById(flow.id);
                if (flowNode != null)
                {
                    flow.AvgValue = countF>0? sumF / countF * 60 * 60 / 1000f:0;
                }
                // N状态不小于45min的，小时流量标记为N，否则为D
                if (countF > 45)
                {
                    flow.AvgFlag = 'N';
                }
                else
                {
                    flow.AvgFlag = 'D';
                }
                // 整小时都是D的，全部累计，标记为D
                if (countFD == 60)
                {
                    flow.AvgValue = count>0? sumTF / count * 60:0;
                }

                
               

                //其他因子均值和标志
                foreach (ValueNode node in list)
                {
                    //判断数据是否取周期
                    if(useCycle)
                    {
                        //取当前数据
                        node.AvgValue = node.GetValue();
                        node.AvgFlag = 'N';
                    }
                    else
                    {
                        //加权平均数计算
                        node.AvgValue = node.SumValue / node.FlowValue;
                        if (node.countD >= 15)
                        {
                            node.AvgFlag = 'D';
                        }
                        else if (node.countM >= 15)
                        {
                            node.AvgFlag = 'M';
                        }
                        else if (node.countC >= 15)
                        {
                            node.AvgFlag = 'C';
                        }
                        else if (node.countT >= 15)
                        {
                            node.AvgFlag = 'T';
                        }
                        else
                        {
                            node.AvgFlag = 'N';
                        }

                        // 全时段流量状态为D的，按算术平均计算小时值
                        if (countFD == 60 || node.countC == 60)
                        {
                            node.AvgValue = node.Tcount > 0 ? node.TSumValue / node.Tcount : 0;
                        }
                        // 全时段流量为0的按算术平均计算，标记为F（仪器停运）。
                        else if (flow.AvgValue == 0)

                        {
                            node.AvgValue = node.Tcount > 0 ? node.TSumValue / node.Tcount : 0;
                            // node.AvgFlag = 'F';
                            node.AvgFlag = 'N';
                        }

                        //超标标识
                        if (node.AvgFlag == 'N')
                        {
                            if (node.IsOverAlarmLimit(node.AvgValue))
                            {
                                node.AvgFlag = 'O';
                            }
                        }
                    }
                }

         
                //PH
                // 流量整小时都是D的取 取中位值（7） 按实际标记状态
                if (ph.countD >= 15)
                {
                    ph.AvgFlag = 'D';
                }
                else if (ph.countM >= 15)
                {
                    ph.AvgFlag = 'M';
                }
                else if (ph.countC >= 15)
                {
                    ph.AvgFlag = 'C';
                }
                else if (ph.countT >= 15)
                {
                    ph.AvgFlag = 'T';
                }
                else
                {
                    ph.AvgFlag = 'N';
                }

                //if (countFD == 60)
                //{
                //    phList.Sort();
                //    if (phList.Count > 0)
                //    {
                //        if (phList.Count%2 == 0)
                //        {
                //            ph.AvgValue = (phList[phList.Count/2-1] + phList[phList.Count/2 ])/2;
                //        }
                //        else
                //        {
                //            ph.AvgValue = phList[phList.Count/2 ];
                //        }
                //    }
                //}
                //// 全时段流量为0的取 取中位值（7） ，标记为F（仪器停运）
                //else if (flow.AvgValue == 0)
                //    //if (!tmpDataList.Exists(a => a.FLOW != 0))
                //{
                //    phList.Sort();
                //    if (phList.Count > 0)
                //    {
                //        if (phList.Count % 2 == 0)
                //        {
                //            ph.AvgValue = (phList[phList.Count / 2-1] + phList[phList.Count / 2]) / 2;
                //        }
                //        else
                //        {
                //            ph.AvgValue = phList[phList.Count / 2 ];
                //        }
                //    }
                //    ph.AvgFlag = 'F';
                //}
                //else
                //{
                //    double avg = ph.FlowValue!=0? ph.SumValue / ph.FlowValue:0;
                //    if (avg == 0)
                //    {
                //        ph.AvgValue = 7;
                //    }
                //    else
                //    {

                //        ph.AvgValue = avg > 0 ? Math.Log10(1 / avg) : 14 - Math.Log10(-1 / avg);
                //    }
                //}

                //现场经常反馈PH计算存在问题，PH进行修正
                ph.AvgValue = ph.countN==0?0: ph.SumValue / ph.countN;


                //添加超标标识
                if(ph.AvgFlag == 'N')
                {
                    if (ph.IsOverAlarmLimit(ph.AvgValue))
                    {
                        ph.AvgFlag = 'O';
                    }
                }


                // 均值非ONF状态的或者整小时流量状态为D的，不计算小时排放量
                if (countFD != 60)
                {
                    // 小时排放量按照ONF状态的时均值 乘以 N状态累计流量*60/N的个数
                    foreach (ValueNode node in list)
                    {
                        if ((node.AvgFlag == 'N' || node.AvgFlag == 'O' || node.AvgFlag == 'F') && flow.AvgFlag == 'N')
                        {
                            node.LetValue = node.AvgValue * (flow.AvgValue) / 1000;
                        }
                        else
                        {
                              node.LetValue = double.NaN;
                        }
                    }
                }
                else
                {
                    foreach (ValueNode node in list)
                    {
                        node.LetValue = double.NaN;
                    }
                }

                String sql = "select count(*) from " + tableName + " where DateTime='" + time.AddHours(-1) + "'";
                int num = DbAccess.QueryRecordCount(sql);
                if (num == 0)
                {
                    //插入小时数据
                    String insertSql = "insert into " + tableName + "(DateTime,FB01,FB01_flag,F302,F302_flag,";
                    foreach (ValueNode node in list)
                    {
                        if (node.AvgFlag != 0 && node.AvgValue != 0 && !double.IsNaN(node.AvgValue))
                        {
                            insertSql += "F" + node.id + ",";
                            insertSql += "F" + node.id + "_flag,";
                            insertSql += "F" + node.id + "_let,";
                        }
                    }
                    insertSql = insertSql.TrimEnd(',');
                    insertSql += ") values('" + time.AddHours(-1) + "'," +(double.IsNaN( flow.AvgValue)?"NULL":flow.AvgValue.ToString()) + "," + flow.AvgFlag + "," + ph.AvgValue +
                                 "," + ph.AvgFlag + ",";
                    foreach (ValueNode node in list)
                    {
                        if (node.AvgFlag != 0 && node.AvgValue != 0&& !double.IsNaN(node.AvgValue))
                        {
                            insertSql += node.AvgValue + ",";
                            insertSql += node.AvgFlag + ",";
                            if (double.IsNaN(node.LetValue))
                            {
                                insertSql += "NULL,";
                            }
                            else
                            {
                                insertSql += node.LetValue + ",";
                            }
                        }
                    }
                    insertSql = insertSql.TrimEnd(',');
                    insertSql += ")";
                    LogUtil.Info("insertSql：" + insertSql);
                    DbAccess.ExecuteNonQuery(insertSql);
                }
            }
            catch (Exception ex)
            {
                throw new Exception("插入小时数据异常："+ex.Message);
            }
        }

    

        private static void SaveDayData(string tableName, ArrayList nodes, DateTime time)
        {
            try
            {

                String searchSql = "select * from fpi_sys_hour where DateTime<='" + ToDB_DateTimeFormat(time) +
                                   "' and DateTime>'" + ToDB_DateTimeFormat(time.AddDays(-1)) + "'";
                //流量因子
                ValueNode flow = new ValueNode();
                //PH
                ValueNode ph = new ValueNode(); 
                //其他因子均
                List<ValueNode> list = new List<ValueNode>(); 
                foreach (VarNode node in nodes)
                {
                    if (node is ValueNode)
                    {
                        ValueNode valueNode = node as ValueNode;
                        if (node.id.ToUpper().Equals("B01"))
                        {
                            flow = valueNode;
                        }
                        if (node.id.ToUpper().Equals("302"))
                        {
                            ph = valueNode;
                        }
                        if (!node.id.ToUpper().Equals("B01") && !node.id.ToUpper().Equals("302"))
                        {
                            list.Add(valueNode);
                        }
                    }
                }

                double sumDF = 0;
                int countDF = 0;

                ph.DSumValue = 0;
                ph.dCount = 0;
                ph.DFlowValue = 0;
                foreach (ValueNode node in list)
                {
                    node.DSumValue = 0;
                    node.dCount = 0;
                    node.DFlowValue = 0;
                }
                using (IDataReader reader = DbAccess.ExecuteQueryReturnDataReader(searchSql))
                {
                    while (reader.Read())
                    {
                        if (!(reader["FB01"] is DBNull) && !(reader["FB01_flag"] is DBNull))
                        {
                            int flag = Convert.ToInt32(reader["FB01_flag"]);
                            if (flag == 'N')
                            {
                                sumDF = sumDF + Convert.ToDouble(reader["FB01"]);
                                countDF++;
                            }

                            //if (!(reader["F302"] is DBNull) && !(reader["F302_flag"] is DBNull))
                            //{
                            //    int flowflag = Convert.ToInt32(reader["FB01_flag"]);
                            //    if (flowflag == 'N')
                            //    {
                            //        if (flag != 'C')
                            //        {
                            //            double phs = Convert.ToDouble(reader["F302"]);
                            //            double flows = Convert.ToDouble(reader["FB01"]);
                            //            if (phs < 7)
                            //            {
                            //                ph.DSumValue += Math.Pow(10, -phs) * flows * 60;
                            //            }
                            //            else
                            //            {
                            //                ph.DSumValue -= Math.Pow(10, -(14 - phs)) * flows * 60;
                            //            }
                            //            ph.DFlowValue += flows * 60;
                            //            ph.dCount++;
                            //        }
                            //    }
                            //}

                            //现场经常反馈PH计算存在问题，PH进行修正
                            if(!( reader["F302"] is DBNull ) && !( reader["F302_flag"] is DBNull ))
                            {
                                //int flowflag = Convert.ToInt32(reader["FB01_flag"]);
                                //if(flowflag == 'N')
                                //{
                                //    if(flag != 'C')
                                //    {
                                        double phs = Convert.ToDouble(reader["F302"]);
                                        ph.DFlowValue += phs;
                                        ph.dCount++;
                                //    }
                                //}
                            }

                            foreach(ValueNode node in list)
                            {
                                if (!(reader["F" + node.id + "_flag"] is DBNull))
                                {
                                    int nodeFlag = Convert.ToInt32(reader["F" + node.id + "_flag"]);
                                    if (flag == 'N' && (nodeFlag == 'N' || nodeFlag == 'O' || nodeFlag == 'F'))
                                    {
                                        node.DSumValue += Convert.ToDouble(reader["F" + node.id]) * Convert.ToDouble(reader["FB01"]);
                                        node.DFlowValue += Convert.ToDouble(reader["FB01"]);
                                        node.dCount++;
                                    }
                                }
                            }

                        }
                    }
                }

                // 日累计流量按照 N状态累计流量*24/N的个数
                // 日累计流量按照 N状态累计流量*24/N的个数
                if (countDF > 0)
                {
                    flow.AvgValue = countDF>0? sumDF / countDF * 24:0;
                    flow.AvgFlag = 'N';
                }
                else
                {
                    flow.AvgValue = 0;
                    flow.AvgFlag = 'D';
                }
                foreach (ValueNode node in list)
                {
                    // 选取同时满足监测因子N/O/F状态和流量N状态的小时，通过加权计算该因子日浓度均值。日排放量按照该日均值乘以 日累计流量
                    node.AvgValue = node.DFlowValue!=0? node.DSumValue / node.DFlowValue:0;
                    // 如选取小时数在运行小时数75%以上，则标记为有效N，否则标记为无效U
                    node.AvgFlag = node.dCount >= 18 ? 'N' : 'U';
                }
                //double avg = ph.DFlowValue!=0? ph.DSumValue / ph.DFlowValue:0;
                //if (avg == 0)
                //{
                //    ph.AvgValue = 7;
                //}
                //else
                //{
                //    ph.AvgValue = avg > 0 ? Math.Log10(1 / avg) : 14 - Math.Log10(-1 / avg);
                //}

                //现场经常反馈PH计算存在问题，PH进行修正
                ph.AvgValue = ph.dCount==0?0: ph.DFlowValue / ph.dCount;

                // 选取同时满足监测因子N/O/F状态和流量N状态的小时，通过加权计算该因子日浓度均值。日排放量按照该日均值乘以 日累计流量
                // 如选取小时数在运行小时数75%以上，则标记为有效N，否则标记为无效U
                ph.AvgFlag = ph.dCount >= 18 ? 'N' : 'U';

                // 日排放量按照该日均值乘以 日累计流量
                foreach (ValueNode node in list)
                {
                    node.LetValue = flow.AvgValue * node.AvgValue / 1000;
                }

                String sql = "select count(*) from " + tableName + " where DateTime='" + time.AddDays(-1) + "'";
                int num = DbAccess.QueryRecordCount(sql);
                if (num == 0)
                {
                    //插入日数据
                    String insertSql = "insert into " + tableName + "(DateTime,FB01,FB01_flag,F302,F302_flag,";
                    foreach (ValueNode node in list)
                    {
                        if (node.AvgFlag != 0 && node.AvgValue != 0&& !double.IsNaN(node.AvgValue))
                        {
                            insertSql += "F" + node.id + ",";
                            insertSql += "F" + node.id + "_flag,";
                            insertSql += "F" + node.id + "_let,";
                        }
                    }

                    insertSql = insertSql.TrimEnd(',');
                    insertSql += ") values('" + time.AddDays(-1) + "'," + flow.AvgValue + "," + flow.AvgFlag + "," + ph.AvgValue +
                                 "," + ph.AvgFlag + ",";
                    foreach (ValueNode node in list)
                    {
                        if (node.AvgFlag != 0 && node.AvgValue != 0 && !double.IsNaN(node.AvgValue))
                        {
                            insertSql += node.AvgValue + ",";
                            insertSql += node.AvgFlag + ",";
                            insertSql += node.LetValue + ",";
                        }
                    }
                    insertSql = insertSql.TrimEnd(',');
                    insertSql += ")";
                    LogUtil.Info("insertSql：" + insertSql);
                    DbAccess.ExecuteNonQuery(insertSql);
                }
            }
            catch (Exception ex)
            {
                throw new Exception("插入日数据异常：" + ex.Message);
            }
        }

        /// <summary>
        /// 获取因子最近数据库保存的值
        /// </summary>
        /// <param name="nodes"></param>
        /// <returns></returns>
        public static Dictionary<string, double> GetLatestValues(List<string> fullNodeIds)
        {
            Dictionary<string, double> values = new Dictionary<string, double>();
            Dictionary<string, List<DBNode>> tableList = new Dictionary<string, List<DBNode>>();
            foreach(string fullnodeid in fullNodeIds)
            {
                DBNode dbnode = new DBNode(fullnodeid);
                if(!tableList.ContainsKey(dbnode.TableName))
                {
                    tableList.Add(dbnode.TableName, new List<DBNode>());
                }

                List<DBNode> tableNodes = tableList[dbnode.TableName];
                tableNodes.Add(dbnode);
            }
            foreach(KeyValuePair<string, List<DBNode>> table in tableList)
            {
                FpiTable fpitable = FpiDataBase.GetInstance().FindTableByName(table.Key);
                if(fpitable == null) continue;
                FpiRow row = fpitable.GetFirstRow("datetime", OrderType.Desc);
                foreach(DBNode dbnode in table.Value)
                {
                    float dataValue;
                    try
                    {
                        dataValue = Convert.ToSingle(row.GetFieldValue(DbCreator.COLUMN_NAME_PRIX + dbnode.NodeId));
                    }
                    catch
                    {
                        dataValue = 0;
                    }
                    dbnode.Value = dataValue;

                    if(values.ContainsKey(dbnode.FullNodeID))
                    {
                        values[dbnode.FullNodeID] = dbnode.Value;
                    }
                    else
                    {
                        values.Add(dbnode.FullNodeID, dbnode.Value);
                    }
                }
            }
            return values;
        }

        public static Dictionary<string, double> GetValues(List<string> fullids, DateTime sTime, DateTime eTime)
        {
            Dictionary<string, double> values = new Dictionary<string, double>();
            string startTime = ToDB_DateTimeFormat(sTime);
            string endTime = ToDB_DateTimeFormat(eTime);
            Dictionary<string, List<DBNode>> tableList = new Dictionary<string, List<DBNode>>();
            foreach (string fullnodeid in fullids)
            {
                DBNode dbnode = new DBNode(fullnodeid);
                if(!tableList.ContainsKey(dbnode.TableName))
                {
                    tableList.Add(dbnode.TableName, new List<DBNode>());
                }
                List<DBNode> tableNodes = tableList[dbnode.TableName];
                tableNodes.Add(dbnode);
            }
            foreach (KeyValuePair<string, List<DBNode>> table in tableList)
            {
                StringBuilder sb = new StringBuilder("select ");
                foreach (DBNode dbnode in table.Value)
                {
                    string colname = DbCreator.COLUMN_NAME_PRIX + dbnode.NodeId;
                    sb.Append(colname).Append(",");
                }
                sb.Remove(sb.Length - 1, 1);
                sb.Append(" from fpi_sys");
                sb.Append(" where datetime>='").Append(startTime).Append("' and datetime<'").Append(endTime).Append("' order by datetime desc limit 0,1");
                float[] results = DbAccess.ExecuteQueryReturnFloatValues(sb.ToString());
                int colIndex = 0; 
                foreach (DBNode dbnode in table.Value)
                {
                    values.Add(dbnode.NodeId, results[colIndex]);
                    colIndex++;
                }
            }
            return values;
        }

        /// <summary>
        /// 获取因子历史统计值
        /// </summary>
        /// <param name="nodes"></param>
        /// <returns></returns>
        public static Dictionary<string, DBNode> GetStatValues(List<string> fullNodeIds, DateTime begin, DateTime end)//, AreaEnum area, string cn
        {
            string startTime = ToDB_DateTimeFormat(begin);
            string endTime = ToDB_DateTimeFormat(end);
            Dictionary<string, DBNode> values = new Dictionary<string, DBNode>();

            Dictionary<string, List<DBNode>> tableList = new Dictionary<string, List<DBNode>>();
            foreach(string fullnodeid in fullNodeIds)
            {
                DBNode dbnode = new DBNode(fullnodeid);
                if(!tableList.ContainsKey(dbnode.TableName))
                {
                    tableList.Add(dbnode.TableName, new List<DBNode>());
                }

                List<DBNode> tableNodes = tableList[dbnode.TableName];
                tableNodes.Add(dbnode);
            }
            foreach(KeyValuePair<string, List<DBNode>> table in tableList)
            {
                StringBuilder sb = new StringBuilder("select ");

                foreach(DBNode dbnode in table.Value)
                {
                    string colname = DbCreator.COLUMN_NAME_PRIX + dbnode.NodeId;
                    sb.Append("min(").Append(colname).Append("),");
                    sb.Append("max(").Append(colname).Append("),");
                    sb.Append("avg(").Append(colname).Append("),");
                }
                sb.Remove(sb.Length - 1, 1);
                sb.Append(" from ");
          
                sb.Append(table.Key);
                sb.Append(" where DateTime>='").Append(startTime).Append("' and DateTime<'").Append(endTime).Append("'");

                float[] results = DbAccess.ExecuteQueryReturnFloatValues(sb.ToString());
                int colIndex = 0;   //列索引
                foreach(DBNode dbnode in table.Value)
                {
                    dbnode.Min = results[colIndex++];
                    dbnode.Max = results[colIndex++];
                    dbnode.Avg = results[colIndex++];
                    //dbnode.Cou 统计怎么算？？？？？？
                    if(values.ContainsKey(dbnode.FullNodeID))
                    {
                        values[dbnode.FullNodeID] = dbnode;
                    }
                    else
                    {
                        values.Add(dbnode.FullNodeID, dbnode);
                    }
                }
            }
            return values;
        }
        public static string ToDB_DateTimeFormat(DateTime dateTime)
        {
            string DB_Format = "yyyy-MM-dd HH:mm:ss";
            return dateTime.ToString(DB_Format);
        }

        //by xiaoqing_ye 2014-07-15
        public static double GetCou(string nodeID, DateTime begin, DateTime end)
        {
            double result = double.NaN;
            string startTime = ToDB_DateTimeFormat(begin);
            string endTime = ToDB_DateTimeFormat(end);
            string colName = DbCreator.COLUMN_NAME_PRIX + nodeID;
            try
            {
                //获取后一个值               
                string sql = string.Format("select datetime,{0} from {1} where datetime <='{2}' order by datetime desc limit 0,1", colName, DbCreator.TABLE_NAME_PRIX + "sys", endTime);
                IDataReader reader = DbAccess.ExecuteQueryReturnDataReader(sql);
                double data1 = double.NaN;
                double data2 = double.NaN;
                if(reader.Read() && !( reader[colName] is DBNull ))
                    data1 = double.Parse(reader[colName].ToString());
                //获取前一个值  
                sql = string.Format("select datetime,{0} from {1} where datetime <='{2}' order by datetime desc limit 0,1", colName, DbCreator.TABLE_NAME_PRIX + "sys", startTime);
                reader = DbAccess.ExecuteQueryReturnDataReader(sql);
                if(reader.Read() && !( reader[colName] is DBNull ))
                    data2 = double.Parse(reader[colName].ToString());
                if(!double.IsNaN(data1) && !double.IsNaN(data2))
                    result = data1 - data2;
            }
            catch
            { }
            return result;
        }

        //by xiaoqing_ye 2014-08-13
        public static double GetAvg(string nodeID, DateTime begin, DateTime end)
        {
            double result = 0;
            string startTime = ToDB_DateTimeFormat(begin);
            string endTime = ToDB_DateTimeFormat(end);
            string colName = DbCreator.COLUMN_NAME_PRIX + nodeID;
            try
            {
                //获取后一个值               
                string sql = string.Format("select avg({0}) from {1} where DateTime>='{2}' and datetime <'{3}'", colName, DbCreator.TABLE_NAME_PRIX + "sys", startTime, endTime);
                float[] v = DbAccess.ExecuteQueryReturnFloatValues(sql);
                if(v.Length >= 1)
                    result = (double)v[0];
              
            }
            catch(Exception err)
            {
            }
            return result;
        }

        //by xiaoqing_ye 2015.04.21
        private static void GetLastTimeandValue(string nodeID, ref DateTime dt, ref double value)
        {
            dt = DateTime.MinValue;
            value = double.NaN;
            string colName = DbCreator.COLUMN_NAME_PRIX + nodeID;
            string sql = string.Format("select datetime,{0} from {1} order by datetime desc limit 0,1", colName, DbCreator.TABLE_NAME_PRIX + "sys");
            IDataReader reader = DbAccess.ExecuteQueryReturnDataReader(sql);
            double data1 = double.NaN;
            double data2 = double.NaN;
            if(reader.Read() && !( reader["datetime"] is DBNull ))
                dt = Convert.ToDateTime(reader["datetime"]);
            else
                dt = DateTime.MinValue;

            if(reader.Read() && !( reader[colName] is DBNull ))
                value = double.Parse(reader[colName].ToString());
            else
                value = double.Parse(reader[colName].ToString()); ;
        }

      

        public static void SaveHourData(DateTime dateTime,bool useCycle)
        {
            if (DataManager.GetInstance().GetAllValueNodes() != null && DataManager.GetInstance().GetAllValueNodes().Length > 0)
            {
                ArrayList list=new ArrayList();
                foreach (var item in DataManager.GetInstance().GetAllValueNodes())
                {
                    list.Add(item);
                }
                SaveHourData("fpi_sys_hour", list, dateTime, useCycle);
            }
        }

        public static bool HasHourData(DateTime dateTime)
        {
            String searchSql = "select count(*) from fpi_sys_hour where DateTime='" + ToDB_DateTimeFormat(dateTime) + "'";
            int cout = DbAccess.QueryRecordCount(searchSql);
            if (cout > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        public static bool HasDayData(DateTime dateTime)
        {
            String searchSql = "select count(*) from fpi_sys_day where DateTime='" + ToDB_DateTimeFormat(dateTime) + "'";
            int cout = DbAccess.QueryRecordCount(searchSql);
            if(cout > 0)
            {
                return true;
            }
            else
            {
                return false;
            }

        }


        public static void SaveDayData(DateTime dateTime)
        {
            if (DataManager.GetInstance().GetAllValueNodes() != null && DataManager.GetInstance().GetAllValueNodes().Length > 0)
            {
                ArrayList list = new ArrayList();
                foreach (var item in DataManager.GetInstance().GetAllValueNodes())
                {
                    list.Add(item);
                }
                SaveDayData("fpi_sys_day", list, dateTime);
            }
        }

      

        public static void SaveMinData(DateTime dateTime)
        {
            if (DataManager.GetInstance().GetAllValueNodes() != null && DataManager.GetInstance().GetAllValueNodes().Length > 0)
            {
                ArrayList list = new ArrayList();
                foreach (var item in DataManager.GetInstance().GetAllValueNodes())
                {
                    list.Add(item);
                }
                SaveMinData("fpi_sys_min", list, dateTime);
            }
        }

      
    }
}
