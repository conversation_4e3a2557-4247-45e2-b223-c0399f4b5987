﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{16BEBED9-A33D-4F44-AF70-46C01CB3F535}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>NLPutilsUI</RootNamespace>
    <AssemblyName>Fpi.NLPutilsUI</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AssistantHuman.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="AssistantHuman.Designer.cs">
      <DependentUpon>AssistantHuman.cs</DependentUpon>
    </Compile>
    <Compile Include="AssistantRobot.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="AssistantRobot.Designer.cs">
      <DependentUpon>AssistantRobot.cs</DependentUpon>
    </Compile>
    <Compile Include="CmdConfirmForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CmdConfirmForm.Designer.cs">
      <DependentUpon>CmdConfirmForm.cs</DependentUpon>
    </Compile>
    <Compile Include="SelectCmdForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SelectCmdForm.Designer.cs">
      <DependentUpon>SelectCmdForm.cs</DependentUpon>
    </Compile>
    <Compile Include="CmdSearchForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CmdSearchForm.Designer.cs">
      <DependentUpon>CmdSearchForm.cs</DependentUpon>
    </Compile>
    <Compile Include="CmdTemp.cs" />
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="InputParamForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="InputParamForm.Designer.cs">
      <DependentUpon>InputParamForm.cs</DependentUpon>
    </Compile>
    <Compile Include="NoFlickerPaner.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="SearchResultForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SearchResultForm.Designer.cs">
      <DependentUpon>SearchResultForm.cs</DependentUpon>
    </Compile>
    <Compile Include="UserControl1.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UserControl1.Designer.cs">
      <DependentUpon>UserControl1.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Service Include="{94E38DFF-614B-4cbd-B67C-F211BB35CE8B}" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="AssistantHuman.resx">
      <DependentUpon>AssistantHuman.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="AssistantRobot.resx">
      <DependentUpon>AssistantRobot.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CmdConfirmForm.resx">
      <DependentUpon>CmdConfirmForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SelectCmdForm.resx">
      <DependentUpon>SelectCmdForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CmdSearchForm.resx">
      <DependentUpon>CmdSearchForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="InputParamForm.resx">
      <DependentUpon>InputParamForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\AudioGif.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\InputEdite3.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\InputEdite2.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\cancel.png" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="BaseUI\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Fpi.Log\Fpi.Log.csproj">
      <Project>{c7c2425f-8926-43c6-996e-47205531c604}</Project>
      <Name>Fpi.Log</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>