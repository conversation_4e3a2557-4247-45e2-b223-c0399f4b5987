﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{B6F3E90E-1B45-4BD6-9387-DF10E744B0B8}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>NLPutils</RootNamespace>
    <AssemblyName>Fpi.NLPutils</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\Project\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>..\Project\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Fastenshtein">
      <HintPath>..\FpiDLL\Fastenshtein.dll</HintPath>
    </Reference>
    <Reference Include="NAudio">
      <HintPath>..\FpiDLL\NAudio.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Pinyin4net">
      <HintPath>..\FpiDLL\Pinyin4net.dll</HintPath>
    </Reference>
    <Reference Include="RestSharp">
      <HintPath>..\FpiDLL\RestSharp.dll</HintPath>
    </Reference>
    <Reference Include="SuperSocket.ClientEngine">
      <HintPath>..\FpiDLL\SuperSocket.ClientEngine.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="VoiceRecorder.Audio">
      <HintPath>..\FpiDLL\VoiceRecorder.Audio.dll</HintPath>
    </Reference>
    <Reference Include="WebSocket4Net">
      <HintPath>..\FpiDLL\WebSocket4Net.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Audio\AudioDurationCalculator.cs" />
    <Compile Include="Audio\AudioUtil.cs" />
    <Compile Include="Audio\VoiceData.cs" />
    <Compile Include="baidu\cloud\ASR.cs" />
    <Compile Include="Command\ScoreCommand.cs" />
    <Compile Include="Command\VoiceCommandManager.cs" />
    <Compile Include="Command\VoiceCommand.cs" />
    <Compile Include="ComTool.cs" />
    <Compile Include="Config\WorkTime.cs" />
    <Compile Include="Config\WorkState.cs" />
    <Compile Include="iflytek\Aikit\AikitProxyDll.cs" />
    <Compile Include="iflytek\Aikit\ASRaikitOffline.cs" />
    <Compile Include="iflytek\Aikit\FsaUtils.cs" />
    <Compile Include="iflytek\Aikit\IVWaiKit.cs" />
    <Compile Include="iflytek\Aikit\SpeechAikit.cs" />
    <Compile Include="iflytek\Aikit\TTSAikit.cs" />
    <Compile Include="iflytek\IflytekHelper.cs" />
    <Compile Include="iflytek\MSCutil\ASRmscOnline.cs" />
    <Compile Include="iflytek\MSCutil\ASRmscOffline.cs" />
    <Compile Include="iflytek\MSCutil\IVWmsc.cs" />
    <Compile Include="iflytek\MSCutil\MSCDLL.cs" />
    <Compile Include="iflytek\MSCutil\SpeechMsc.cs" />
    <Compile Include="iflytek\MSCutil\TTSmsc.cs" />
    <Compile Include="iflytek\XingHuo\ASRXingHuo.cs" />
    <Compile Include="NLPapi.cs" />
    <Compile Include="NPLinit.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="StringCommand.cs" />
    <Compile Include="NLPSetting.cs" />
    <Compile Include="VoiceUIManager.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="iflytek\Aikit\res\resource\cnenesr\e75f07b62_MLP_VAD_CN.bin_1.0.0.0" />
    <None Include="iflytek\Aikit\res\resource\cnenesr\e75f07b62_MLP_VAD_EN.bin_1.0.0.0" />
    <None Include="iflytek\Aikit\res\resource\cnenesr\e75f07b62_MLP_XN_CN.bin_1.0.0.0" />
    <None Include="iflytek\Aikit\res\resource\cnenesr\e75f07b62_MLP_XN_EN.bin_1.0.0.0" />
    <None Include="iflytek\Aikit\res\resource\cnenesr\e75f07b62_WFST_CN.bin_1.0.0.0" />
    <None Include="iflytek\Aikit\res\resource\cnenesr\e75f07b62_WFST_EN.bin_1.0.0.0" />
    <None Include="iflytek\Aikit\res\resource\ivw70\IVW_FILLER_1" />
    <None Include="iflytek\Aikit\res\resource\ivw70\IVW_GRAM_1" />
    <None Include="iflytek\Aikit\res\resource\ivw70\IVW_KEYWORD_1" />
    <None Include="iflytek\Aikit\res\resource\ivw70\IVW_MLP_1" />
    <None Include="iflytek\Aikit\res\resource\xtts10\e05d571cc_1.0.0_xTTS_CnCn_xiaoyan_2018_fix_arm.dat" />
    <None Include="iflytek\Aikit\res\resource\xtts10\e3fe94474_1.0.0_xTTS_CnCn_xiaoyan_2018_arm.irf" />
    <None Include="iflytek\Aikit\res\resource\xtts10\e4b08c6f3_1.0.0_xTTS_CnCn_xiaofeng_2018_fix_arm.dat" />
    <None Include="iflytek\Aikit\res\resource\xtts10\e4caee636_1.0.2_xTTS_CnCn_front_Emb_arm_2017.irf" />
    <None Include="iflytek\Aikit\res\resource\xtts10\ebdbd61ae_1.0.0_xTTS_CnCn_xiaofeng_2018_arm.irf" />
    <None Include="iflytek\Aikit\res\resource\xtts10\xTTS_EnUs_Catherine_2018_arm.irf_1.0.0" />
    <None Include="iflytek\Aikit\res\resource\xtts10\xTTS_EnUs_Catherine_2018_fix_arm.dat_1.0.0" />
    <None Include="iflytek\Aikit\res\resource\xtts10\xTTS_EnUs_front_arm.irf_1.0.0" />
    <None Include="iflytek\Aikit\res\resource\xtts10\xTTS_EnUs_John_2018_arm.irf_1.0.0" />
    <None Include="iflytek\Aikit\res\resource\xtts10\xTTS_EnUs_John_2018_fix_arm.dat_1.0.0" />
    <None Include="iflytek\callNum.bnf" />
    <None Include="iflytek\call.bnf" />
    <None Include="Config\Librarys.Config" />
    <None Include="msc\del.bat">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="msc\msc.cfg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="msc\res\asr\common.jet">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="msc\res\ivw\wakeupresource.jet">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="msc\res\tts\common.jet">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="msc\res\tts\xiaofeng.jet">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="msc\res\tts\xiaoyan.jet">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Config\LibraryManager.xml" />
    <Content Include="Config\LogConfig.xml" />
    <Content Include="Config\LogManager.xml" />
    <Content Include="Config\NLPSetting.xml" />
    <Content Include="Config\VoiceCommandManager.xml" />
    <Content Include="iflytek\Aikit\res\AEE_lib.dll" />
    <Content Include="iflytek\Aikit\res\AikitProxy.dll" />
    <Content Include="iflytek\Aikit\res\eabb2f029_v10092_aee.dll" />
    <Content Include="iflytek\Aikit\res\ebd1bade4_v1031_aee.dll" />
    <Content Include="iflytek\Aikit\res\ef7d69542_v1014_aee.dll" />
    <Content Include="iflytek\Aikit\res\resource\cnenesr\fsa\cn_fsa.txt" />
    <Content Include="iflytek\Aikit\res\resource\cnenesr\fsa\en_fsa.txt" />
    <Content Include="iflytek\Aikit\res\resource\ivw70\xbxb.txt" />
    <Content Include="msc\msc.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="msc\msc_x64.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="msc\res\asr\GrmBuilld\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Fpi.Assembly\Fpi.Assembly.csproj">
      <Project>{d5405f17-37c7-4cf0-a255-d6ee1afb11e0}</Project>
      <Name>Fpi.Assembly</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Log\Fpi.Log.csproj">
      <Project>{c7c2425f-8926-43c6-996e-47205531c604}</Project>
      <Name>Fpi.Log</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.NLPutilsUI\Fpi.NLPutilsUI.csproj">
      <Project>{16bebed9-a33d-4f44-af70-46c01cb3f535}</Project>
      <Name>Fpi.NLPutilsUI</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Util\Fpi.Util.csproj">
      <Project>{6e37d7b3-8d08-4ef3-a924-3b87982ab246}</Project>
      <Name>Fpi.Util</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Xml\Fpi.Xml.csproj">
      <Project>{3af9654d-39ee-4be9-8553-a9bb9b83a33b}</Project>
      <Name>Fpi.Xml</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>