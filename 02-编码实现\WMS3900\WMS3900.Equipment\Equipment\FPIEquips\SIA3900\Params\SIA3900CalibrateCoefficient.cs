﻿using System;
using System.ComponentModel;
using Fpi.Communication.Converter;
using Fpi.DB;
using Fpi.DB.Manager;
using Fpi.Devices;
using Fpi.Json;
using Fpi.WMS3000.DB;

namespace Fpi.WMS3000.Equipment.SIA3900
{
    /// <summary>
    /// 标定系数
    /// </summary>
    public class SIA3900CalibrateCoefficient
    {
        #region 属性字段

        /// <summary>
        /// 常规系数
        /// </summary>
        public RoutineCalibrateCoefficient RoutineCalibrateCoefficient { get; set; }

        /// <summary>
        /// 高指系数
        /// </summary>
        public GZCalibrateCoefficient GZCalibrateCoefficient { get; set; }

        /// <summary>
        /// 详细参数
        /// </summary>
        public SIA3900CalibrateCoefficientDetail CalibrateCoefficientDetail { get; set; }

        #endregion

        #region 公有方法

        public void UpdataValue(byte[] data, int startIndex, SIA3900Equipment device, byte[] datadetail)
        {
            if(data.Length < startIndex + 76)
            {
                throw new Exception("读取标定系数回应数据不完整！");
            }

            // 若是高指
            if(device.TypeDesc.Equals(eDeviceMeasureType.CODMn.ToString()))
            {
                // 解析最新数据时间
                DateTime dt = DataConvertHelper.GetDateTimeFromUnixTimeSeconds2143(data, startIndex + 40);
                // 软件初次运行，或者设备数据有更新
                if(dt != GZCalibrateCoefficient?.GZTimeStamp)
                {
                    // 解析标定系数
                    GZCalibrateCoefficient tempdata = new()
                    {
                        GZFlagType = (eSIA3900FlagType)DataConverter.GetInstance().ToInt32(data, startIndex + 36),
                        GZName = (eSIA3900GZNameType)DataConverter.GetInstance().ToInt32(data, startIndex + 38),
                        GZTimeStamp = DataConvertHelper.GetDateTimeFromUnixTimeSeconds2143(data, startIndex + 40),
                        GZRealTitrationNum = DataConvertHelper.ParseFloatValue2143(data, startIndex + 44),
                        GZCorrectTitrationNum = DataConvertHelper.ParseFloatValue2143(data, startIndex + 48),
                        GZTitrationVolume = DataConvertHelper.ParseFloatValue2143(data, startIndex + 52),
                        GZKValue = DataConvertHelper.ParseFloatValue2143(data, startIndex + 56),
                        GZV1 = DataConvertHelper.ParseFloatValue2143(data, startIndex + 60),
                        GZR1 = DataConvertHelper.ParseFloatValue2143(data, startIndex + 64),
                        GZV2 = DataConvertHelper.ParseFloatValue2143(data, startIndex + 68),
                        GZR2 = DataConvertHelper.ParseFloatValue2143(data, startIndex + 72)
                    };

                    // 赋值给当前对象
                    this.GZCalibrateCoefficient = tempdata;

                    // 保存到数据库中
                    SaveToDB(device, dt);
                }
            }
            else
            {
                // 解析最新数据时间
                DateTime dt = DataConvertHelper.GetDateTimeFromUnixTimeSeconds2143(data, startIndex + 6);
                // 软件初次运行，或者设备数据有更新
                if(dt != RoutineCalibrateCoefficient?.TimeStamp)
                {
                    // 解析标定系数
                    RoutineCalibrateCoefficient tempCalibrateData = new()
                    {
                        FlagType = (eSIA3900FlagType)DataConverter.GetInstance().ToInt32(data, startIndex),
                        Range = DataConvertHelper.ParseFloatValue2143(data, startIndex + 2),
                        TimeStamp = DataConvertHelper.GetDateTimeFromUnixTimeSeconds2143(data, startIndex + 6),
                        CalibrateType = (eSIA3900CalibrateType)DataConverter.GetInstance().ToInt32(data, startIndex + 10),
                        Order = DataConverter.GetInstance().ToInt32(data, startIndex + 12),

                        Param1 = DataConvertHelper.ParseFloatValue2143(data, startIndex + 14),
                        Param2 = DataConvertHelper.ParseFloatValue2143(data, startIndex + 18),
                        Param3 = DataConvertHelper.ParseFloatValue2143(data, startIndex + 22),
                        Param4 = DataConvertHelper.ParseFloatValue2143(data, startIndex + 26),

                        RR = DataConvertHelper.ParseFloatValue2143(data, startIndex + 30),
                        CalibrateCount = DataConverter.GetInstance().ToInt32(data, startIndex + 34)
                    };

                    // 解析标定系数详细区
                    SIA3900CalibrateCoefficientDetail tempCalibrateDetailData = new SIA3900CalibrateCoefficientDetail(tempCalibrateData.CalibrateCount);
                    tempCalibrateDetailData.UpdataValue(datadetail, 3);

                    // 赋值给当前对象
                    this.RoutineCalibrateCoefficient = tempCalibrateData;
                    this.CalibrateCoefficientDetail = tempCalibrateDetailData;

                    // 保存到数据库中
                    SaveToDB(device, dt);
                }
            }
        }

        #endregion

        #region 私有方法

        private static readonly object lockObj = new();

        /// <summary>
        /// 保存数据到数据库中
        /// </summary>   
        /// <param name="dt">新时间</param>
        /// <returns></returns>
        private void SaveToDB(SIA3900Equipment device, DateTime dt)
        {
            // 判断数据库中是否有相同时间、相同设备ID的数据
            string strSql = $"select count(*) from {DbConfig.DEVICE_CALIBRATECOEFFICIENT_TABLE} where datatime='{dt.ToString(DbConfig.DATETIME_FORMAT)}' and sourceid='{device.id}'";
            int recordCount = DbAccess.QueryRecordCount(strSql);
            if(recordCount == 0)
            {
                if(!string.IsNullOrWhiteSpace(device.id))
                {
                    WriteDeviceCalibrateCoefficientToDb(device.id, dt, device.TypeDesc, FpiJsonHelper.ModelToJson(this));
                }
            }
        }

        /// <summary>
        /// 写标定信息至数据库
        /// </summary>
        /// <param name="sourceId">设备ID</param>
        /// <param name="dt">数据时间</param>
        /// <param name="deviceType">设备类型</param>
        /// <param name="info">序列化信息</param>
        /// <exception cref="Exception"></exception>
        private void WriteDeviceCalibrateCoefficientToDb(string sourceId, DateTime dt, string deviceType, string info)
        {
            FpiTable table = FpiDataBase.GetInstance().FindTableByName(DbConfig.DEVICE_CALIBRATECOEFFICIENT_TABLE) ??
            throw new Exception("标定系数表不存在！");
            lock(lockObj)
            {
                FpiRow row = new();
                row.SetFieldValue("sourceid", sourceId);
                row.SetFieldValue("datatime", dt);
                row.SetFieldValue("calibratetype", deviceType);
                row.SetFieldValue("calibrateinfo", info);
                table.AddRecord(row);
            }
        }
    }

    #endregion

    /// <summary>
    /// 常规因子标定系数
    /// </summary>
    public class RoutineCalibrateCoefficient
    {
        #region 属性字段

        /// <summary>
        /// 标识
        /// </summary>
        [Description("标识")]
        public eSIA3900FlagType FlagType { get; set; } = (eSIA3900FlagType)(-1);

        /// <summary>
        /// 量程
        /// </summary>
        [Description("量程")]
        public float Range { get; set; } = float.NaN;

        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        public DateTime TimeStamp { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 标定类型
        /// </summary>
        [Description("标定类型")]
        public eSIA3900CalibrateType CalibrateType { get; set; } = (eSIA3900CalibrateType)(-1);

        /// <summary>
        /// 阶次
        /// </summary>
        [Description("阶次")]
        public int Order { get; set; } = -1;

        /// <summary>
        /// 参数1
        /// </summary>
        [Description("参数1")]
        public float Param1 { get; set; } = float.NaN;

        /// <summary>
        /// 参数2
        /// </summary>
        [Description("参数2")]
        public float Param2 { get; set; } = float.NaN;

        /// <summary>
        /// 参数3
        /// </summary>
        [Description("参数3")]
        public float Param3 { get; set; } = float.NaN;

        /// <summary>
        /// 参数4
        /// </summary>
        [Description("参数4")]
        public float Param4 { get; set; } = float.NaN;

        /// <summary>
        /// RR（线性相关系数）
        /// </summary>
        [Description("RR（线性相关系数）")]
        public float RR { get; set; } = float.NaN;

        /// <summary>
        /// 标定点数
        /// </summary>
        [Description("标定点数")]
        public int CalibrateCount { get; set; } = -1;

        #endregion

    }

    /// <summary>
    /// 高指标定系数
    /// </summary>
    public class GZCalibrateCoefficient
    {
        #region 属性字段

        /// <summary>
        /// 标识(高指)
        /// </summary>
        [Description("标识(高指)")]
        public eSIA3900FlagType GZFlagType { get; set; } = (eSIA3900FlagType)(-1);

        /// <summary>
        /// 名称(高指)
        /// </summary>
        [Description("名称(高指)")]
        public eSIA3900GZNameType GZName { get; set; } = (eSIA3900GZNameType)(-1);

        /// <summary>
        /// 时间戳(高指)
        /// </summary>
        [Description("时间戳(高指)")]
        public DateTime GZTimeStamp { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 实际滴定数(高指)
        /// </summary>
        [Description("实际滴定数(高指)")]
        public float GZRealTitrationNum { get; set; } = float.NaN;

        /// <summary>
        /// 修正滴定数(高指)
        /// </summary>
        [Description("修正滴定数(高指)")]
        public float GZCorrectTitrationNum { get; set; } = float.NaN;

        /// <summary>
        /// 滴定体积(高指)
        /// </summary>
        [Description("滴定体积(高指)")]
        public float GZTitrationVolume { get; set; } = float.NaN;

        /// <summary>
        /// K值(高指)
        /// </summary>
        [Description("K值(高指)")]
        public float GZKValue { get; set; } = float.NaN;

        /// <summary>
        /// V1(高指)
        /// </summary>
        [Description("V1(高指)")]
        public float GZV1 { get; set; } = float.NaN;

        /// <summary>
        /// R1(高指)
        /// </summary>
        [Description("R1(高指)")]
        public float GZR1 { get; set; } = float.NaN;

        /// <summary>
        /// V2(高指)
        /// </summary>
        [Description("V2(高指)")]
        public float GZV2 { get; set; } = float.NaN;

        /// <summary>
        /// R2(高指)
        /// </summary>
        [Description("R2(高指)")]
        public float GZR2 { get; set; } = float.NaN;

        #endregion

    }
}