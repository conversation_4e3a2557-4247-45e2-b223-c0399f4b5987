﻿using System;
using System.Linq;
using System.Windows.Forms;
using Fpi.UI.Common.PC;
using Fpi.WMS3000.Voice.Config;
using Fpi.Xml;
using Sunny.UI;

namespace Fpi.WMS3000.Voice.UI
{
    /// <summary>
    /// 语音控制命令配置界面
    /// </summary>
    public partial class FrmConfigVoiceCmd : UIForm
    {
        #region 字段属性

        private VoiceControlCmdManager _manager;

        #endregion

        #region 构造

        public FrmConfigVoiceCmd()
        {
            InitializeComponent();
            _manager = VoiceControlCmdManager.GetInstance().Clone() as VoiceControlCmdManager;
        }

        #endregion

        #region 事件

        private void FrmConfigVoiceCmd_Load(object sender, EventArgs e)
        {
            InitDgv();
            foreach(VoiceControlCmd item in _manager.GetAllVoiceControlCmd().OrderBy(x => x.id))
            {
                AddInView(dgvData, item);
            }
            chkAutoStart.Checked = _manager.AutoStart;
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            VoiceControlCmd voiceCmd = new();
            FrmEditVoiceCmd form = new FrmEditVoiceCmd(_manager, voiceCmd, eEditType.新增);
            if(form.ShowDialog() == DialogResult.OK)
            {
                AddInView(dgvData, form.VoiceOp);
                _manager.VoiceControlCmds.Add(form.VoiceOp);
            }
        }

        private void dgvData_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if(this.dgvData.SelectedRows.Count > 0 && dgvData.SelectedRows[0].Tag is VoiceControlCmd voiceCmd)
            {
                FrmEditVoiceCmd form = new FrmEditVoiceCmd(_manager, voiceCmd, eEditType.编辑);
                if(form.ShowDialog() == DialogResult.OK)
                {
                    UpdateDgv(form.VoiceOp);
                }
            }
        }

        private void btnUpdate_Click(object sender, EventArgs e)
        {
            if(dgvData.SelectedRows.Count <= 0)
            {
                FpiMessageBox.ShowError("未选中任何命令！");
                return;
            }

            if(this.dgvData.SelectedRows.Count > 0 && dgvData.SelectedRows[0].Tag is VoiceControlCmd voiceCmd)
            {
                FrmEditVoiceCmd form = new FrmEditVoiceCmd(_manager, voiceCmd, eEditType.编辑);
                if(form.ShowDialog() == DialogResult.OK)
                {
                    UpdateDgv(form.VoiceOp);
                }
            }
        }

        private void UpdateDgv(VoiceControlCmd voiceControl)
        {
            if(dgvData.SelectedRows.Count <= 0)
            {
                FpiMessageBox.ShowError("未选中任何命令！");
                return;
            }

            DataGridViewRow dr = dgvData.CurrentRow;
            dr.Tag = voiceControl;
            dr.Cells[0].Value = voiceControl.id;
            dr.Cells[1].Value = voiceControl.name;
            dr.Cells[2].Value = voiceControl.SubCmdList;
            dr.Cells[3].Value = voiceControl.GetOperationImpl().ToString();
        }

        private void btnDel_Click(object sender, EventArgs e)
        {
            if(dgvData.SelectedRows.Count <= 0)
            {
                FpiMessageBox.ShowError("未选中任何命令！");
                return;
            }
            DataGridViewRow dr = dgvData.CurrentRow;
            IdNameNode node = dr.Tag as IdNameNode;
            if(FpiMessageBox.ShowQuestion($"确定删除{node.name}命令？") == DialogResult.Yes)
            {
                _manager.VoiceControlCmds.Remove(node.id);
                // 更新删除后的显示
                foreach(DataGridViewRow row in dgvData.SelectedRows)
                {
                    if(!row.IsNewRow)
                    {
                        dgvData.Rows.Remove(row);
                    }
                }
            }
        }

        private void btnCopyAtom_Click(object sender, EventArgs e)
        {
            if(dgvData.SelectedRows.Count <= 0)
            {
                FpiMessageBox.ShowError("未选中任何命令！");
                return;
            }
            if(dgvData.CurrentRow.Tag is VoiceControlCmd voiceControlCmd)
            {
                FrmEditVoiceCmd form = new(_manager, voiceControlCmd.Clone() as VoiceControlCmd, eEditType.复制);
                if(form.ShowDialog() == DialogResult.OK)
                {
                    AddInView(dgvData, form.VoiceOp);
                    _manager.VoiceControlCmds.Add(form.VoiceOp);
                }
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                _manager.AutoStart = chkAutoStart.Checked;
                _manager.Save();
                _manager.Save(VoiceControlCmdManager.GetInstance());
                // 保存时重新初始化
                if(!chkAutoStart.Checked)
                {
                    VoiceControlCmdManager.GetInstance().StopVoiceServer();
                }

                // 更新指令集
                try
                {
                    if(chkAutoStart.Checked)
                    {
                        VoiceControlCmdManager.GetInstance().UpdateCommandList();
                    }
                }
                catch
                {
                }

                FpiMessageBox.ShowInfo("语音命令配置保存成功，重启软件生效!");
            }
            catch(Exception ex)
            {
                this.DialogResult = DialogResult.None;
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化表头显示
        /// </summary>
        private void InitDgv()
        {
            dgvData.ClearColumns();

            DataGridViewColumn col = dgvData.AddColumn("编号", "num");
            col.Width = 150;
            col.FillWeight = 150;
            col = dgvData.AddColumn("命名", "name");
            col.Width = 250;
            col.FillWeight = 250;
            col = dgvData.AddColumn("子命令", "subcmd");
            col.Width = 400;
            col.FillWeight = 400;
            col = dgvData.AddColumn("实现模板", "template");
            col.Width = 200;
            col.FillWeight = 200;

            dgvData.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
        }

        private void AddInView(UIDataGridView dgv, VoiceControlCmd voiceControl)
        {
            if(voiceControl == null) return;
            int index = dgv.Rows.Add();
            DataGridViewRow dr = dgv.Rows[index];
            dr.Tag = voiceControl;
            dr.Cells[0].Value = voiceControl.id;
            dr.Cells[1].Value = voiceControl.name;
            dr.Cells[2].Value = voiceControl.SubCmdList;
            dr.Cells[3].Value = voiceControl.GetOperationImpl().ToString();
        }

        #endregion
    }
}