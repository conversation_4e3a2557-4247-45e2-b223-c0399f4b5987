﻿using System;
using System.ComponentModel;
using Fpi.Data.Config;
using Newtonsoft.Json;

namespace Fpi.WMS3000.Equipment.Config
{
    /// <summary>
    /// 采水参数配置
    /// </summary>
    public class WaterCollectionConfig
    {
        #region 可存储属性

        /// <summary>
        /// 水泵运行电流上限 单位安
        /// </summary>
        [Description("水泵运行电流上限")]
        public float PumpCurrentUpperLimit { get; set; }

        /// <summary>
        /// 水泵运行电流下限 单位安
        /// </summary>
        [Description("水泵运行电流下限")]
        public float PumpCurrentLowerLimit { get; set; }

        /// <summary>
        /// 采水管堵塞压力下限 单位Kpa
        /// </summary>
        [Description("采水管堵塞压力下限")]
        public float WaterPipePluggingLowerLimit { get; set; }

        /// <summary>
        /// 采水正常压力下限 单位Kpa
        /// </summary>
        [Description("采水正常压力下限")]
        public float WaterPressureNormalLowerLimit { get; set; }

        /// <summary>
        /// 采水管爆管压力下限 单位Kpa
        /// </summary>
        [Description("采水管爆管压力下限")]
        public float WaterPipeBlastingLowerLimit { get; set; }

        /// <summary>
        /// 采水压力判断时间 单位秒
        /// </summary>
        [Description("采水压力判断时间")]
        public int PressureJudgeTime { get; set; }

        /// <summary>
        /// 五参数测量采水持续时间 单位秒
        /// </summary>
        [Description("五参数测量采水持续时间")]
        public int FiveParamSampleWaitingTime { get; set; }

        /// <summary>
        /// 沉砂池排水时间 单位秒
        /// </summary>
        [Description("沉砂池排水时间")]
        public int SandSinkDrainageTime { get; set; }

        /// <summary>
        /// 原水置换时间 单位秒
        /// </summary>
        [Description("原水置换时间")]
        public int WaterSubstitutionTime { get; set; }

        /// <summary>
        /// 采水持续时间 单位秒
        /// </summary>
        [Description("采水持续时间")]
        public int SampleWaitingTime { get; set; }

        /// <summary>
        /// 采水泵使用模式
        /// </summary>
        [Description("采水泵使用模式")]
        public ePumpUsingMode PumpUsingMode { get; set; }

        /// <summary>
        /// 1#采水泵因子编码
        /// </summary>
        [Description("1#采水泵因子编码")]
        public string Pump1NodeId { get; set; }

        /// <summary>
        /// 2#采水泵因子编码
        /// </summary>
        [Description("2#采水泵因子编码")]
        public string Pump2NodeId { get; set; }

        /// <summary>
        /// 1#进水球阀因子编码
        /// </summary>
        [Description("1#进水球阀因子编码")]
        public string Pump1ValveNodeId { get; set; }

        /// <summary>
        /// 2#进水球阀因子编码
        /// </summary>
        [Description("2#进水球阀因子编码")]
        public string Pump2ValveNodeId { get; set; }

        /// <summary>
        /// 沉砂池进水球阀因子编码
        /// </summary>
        [Description("沉砂池进水球阀因子编码")]
        public string SandSinkWaterInflowValveNodeId { get; set; }

        /// <summary>
        /// 沉砂池排水球阀因子编码
        /// </summary>
        [Description("沉砂池排水球阀因子编码")]
        public string SandSinkDrainageValveNodeId { get; set; }

        /// <summary>
        /// 采水深度因子编码
        /// </summary>
        [Description("采水深度因子编码")]
        public string WaterDepthNodeId { get; set; }

        /// <summary>
        /// 采水流量因子编码
        /// </summary>
        [Description("采水流量因子编码")]
        public string WaterFlowRateNodeId { get; set; }

        /// <summary>
        /// 采水压力因子编码
        /// </summary>
        [Description("采水压力因子编码")]
        public string WaterPressureNodeId { get; set; }

        /// <summary>
        /// 采水泵电流因子编码
        /// </summary>
        [Description("采水泵电流因子编码")]
        public string PumpCurrentNodeId { get; set; }

        /// <summary>
        /// 预处理浊度因子编码
        /// </summary>
        [Description("预处理浊度因子编码")]
        public string WaterTurbNodeId { get; set; }

        /// <summary>
        /// 沉砂池液位因子编码
        /// </summary>
        [Description("沉砂池液位因子编码")]
        public string SandSinkLiquidLevelNodeId { get; set; }

        /// <summary>
        /// 采水异常标志因子编码
        /// </summary>
        [Description("采水异常标志因子编码")]
        public string WaterAnomalyMarkNodeId { get; set; }

        /// <summary>
        /// 大周期采配水时触发五参数仪表测量
        /// </summary>
        [Description("大周期采配水时触发五参数仪表测量")]
        public bool FiveParamMeasure { get; set; } = true;

        #region 污染源专用

        /// <summary>
        /// 采样器采水等待时间 单位秒
        /// </summary>
        [Description("采样器采水等待时间")]
        public int SamplerSampleWaitingTime { get; set; }

        /// <summary>
        /// 偶数点A桶采样
        /// 为false时，偶数点B桶采样
        /// </summary>
        public bool EvenPointABucketSampling { get; set; }

        #endregion

        #endregion

        #region 无需存储属性

        /// <summary>
        /// 1#采水泵因子
        /// </summary>
        [Description("1#采水泵因子")]
        [JsonIgnore]
        public StateNode Pump1Node => DataManager.GetInstance().GetStateNodeById(Pump1NodeId);

        /// <summary>
        /// 2#采水泵因子
        /// </summary>
        [Description("2#采水泵因子")]
        [JsonIgnore]
        public StateNode Pump2Node => DataManager.GetInstance().GetStateNodeById(Pump2NodeId);

        /// <summary>
        /// 1#进水球阀因子
        /// </summary>
        [Description("1#进水球阀因子")]
        [JsonIgnore]
        public StateNode Pump1ValveNode => DataManager.GetInstance().GetStateNodeById(Pump1ValveNodeId);

        /// <summary>
        /// 2#进水球阀因子
        /// </summary>
        [Description("2#进水球阀因子")]
        [JsonIgnore]
        public StateNode Pump2ValveNode => DataManager.GetInstance().GetStateNodeById(Pump2ValveNodeId);

        /// <summary>
        /// 沉砂池进水球阀因子
        /// </summary>
        [Description("沉砂池进水球阀因子")]
        [JsonIgnore]
        public StateNode SandSinkWaterInflowValveNode => DataManager.GetInstance().GetStateNodeById(SandSinkWaterInflowValveNodeId);

        /// <summary>
        /// 沉砂池排水球阀因子
        /// </summary>
        [Description("沉砂池排水球阀因子")]
        [JsonIgnore]
        public StateNode SandSinkDrainageValveNode => DataManager.GetInstance().GetStateNodeById(SandSinkDrainageValveNodeId);

        /// <summary>
        /// 采水深度因子
        /// </summary>
        [Description("采水深度因子")]
        [JsonIgnore]
        public ValueNode WaterDepthNode => DataManager.GetInstance().GetValueNodeById(WaterDepthNodeId);

        /// <summary>
        /// 采水流量因子
        /// </summary>
        [Description("采水流量因子")]
        [JsonIgnore]
        public ValueNode WaterFlowRateNode => DataManager.GetInstance().GetValueNodeById(WaterFlowRateNodeId);

        /// <summary>
        /// 采水压力因子
        /// </summary>
        [Description("采水压力因子")]
        [JsonIgnore]
        public ValueNode WaterPressureNode => DataManager.GetInstance().GetValueNodeById(WaterPressureNodeId);

        /// <summary>
        /// 采水泵电流因子
        /// </summary>
        [Description("采水泵电流因子")]
        [JsonIgnore]
        public ValueNode PumpCurrentNode => DataManager.GetInstance().GetValueNodeById(PumpCurrentNodeId);

        /// <summary>
        /// 预处理浊度因子
        /// </summary>
        [Description("预处理浊度因子")]
        [JsonIgnore]
        public ValueNode WaterTurbNode => DataManager.GetInstance().GetValueNodeById(WaterTurbNodeId);

        /// <summary>
        /// 沉砂池液位因子
        /// </summary>
        [Description("沉砂池液位因子")]
        [JsonIgnore]
        public StateNode SandSinkLiquidLevelNode => DataManager.GetInstance().GetStateNodeById(SandSinkLiquidLevelNodeId);

        /// <summary>
        /// 采水异常标志因子
        /// </summary>
        [Description("采水异常标志因子")]
        [JsonIgnore]
        public StateNode WaterAnomalyMarkNode => DataManager.GetInstance().GetStateNodeById(WaterAnomalyMarkNodeId);

        #region 污染源专用

        /// <summary>
        /// 当前时间应使用采样桶
        /// </summary>
        [Description("当前时间应使用采样桶")]
        [JsonIgnore]
        public eSamplingBucket CurrentSampleBucketSet
        {
            get
            {
                return DateTime.Now.Hour % 2 == 0
                // 当前为偶数小时点
                    ? EvenPointABucketSampling ? eSamplingBucket.A桶 : eSamplingBucket.B桶
                // 当前为奇数小时点
                    : EvenPointABucketSampling ? eSamplingBucket.B桶 : eSamplingBucket.A桶;
            }
        }

        /// <summary>
        /// 当前时间应使用供样桶
        /// </summary>
        [Description("当前时间应使用供样桶")]
        [JsonIgnore]
        public eSamplingBucket CurrentSupplyBucketSet
        {
            get
            {
                return DateTime.Now.Hour % 2 == 0
                // 当前为偶数小时点
                   ? EvenPointABucketSampling ? eSamplingBucket.B桶 : eSamplingBucket.A桶
                // 当前为奇数小时点
                   : EvenPointABucketSampling ? eSamplingBucket.A桶 : eSamplingBucket.B桶;
            }
        }

        #endregion

        #endregion

        #region 公共方法

        /// <summary>
        /// 地表水运行参数检查
        /// </summary>
        public void Check()
        {
            if(PumpCurrentUpperLimit < PumpCurrentLowerLimit)
            {
                throw new Exception("水泵运行电流上限不可小于下限！");
            }

            if(WaterPipePluggingLowerLimit > WaterPressureNormalLowerLimit)
            {
                throw new Exception("采水管堵塞压力下限不可大于采水正常压力下限！");
            }

            if(WaterPressureNormalLowerLimit > WaterPipeBlastingLowerLimit)
            {
                throw new Exception("采水正常压力下限不可大于采水管爆管压力上限！");
            }

            if(WaterSubstitutionTime < PressureJudgeTime)
            {
                throw new Exception("原水置换时间不可小于采水压力判断时间！");
            }

            if(SampleWaitingTime < WaterSubstitutionTime)
            {
                throw new Exception("采水持续时间不可小于原水置换时间！");
            }

            if(FiveParamSampleWaitingTime < PressureJudgeTime)
            {
                throw new Exception("五参数测量采水持续时间不可小于采水压力判断时间！");
            }

            if(PumpUsingMode == ePumpUsingMode.只用泵一 || PumpUsingMode == ePumpUsingMode.双泵交替)
            {
                if(Pump1Node == null)
                {
                    throw new Exception("1#采水泵因子配置不合法！");
                }
                if(Pump1ValveNode == null)
                {
                    throw new Exception("1#进水球阀因子配置不合法！");
                }
            }

            if(PumpUsingMode == ePumpUsingMode.只用泵二 || PumpUsingMode == ePumpUsingMode.双泵交替)
            {
                if(Pump2Node == null)
                {
                    throw new Exception("2#采水泵因子配置不合法！");
                }
                if(Pump2ValveNode == null)
                {
                    throw new Exception("2#进水球阀因子配置不合法！");
                }
            }

            if(SandSinkWaterInflowValveNode == null)
            {
                throw new Exception("沉砂池进水球阀因子配置不合法！");
            }

            if(SandSinkDrainageValveNode == null)
            {
                throw new Exception("沉砂池排水球阀因子配置不合法！");
            }

            //if(WaterDepthNode == null)
            //{
            //    throw new Exception("采水深度因子配置不合法！");
            //}

            //if(WaterFlowRateNode == null)
            //{
            //    throw new Exception("采水流量因子配置不合法！");
            //}

            if(WaterPressureNode == null)
            {
                throw new Exception("采水压力因子配置不合法！");
            }

            if(WaterTurbNode == null)
            {
                throw new Exception("预处理浊度因子配置不合法！");
            }

            if(SandSinkLiquidLevelNode == null)
            {
                throw new Exception("沉砂池液位因子配置不合法！");
            }

            if(WaterAnomalyMarkNode == null)
            {
                throw new Exception("采水异常标志因子配置不合法！");
            }
        }

        /// <summary>
        /// 污染源运行参数检查
        /// </summary>
        public void PollutionCheck()
        {
            if(PumpCurrentUpperLimit < PumpCurrentLowerLimit)
            {
                throw new Exception("水泵运行电流上限不可小于下限！");
            }

            if(WaterPipePluggingLowerLimit > WaterPressureNormalLowerLimit)
            {
                throw new Exception("采水管堵塞压力下限不可大于采水正常压力下限！");
            }

            if(WaterPressureNormalLowerLimit > WaterPipeBlastingLowerLimit)
            {
                throw new Exception("采水正常压力下限不可大于采水管爆管压力上限！");
            }

            if(WaterSubstitutionTime < PressureJudgeTime)
            {
                throw new Exception("原水置换时间不可小于采水压力判断时间！");
            }

            if(SampleWaitingTime < WaterSubstitutionTime)
            {
                throw new Exception("采水持续时间不可小于原水置换时间！");
            }

            if(FiveParamSampleWaitingTime < PressureJudgeTime)
            {
                throw new Exception("五参数测量采水持续时间不可小于采水压力判断时间！");
            }

            if(PumpUsingMode == ePumpUsingMode.只用泵一 || PumpUsingMode == ePumpUsingMode.双泵交替)
            {
                if(Pump1Node == null)
                {
                    throw new Exception("1#采水泵因子配置不合法！");
                }
                if(Pump1ValveNode == null)
                {
                    throw new Exception("1#进水球阀因子配置不合法！");
                }
            }

            if(PumpUsingMode == ePumpUsingMode.只用泵二 || PumpUsingMode == ePumpUsingMode.双泵交替)
            {
                if(Pump2Node == null)
                {
                    throw new Exception("2#采水泵因子配置不合法！");
                }
                if(Pump2ValveNode == null)
                {
                    throw new Exception("2#进水球阀因子配置不合法！");
                }
            }

            if(SandSinkWaterInflowValveNode == null)
            {
                throw new Exception("沉砂池进水球阀因子配置不合法！");
            }

            if(SandSinkDrainageValveNode == null)
            {
                throw new Exception("沉砂池排水球阀因子配置不合法！");
            }

            //if(WaterDepthNode == null)
            //{
            //    throw new Exception("采水深度因子配置不合法！");
            //}

            //if(WaterFlowRateNode == null)
            //{
            //    throw new Exception("采水流量因子配置不合法！");
            //}

            if(WaterPressureNode == null)
            {
                throw new Exception("采水压力因子配置不合法！");
            }

            if(WaterTurbNode == null)
            {
                throw new Exception("预处理浊度因子配置不合法！");
            }

            if(SandSinkLiquidLevelNode == null)
            {
                throw new Exception("沉砂池液位因子配置不合法！");
            }

            if(WaterAnomalyMarkNode == null)
            {
                throw new Exception("采水异常标志因子配置不合法！");
            }
        }


        #endregion
    }
}