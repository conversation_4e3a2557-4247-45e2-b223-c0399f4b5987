﻿using System;
using Fpi.Data.Config;
using Fpi.UI.Common.PC;
using Fpi.Util.EnumRelated;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using Sunny.UI;

namespace Fpi.WMS3000.Pollution.UI.ExterDevices
{
    /// <summary>
    /// 混采参数配置
    /// </summary>
    public partial class FrmPollutionCollectionConfig : UIForm
    {
        #region 字段属性

        private const string DisplayFormat = "F2";

        #endregion

        #region 构造

        public FrmPollutionCollectionConfig()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void FrmPollutionCollectionConfig_Load(object sender, EventArgs e)
        {
            InitControl();

            LoadConfig();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                Check();

                Save();

                FpiMessageBox.ShowInfo($"保存混采参数配置成功！");

                // 记录系统操作日志
                SystemOpLogHelper.SaveLog(new SystemOpLogInfo($"修改混采参数配置", eOpType.配置操作, eOpStyle.本地操作));

                this.Close();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitControl()
        {
            var pumpNodeList = DataManager.GetInstance().GetAllSelectTypeStateNodes(eStateNodeType.Pump).ToArray();
            var valveNodeList = DataManager.GetInstance().GetAllSelectTypeStateNodes(eStateNodeType.Valve).ToArray();
            var signalNodeList = DataManager.GetInstance().GetAllSelectTypeStateNodes(eStateNodeType.Signal).ToArray();
            var flagNodeList = DataManager.GetInstance().GetAllSelectTypeStateNodes(eStateNodeType.Flag).ToArray();
            var valueNodeList = DataManager.GetInstance().GetAllValueNodes().ToArray();

            EnumOperate.BandEnumToCmb(cmbPumpUsingMode, typeof(ePumpUsingMode));

            cmbPump1Node.Items.AddRange(pumpNodeList);
            cmbPump2Node.Items.AddRange(pumpNodeList);
            cmbWaterPressureNode.Items.AddRange(valueNodeList);
            cmbPumpCurrentNode.Items.AddRange(valueNodeList);
            cmbWaterAnomalyMarkNode.Items.AddRange(flagNodeList);
        }

        /// <summary>
        /// 加载当前配置
        /// </summary>
        private void LoadConfig()
        {
            txtPumpCurrentUpperLimit.Text = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.PumpCurrentUpperLimit.ToString(DisplayFormat);
            txtPumpCurrentLowerLimit.Text = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.PumpCurrentLowerLimit.ToString(DisplayFormat);

            txtWaterPipePluggingLowerLimit.Text = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.WaterPipePluggingLowerLimit.ToString(DisplayFormat);
            txtWaterPressureNormalLowerLimit.Text = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.WaterPressureNormalLowerLimit.ToString(DisplayFormat);
            txtWaterPipeBlastingLowerLimit.Text = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.WaterPipeBlastingLowerLimit.ToString(DisplayFormat);
            txtPressureJudgeTime.Text = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.PressureJudgeTime.ToString();

            txtSamplerSampleWaitingTime.Text = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.SamplerSampleWaitingTime.ToString();
            txtSampleWaitingTime.Text = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.SampleWaitingTime.ToString();

            rdbEvenPointABucketSampling.Checked = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.EvenPointABucketSampling;
            rdbEvenPointBBucketSampling.Checked = !ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.EvenPointABucketSampling;

            cmbPumpUsingMode.SelectedValue = (int)ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.PumpUsingMode;

            cmbPump1Node.SelectedItem = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.Pump1Node;
            cmbPump2Node.SelectedItem = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.Pump2Node;

            cmbWaterPressureNode.SelectedItem = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.WaterPressureNode;
            cmbPumpCurrentNode.SelectedItem = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.PumpCurrentNode;
            cmbWaterAnomalyMarkNode.SelectedItem = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.WaterAnomalyMarkNode;
        }

        private void Check()
        {
            if(!float.TryParse(txtPumpCurrentUpperLimit.Text, out float pumpCurrentUpperLimit))
            {
                throw new Exception("水泵运行电流上限输入不合法！");
            }
            if(!float.TryParse(txtPumpCurrentLowerLimit.Text, out float pumpCurrentLowerLimit))
            {
                throw new Exception("水泵运行电流下限输入不合法！");
            }
            if(pumpCurrentUpperLimit < pumpCurrentLowerLimit)
            {
                throw new Exception("水泵运行电流上限不可小于下限！");
            }

            if(!float.TryParse(txtWaterPipePluggingLowerLimit.Text, out float waterPipePluggingLowerLimit))
            {
                throw new Exception("采水管堵塞压力下限输入不合法！");
            }
            if(!float.TryParse(txtWaterPressureNormalLowerLimit.Text, out float waterPressureNormalLowerLimit))
            {
                throw new Exception("采水管正常压力下限输入不合法！");
            }
            if(!float.TryParse(txtWaterPipeBlastingLowerLimit.Text, out float waterPipeBlastingLowerLimit))
            {
                throw new Exception("采水管爆管压力下限输入不合法！");
            }
            if(waterPipePluggingLowerLimit > waterPressureNormalLowerLimit)
            {
                throw new Exception("采水管堵塞压力下限不可大于采水正常压力下限！");
            }
            if(waterPressureNormalLowerLimit > waterPipeBlastingLowerLimit)
            {
                throw new Exception("采水正常压力下限不可大于采水管爆管压力上限！");
            }
            if(!uint.TryParse(txtPressureJudgeTime.Text, out uint pressureJudgeTime))
            {
                throw new Exception("采水压力判断时间输入不合法！");
            }

            if(!uint.TryParse(txtSamplerSampleWaitingTime.Text, out uint samplerSampleWaitingTime))
            {
                throw new Exception("采样器采水等待时间输入不合法！");
            }
            if(!uint.TryParse(txtSampleWaitingTime.Text, out uint sampleWaitingTime))
            {
                throw new Exception("采水持续时间输入不合法！");
            }
            if(sampleWaitingTime < samplerSampleWaitingTime)
            {
                throw new Exception("采水持续时间不可小于采样器采水等待时间！");
            }

            if(cmbPumpUsingMode.SelectedIndex == -1)
            {
                throw new Exception("请选择采水泵使用模式！");
            }

            if(cmbPump1Node.SelectedIndex == -1)
            {
                throw new Exception("请选择1#采水泵！");
            }
            if(cmbPump2Node.SelectedIndex == -1)
            {
                throw new Exception("请选择2#采水泵！");
            }

            if(cmbWaterPressureNode.SelectedIndex == -1)
            {
                throw new Exception("请选择采水压力因子！");
            }
            if(cmbPumpCurrentNode.SelectedIndex == -1)
            {
                throw new Exception("请选择水泵电流因子！");
            }
            if(cmbWaterAnomalyMarkNode.SelectedIndex == -1)
            {
                throw new Exception("请选择采水异常标志因子！");
            }
        }

        private void Save()
        {
            ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.PumpCurrentUpperLimit = float.Parse(txtPumpCurrentUpperLimit.Text);
            ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.PumpCurrentLowerLimit = float.Parse(txtPumpCurrentLowerLimit.Text);
            ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.WaterPipePluggingLowerLimit = float.Parse(txtWaterPipePluggingLowerLimit.Text);
            ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.WaterPressureNormalLowerLimit = float.Parse(txtWaterPressureNormalLowerLimit.Text);
            ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.WaterPipeBlastingLowerLimit = float.Parse(txtWaterPipeBlastingLowerLimit.Text);
            ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.PressureJudgeTime = int.Parse(txtPressureJudgeTime.Text);
            ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.SamplerSampleWaitingTime = int.Parse(txtSamplerSampleWaitingTime.Text);
            ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.SampleWaitingTime = int.Parse(txtSampleWaitingTime.Text);

            ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.EvenPointABucketSampling = rdbEvenPointABucketSampling.Checked;

            ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.PumpUsingMode = (ePumpUsingMode)(int)cmbPumpUsingMode.SelectedValue;

            ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.Pump1NodeId = (cmbPump1Node.SelectedItem as VarNode).id;
            ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.Pump2NodeId = (cmbPump2Node.SelectedItem as VarNode).id;

            ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.WaterPressureNodeId = (cmbWaterPressureNode.SelectedItem as VarNode).id;
            ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.PumpCurrentNodeId = (cmbPumpCurrentNode.SelectedItem as VarNode).id;
            ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.WaterAnomalyMarkNodeId = (cmbWaterAnomalyMarkNode.SelectedItem as VarNode).id;

            ExterEquipConfigManager.GetInstance().Save();
        }

        #endregion
    }
}