﻿using NLPutils.Audio;
using NLPutils.Command;
using NLPutils.Config;
using NLPutils.iflytek.Aikit;
using NLPutils.iflytek.MSCutil;
using NLPutils.iflytek.XingHuo;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Sockets;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace NLPutils.iflytek
{
    public class IflytekHelper
    {
        //private static bool IsAwaken = false;


        private static ScoreCommand needConfirmCmd = null;
        private static List<ScoreCommand> needSelectCmd = null;
        /// <summary>
        /// 语音合成语速
        /// </summary>
        public static int ttsSpeed = 50;
        /// <summary>
        /// 语音合成音量
        /// </summary>
        public static int ttsVolume = 100;

        public static void StartSpeechRecognition()
        {
            CopyDllFile();
            CheckConnect();
            //SpeechMsc.MSPLogin();

            ASRXingHuo.Init();
            SpeechAikit.Init();

            WorkTime.AwakenTimeOutEvent += WorkTime_AwakenTimeOutEvent;
            SpeechAikit.IVWawaken += IVWmsc_IVWawaken;
            IVWaiKit.Init();
            ASRaikitOffline.Init();
            AudioUtil.MicrophoneOpen();
            AudioUtil.MicrophoneData += AudioUtil_MicrophoneData;
        }

        public static void StopSpeechRecognition()
        {
            WorkTime.StopTask();
            ASRaikitOffline.UnInit();
            //SpeechMsc.MSPLogout();

            IVWaiKit.UnInit();
            SpeechAikit.UnInit();
            AudioUtil.MicrophoneClose();
            AudioUtil.MicrophoneData -= AudioUtil_MicrophoneData;
            WorkTime.AwakenTimeOutEvent -= WorkTime_AwakenTimeOutEvent;

        }

        private static void IVWmsc_IVWawaken()
        {
            needConfirmCmd = null;
            needSelectCmd = null;

            if(AudioUtil.IsPlaying)
            {
                AudioUtil.StopPlay();
            }


            WorkTime.SetAwakenTime(DateTime.Now);
            WorkState.SetWorkstate(WorkStateType.Awakening);//先设置时间，再设置状态
            WorkTime.StartTask();
            NLPapi.OnAwaken();

        }

        private static void AudioUtil_MicrophoneData(List<VoiceData> VoiceBuffer)
        {
            try
            {
                if(WorkState.GetWorkstate() == WorkStateType.Waiting)
                {
                    IVWaiKit.Aikit_Ivw_AudioData(VoiceBuffer);
                }
                else
                {
                    if(WorkState.GetWorkstate() == WorkStateType.Awakening)
                    {
                        IVWaiKit.Aikit_Ivw_AudioData(VoiceBuffer);
                    }

                    string asr = "";
                    int confidence = 0;
                    bool awsken = false;
                    if(IflytekHelper.IsXfonline)
                    {
                        asr = ASRXingHuo.RunASR(VoiceBuffer, out awsken, out confidence);//在线
                    }
                    else
                    {
                        asr = ASRaikitOffline.RunASR(VoiceBuffer, out awsken, out confidence);//离线
                    }

                    if(asr == "退出")
                    {
                        WorkTime.SetAwakenTime(DateTime.MinValue);
                    }

                    float.TryParse(NLPSetting.GetInstance().GetSetting("confidenceThreshold", "30"), out float confidenceThreshold);
                    if(confidence < 10)
                    {
                        return;
                    }
                    else if(confidence < confidenceThreshold)
                    {
                        asr = "";
                    }

                    if(string.IsNullOrEmpty(asr))
                    {
                        return;
                    }

                    if(awsken)
                    {
                        //命令词唤醒操作
                    }
                    else
                    {
                        switch(WorkState.GetWorkstate())
                        {
                            case WorkStateType.Awakening:
                                VoiceNormal(asr);
                                break;
                            case WorkStateType.Confirming:
                                VoiceConfirm(asr);
                                break;
                            case WorkStateType.Selecting:
                                VoiceSelecting(asr);
                                break;
                        }
                    }
                }
            }
            catch(Exception ex)
            {
                Fpi.Log.LogUtil.Debug("NLPutils", "AudioUtil_MicrophoneData" + ex.ToString());
            }
        }

        private static void VoiceNormal(string asr)
        {

            List<ScoreCommand> list = VoiceCommandManager.GetInstance().MatchCommandAndGroup(asr);
            ReturnCommand voiceCommand = null;

            if(list.Count > 0)
            {
                ScoreCommand scFrist = list[0];
                if(VoiceCommandManager.GetInstance().BeyondIdentifyThreshold(scFrist))
                {
                    if(list.Count > 1)
                    {
                        SelectCmd(list);
                    }
                    else
                    {
                        ScoreCommand scoreCommand = list[0];
                        if(scoreCommand.isNeedConfirm)
                        {
                            ConfirmCmd(scoreCommand);
                        }
                        else
                        {
                            voiceCommand = list[0].ReturnCmd;
                            VoiceUIManager.CommandEvent(voiceCommand);
                            NLPapi.OnCommand(voiceCommand);
                        }
                    }
                }
                else
                {
                    VoiceUIManager.NoCmdFind();
                }

            }
            WorkTime.SetAwakenTime(DateTime.Now);

            NLPapi.OnAsr(asr);
        }

        private static void SelectCmd(List<ScoreCommand> list)
        {
            string confirmStr = "共匹配:" + list.Count + "个命令,请问要执行第几个?";
            VoiceUIManager.ShowText(confirmStr);
            SpeakText(confirmStr);
            needSelectCmd = list;
            VoiceUIManager.ShowCmdSelectForm(list);
            WorkState.ChangeWorkstate(WorkStateType.Awakening, WorkStateType.Selecting);
        }


        private static void ConfirmCmd(ScoreCommand scoreCommand)
        {
            string action = scoreCommand.ReturnCmd.SubCmd.Replace("?", scoreCommand.ReturnCmd.Parameter);
            string confirmStr = "即将执行:" + action + ",确定还是取消?";
            VoiceUIManager.ShowText(confirmStr);
            SpeakText(confirmStr);
            needConfirmCmd = scoreCommand;
            VoiceUIManager.ShowCmdConfirmForm(scoreCommand);
            WorkState.ChangeWorkstate(WorkStateType.Awakening, WorkStateType.Confirming);
        }

        private static void VoiceConfirm(string asr)
        {
            int res = VoiceCommandManager.GetInstance().ConfirmCommand(asr);
            if(res == 1)
            {
                WorkState.ChangeWorkstate(WorkStateType.Confirming, WorkStateType.Awakening);
                ReturnCommand voiceCommand = needConfirmCmd.ReturnCmd;
                VoiceUIManager.CommandEvent(voiceCommand);
                VoiceUIManager.HideCmdSelectForm();

                NLPapi.OnCommand(voiceCommand);
            }
            else if(res == -1)
            {
                WorkState.ChangeWorkstate(WorkStateType.Confirming, WorkStateType.Awakening);
                VoiceUIManager.CancelCmdSelectForm();

                needConfirmCmd = null;
            }
            WorkTime.SetAwakenTime(DateTime.Now);
            NLPapi.OnAsr(asr);
        }
        private static void VoiceSelecting(string asr)
        {
            int res = VoiceCommandManager.GetInstance().SelectCommand(asr);
            if(res > 0)
            {
                if(res <= needSelectCmd.Count)
                {
                    WorkState.ChangeWorkstate(WorkStateType.Selecting, WorkStateType.Awakening);
                    ReturnCommand voiceCommand = needSelectCmd[res - 1].ReturnCmd;
                    VoiceUIManager.CommandEvent(voiceCommand);
                    VoiceUIManager.HideCmdSelectForm();
                    NLPapi.OnCommand(voiceCommand);
                }

            }
            else if(res == -1)
            {
                WorkState.ChangeWorkstate(WorkStateType.Selecting, WorkStateType.Awakening);
                needSelectCmd = null;
                VoiceUIManager.CancelCmdSelectForm();

            }
            WorkTime.SetAwakenTime(DateTime.Now);
            NLPapi.OnAsr(asr);
        }

        private static void WorkTime_AwakenTimeOutEvent()
        {
            if(AudioUtil.IsPlaying && WorkState.GetWorkstate() == WorkStateType.Awakening)
            {
                return;
            }
            WorkState.SetWorkstate(WorkStateType.Waiting);
            NLPapi.OnQuitAwaken();

        }


        public static void SpeakText(string text)
        {
            bool runLogin = false;

            byte[] wavBytes = TTSAikit.RunTTS(text);
            //File.WriteAllBytes("e:\\connect.wav", wavBytes);

            AudioUtil.PlayWavOneByOne(wavBytes);

        }

        public static bool IsXfonline = false;
        public static bool TestConnect()
        {
            try
            {
                TcpClient tcpClient = new TcpClient();
                string host = "open.xf-yun.com";
                int port = 80;
                // 使用异步连接
                IAsyncResult result = tcpClient.BeginConnect(host, port, null, null);

                // 等待连接完成或超时
                if(result.AsyncWaitHandle.WaitOne(1500, false))
                {
                    // 连接成功，调用 EndConnect 完成连接
                    tcpClient.EndConnect(result);
                    IsXfonline = true;
                    Fpi.Log.LogUtil.Debug("NLPutils", "TestConnect:online");
                    return true;
                }
                else
                {
                    // 超时，关闭连接并抛出异常
                    tcpClient.Close();
                    IsXfonline = false;
                    Fpi.Log.LogUtil.Debug("NLPutils", "TestConnect:offline");
                    return false;
                }
            }
            catch(Exception ex)
            {
                Fpi.Log.LogUtil.Debug("NLPutils", "TestConnect:" + ex.ToString());

                IsXfonline = false;
                return false;
            }

        }


        private static void CheckConnect()
        {
            string OnOrOffline = NLPSetting.GetInstance().GetSetting("OnOrOffline", "Auto");
            switch(OnOrOffline)
            {
                case "Online":
                    IsXfonline = true;
                    break;
                case "Offline":
                    IsXfonline = false;
                    break;
                case "Auto":
                    new Task(() =>
                    {
                        TestConnect();
                        Thread.Sleep(1000 * 60 * 3);//3分钟
                    }).Start();
                    break;
            }
        }


        internal static void SetVolume(int vo)
        {
            ttsVolume = vo;
        }

        public static void LogData(List<VoiceData> voiceBuffer, string xml_str, string reg_str, string num_str)
        {
            try
            {
                //string dirName = "ASRLog";
                //if(!Directory.Exists(dirName))
                //{
                //    Directory.CreateDirectory(dirName);
                //}

                //string fileName = DateTime.Now.ToString("yyyy_MM_dd_HH_mm_ss_fff");
                //using(FileStream fileStream = File.Create(dirName + "\\" + fileName + ".pcm"))
                //{
                //    foreach(VoiceData voiceData in voiceBuffer)
                //    {
                //        fileStream.Write(voiceData.data, 0, voiceData.data.Length);
                //    }
                //}
                //StringBuilder sb = new StringBuilder();
                //sb.AppendLine(xml_str);
                //sb.AppendLine(reg_str);
                //sb.AppendLine(num_str);
                //File.WriteAllText(dirName + "\\" + fileName + ".txt", sb.ToString());

                //DirectoryInfo dirInfo = new DirectoryInfo(dirName);
                //FileInfo[] files = dirInfo.GetFiles();
                //foreach(FileInfo file in files)
                //{
                //    if((DateTime.Now - file.CreationTime).TotalDays > 30)
                //    {
                //        file.Delete();
                //    }
                //}
            }
            catch(Exception ex)
            {
                Fpi.Log.LogUtil.Debug("NLPutils", "LogData" + ex.ToString());
            }
        }

        public static void ScoreCommandLogData(List<ScoreCommand> bestCommandList, string voiceText, string logFileName)
        {
            try
            {
                //string dirName = "ScoreCommandLog";
                //if(!Directory.Exists(dirName))
                //{
                //    Directory.CreateDirectory(dirName);
                //}

                string fileName = DateTime.Now.ToString("[yyyy:MM:dd_HH:mm:ss:fff]");
                if(!string.IsNullOrEmpty(logFileName))
                {
                    fileName = logFileName;
                }

                StringBuilder sb = new StringBuilder();
                sb.Append($"{fileName}\t{voiceText}\t");

                int num = 0;
                foreach(ScoreCommand cmdData in bestCommandList)
                {
                    num++;
                    sb.Append($"{cmdData.Score.ToString("f2")}\t{cmdData.CmdId.PadLeft(3, ' ')}\t{cmdData.ReturnCmd.SubCmd}\t");
                    if(num >= 5)
                    {
                        break;
                    }
                }
                //File.WriteAllText(dirName + "\\" + fileName + ".txt", sb.ToString());

                //DirectoryInfo dirInfo = new DirectoryInfo(dirName);
                //FileInfo[] files = dirInfo.GetFiles();
                //foreach(FileInfo file in files)
                //{
                //    if((DateTime.Now - file.CreationTime).TotalDays > 30)
                //    {
                //        file.Delete();
                //    }
                //}


                Fpi.Log.LogUtil.Debug("NLPutils", sb.ToString());
            }
            catch(Exception ex)
            {
                Fpi.Log.LogUtil.Debug("NLPutils", "LogData" + ex.ToString());
            }
        }

        /// <summary>
        /// 把dllcopy到讯飞可以识别的位置
        /// </summary>
        private static void CopyDllFile()
        {
            string[] dllfile = new string[] { "eabb2f029_v10092_aee.dll", "ebd1bade4_v1031_aee.dll", "ef7d69542_v1014_aee.dll" };
            string dllFilePath = "./iflytek/Aikit/res/";
            foreach(string dll in dllfile)
            {
                if(!File.Exists(dll))
                {
                    File.Copy(dllFilePath + dll, dll);
                }
            }
        }
    }
}
