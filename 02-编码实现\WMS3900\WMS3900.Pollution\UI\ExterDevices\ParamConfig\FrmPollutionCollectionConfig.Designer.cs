﻿namespace Fpi.WMS3000.Pollution.UI.ExterDevices
{
    partial class FrmPollutionCollectionConfig
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if(disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.txtPumpCurrentUpperLimit = new Sunny.UI.UITextBox();
            this.uiLabel8 = new Sunny.UI.UILabel();
            this.uiLabel1 = new Sunny.UI.UILabel();
            this.uiLabel2 = new Sunny.UI.UILabel();
            this.txtWaterPipeBlastingLowerLimit = new Sunny.UI.UITextBox();
            this.uiLabel3 = new Sunny.UI.UILabel();
            this.uiLabel4 = new Sunny.UI.UILabel();
            this.txtWaterPipePluggingLowerLimit = new Sunny.UI.UITextBox();
            this.uiLabel5 = new Sunny.UI.UILabel();
            this.uiLabel12 = new Sunny.UI.UILabel();
            this.uiLabel13 = new Sunny.UI.UILabel();
            this.txtSampleWaitingTime = new Sunny.UI.UITextBox();
            this.uiLabel14 = new Sunny.UI.UILabel();
            this.cmbPumpUsingMode = new Sunny.UI.UIComboBox();
            this.uiLabel9 = new Sunny.UI.UILabel();
            this.cmbPump1Node = new Sunny.UI.UIComboBox();
            this.cmbPump2Node = new Sunny.UI.UIComboBox();
            this.uiLabel11 = new Sunny.UI.UILabel();
            this.cmbWaterPressureNode = new Sunny.UI.UIComboBox();
            this.uiLabel20 = new Sunny.UI.UILabel();
            this.uiLabel24 = new Sunny.UI.UILabel();
            this.txtWaterPressureNormalLowerLimit = new Sunny.UI.UITextBox();
            this.uiLabel25 = new Sunny.UI.UILabel();
            this.cmbPumpCurrentNode = new Sunny.UI.UIComboBox();
            this.uiLabel26 = new Sunny.UI.UILabel();
            this.uiLabel28 = new Sunny.UI.UILabel();
            this.txtPumpCurrentLowerLimit = new Sunny.UI.UITextBox();
            this.uiLabel29 = new Sunny.UI.UILabel();
            this.uiLabel30 = new Sunny.UI.UILabel();
            this.txtPressureJudgeTime = new Sunny.UI.UITextBox();
            this.uiLabel31 = new Sunny.UI.UILabel();
            this.cmbWaterAnomalyMarkNode = new Sunny.UI.UIComboBox();
            this.uiLabel34 = new Sunny.UI.UILabel();
            this.uiPanel1 = new Sunny.UI.UIPanel();
            this.btnCancel = new Sunny.UI.UIButton();
            this.btnSave = new Sunny.UI.UIButton();
            this.rdbEvenPointABucketSampling = new Sunny.UI.UIRadioButton();
            this.rdbEvenPointBBucketSampling = new Sunny.UI.UIRadioButton();
            this.uiLabel6 = new Sunny.UI.UILabel();
            this.txtSamplerSampleWaitingTime = new Sunny.UI.UITextBox();
            this.uiLabel7 = new Sunny.UI.UILabel();
            this.uiPanel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // txtPumpCurrentUpperLimit
            // 
            this.txtPumpCurrentUpperLimit.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtPumpCurrentUpperLimit.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtPumpCurrentUpperLimit.Location = new System.Drawing.Point(206, 60);
            this.txtPumpCurrentUpperLimit.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtPumpCurrentUpperLimit.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtPumpCurrentUpperLimit.Name = "txtPumpCurrentUpperLimit";
            this.txtPumpCurrentUpperLimit.Padding = new System.Windows.Forms.Padding(5);
            this.txtPumpCurrentUpperLimit.ShowText = false;
            this.txtPumpCurrentUpperLimit.Size = new System.Drawing.Size(160, 32);
            this.txtPumpCurrentUpperLimit.TabIndex = 0;
            this.txtPumpCurrentUpperLimit.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtPumpCurrentUpperLimit.Watermark = "";
            // 
            // uiLabel8
            // 
            this.uiLabel8.AutoSize = true;
            this.uiLabel8.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel8.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel8.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel8.Location = new System.Drawing.Point(62, 66);
            this.uiLabel8.Name = "uiLabel8";
            this.uiLabel8.Size = new System.Drawing.Size(138, 21);
            this.uiLabel8.TabIndex = 16;
            this.uiLabel8.Text = "水泵运行电流上限";
            // 
            // uiLabel1
            // 
            this.uiLabel1.AutoSize = true;
            this.uiLabel1.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel1.Location = new System.Drawing.Point(373, 66);
            this.uiLabel1.Name = "uiLabel1";
            this.uiLabel1.Size = new System.Drawing.Size(21, 21);
            this.uiLabel1.TabIndex = 66;
            this.uiLabel1.Text = "A";
            // 
            // uiLabel2
            // 
            this.uiLabel2.AutoSize = true;
            this.uiLabel2.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel2.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel2.Location = new System.Drawing.Point(373, 282);
            this.uiLabel2.Name = "uiLabel2";
            this.uiLabel2.Size = new System.Drawing.Size(38, 21);
            this.uiLabel2.TabIndex = 69;
            this.uiLabel2.Text = "kPa";
            // 
            // txtWaterPipeBlastingLowerLimit
            // 
            this.txtWaterPipeBlastingLowerLimit.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtWaterPipeBlastingLowerLimit.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtWaterPipeBlastingLowerLimit.Location = new System.Drawing.Point(206, 276);
            this.txtWaterPipeBlastingLowerLimit.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtWaterPipeBlastingLowerLimit.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtWaterPipeBlastingLowerLimit.Name = "txtWaterPipeBlastingLowerLimit";
            this.txtWaterPipeBlastingLowerLimit.Padding = new System.Windows.Forms.Padding(5);
            this.txtWaterPipeBlastingLowerLimit.ShowText = false;
            this.txtWaterPipeBlastingLowerLimit.Size = new System.Drawing.Size(160, 32);
            this.txtWaterPipeBlastingLowerLimit.TabIndex = 4;
            this.txtWaterPipeBlastingLowerLimit.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtWaterPipeBlastingLowerLimit.Watermark = "";
            // 
            // uiLabel3
            // 
            this.uiLabel3.AutoSize = true;
            this.uiLabel3.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel3.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel3.Location = new System.Drawing.Point(46, 282);
            this.uiLabel3.Name = "uiLabel3";
            this.uiLabel3.Size = new System.Drawing.Size(154, 21);
            this.uiLabel3.TabIndex = 17;
            this.uiLabel3.Text = "采水管爆管压力下限";
            // 
            // uiLabel4
            // 
            this.uiLabel4.AutoSize = true;
            this.uiLabel4.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel4.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel4.Location = new System.Drawing.Point(373, 174);
            this.uiLabel4.Name = "uiLabel4";
            this.uiLabel4.Size = new System.Drawing.Size(38, 21);
            this.uiLabel4.TabIndex = 72;
            this.uiLabel4.Text = "kPa";
            // 
            // txtWaterPipePluggingLowerLimit
            // 
            this.txtWaterPipePluggingLowerLimit.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtWaterPipePluggingLowerLimit.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtWaterPipePluggingLowerLimit.Location = new System.Drawing.Point(206, 168);
            this.txtWaterPipePluggingLowerLimit.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtWaterPipePluggingLowerLimit.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtWaterPipePluggingLowerLimit.Name = "txtWaterPipePluggingLowerLimit";
            this.txtWaterPipePluggingLowerLimit.Padding = new System.Windows.Forms.Padding(5);
            this.txtWaterPipePluggingLowerLimit.ShowText = false;
            this.txtWaterPipePluggingLowerLimit.Size = new System.Drawing.Size(160, 32);
            this.txtWaterPipePluggingLowerLimit.TabIndex = 2;
            this.txtWaterPipePluggingLowerLimit.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtWaterPipePluggingLowerLimit.Watermark = "";
            // 
            // uiLabel5
            // 
            this.uiLabel5.AutoSize = true;
            this.uiLabel5.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel5.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel5.Location = new System.Drawing.Point(46, 174);
            this.uiLabel5.Name = "uiLabel5";
            this.uiLabel5.Size = new System.Drawing.Size(154, 21);
            this.uiLabel5.TabIndex = 18;
            this.uiLabel5.Text = "采水管堵塞压力下限";
            // 
            // uiLabel12
            // 
            this.uiLabel12.AutoSize = true;
            this.uiLabel12.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel12.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel12.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel12.Location = new System.Drawing.Point(423, 66);
            this.uiLabel12.Name = "uiLabel12";
            this.uiLabel12.Size = new System.Drawing.Size(122, 21);
            this.uiLabel12.TabIndex = 23;
            this.uiLabel12.Text = "采水泵使用模式";
            // 
            // uiLabel13
            // 
            this.uiLabel13.AutoSize = true;
            this.uiLabel13.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel13.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel13.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel13.Location = new System.Drawing.Point(373, 436);
            this.uiLabel13.Name = "uiLabel13";
            this.uiLabel13.Size = new System.Drawing.Size(26, 21);
            this.uiLabel13.TabIndex = 81;
            this.uiLabel13.Text = "秒";
            // 
            // txtSampleWaitingTime
            // 
            this.txtSampleWaitingTime.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtSampleWaitingTime.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtSampleWaitingTime.Location = new System.Drawing.Point(206, 438);
            this.txtSampleWaitingTime.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtSampleWaitingTime.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtSampleWaitingTime.Name = "txtSampleWaitingTime";
            this.txtSampleWaitingTime.Padding = new System.Windows.Forms.Padding(5);
            this.txtSampleWaitingTime.ShowText = false;
            this.txtSampleWaitingTime.Size = new System.Drawing.Size(160, 32);
            this.txtSampleWaitingTime.TabIndex = 7;
            this.txtSampleWaitingTime.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtSampleWaitingTime.Watermark = "";
            // 
            // uiLabel14
            // 
            this.uiLabel14.AutoSize = true;
            this.uiLabel14.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel14.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel14.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel14.Location = new System.Drawing.Point(94, 444);
            this.uiLabel14.Name = "uiLabel14";
            this.uiLabel14.Size = new System.Drawing.Size(106, 21);
            this.uiLabel14.TabIndex = 21;
            this.uiLabel14.Text = "采水持续时间";
            // 
            // cmbPumpUsingMode
            // 
            this.cmbPumpUsingMode.DataSource = null;
            this.cmbPumpUsingMode.DropDownAutoWidth = true;
            this.cmbPumpUsingMode.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbPumpUsingMode.FillColor = System.Drawing.Color.White;
            this.cmbPumpUsingMode.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cmbPumpUsingMode.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbPumpUsingMode.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbPumpUsingMode.Location = new System.Drawing.Point(550, 62);
            this.cmbPumpUsingMode.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbPumpUsingMode.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbPumpUsingMode.Name = "cmbPumpUsingMode";
            this.cmbPumpUsingMode.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbPumpUsingMode.Size = new System.Drawing.Size(205, 29);
            this.cmbPumpUsingMode.SymbolSize = 24;
            this.cmbPumpUsingMode.TabIndex = 10;
            this.cmbPumpUsingMode.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbPumpUsingMode.Watermark = "";
            // 
            // uiLabel9
            // 
            this.uiLabel9.AutoSize = true;
            this.uiLabel9.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel9.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel9.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel9.Location = new System.Drawing.Point(468, 120);
            this.uiLabel9.Name = "uiLabel9";
            this.uiLabel9.Size = new System.Drawing.Size(77, 21);
            this.uiLabel9.TabIndex = 24;
            this.uiLabel9.Text = "1#采水泵";
            // 
            // cmbPump1Node
            // 
            this.cmbPump1Node.DataSource = null;
            this.cmbPump1Node.DropDownAutoWidth = true;
            this.cmbPump1Node.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbPump1Node.FillColor = System.Drawing.Color.White;
            this.cmbPump1Node.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cmbPump1Node.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbPump1Node.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbPump1Node.Location = new System.Drawing.Point(550, 116);
            this.cmbPump1Node.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbPump1Node.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbPump1Node.Name = "cmbPump1Node";
            this.cmbPump1Node.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbPump1Node.Size = new System.Drawing.Size(205, 29);
            this.cmbPump1Node.SymbolSize = 24;
            this.cmbPump1Node.TabIndex = 11;
            this.cmbPump1Node.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbPump1Node.Watermark = "";
            // 
            // cmbPump2Node
            // 
            this.cmbPump2Node.DataSource = null;
            this.cmbPump2Node.DropDownAutoWidth = true;
            this.cmbPump2Node.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbPump2Node.FillColor = System.Drawing.Color.White;
            this.cmbPump2Node.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cmbPump2Node.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbPump2Node.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbPump2Node.Location = new System.Drawing.Point(550, 170);
            this.cmbPump2Node.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbPump2Node.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbPump2Node.Name = "cmbPump2Node";
            this.cmbPump2Node.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbPump2Node.Size = new System.Drawing.Size(205, 29);
            this.cmbPump2Node.SymbolSize = 24;
            this.cmbPump2Node.TabIndex = 12;
            this.cmbPump2Node.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbPump2Node.Watermark = "";
            // 
            // uiLabel11
            // 
            this.uiLabel11.AutoSize = true;
            this.uiLabel11.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel11.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel11.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel11.Location = new System.Drawing.Point(468, 174);
            this.uiLabel11.Name = "uiLabel11";
            this.uiLabel11.Size = new System.Drawing.Size(77, 21);
            this.uiLabel11.TabIndex = 25;
            this.uiLabel11.Text = "2#采水泵";
            // 
            // cmbWaterPressureNode
            // 
            this.cmbWaterPressureNode.DataSource = null;
            this.cmbWaterPressureNode.DropDownAutoWidth = true;
            this.cmbWaterPressureNode.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbWaterPressureNode.FillColor = System.Drawing.Color.White;
            this.cmbWaterPressureNode.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cmbWaterPressureNode.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbWaterPressureNode.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbWaterPressureNode.Location = new System.Drawing.Point(550, 224);
            this.cmbWaterPressureNode.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbWaterPressureNode.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbWaterPressureNode.Name = "cmbWaterPressureNode";
            this.cmbWaterPressureNode.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbWaterPressureNode.Size = new System.Drawing.Size(205, 29);
            this.cmbWaterPressureNode.SymbolSize = 24;
            this.cmbWaterPressureNode.TabIndex = 13;
            this.cmbWaterPressureNode.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbWaterPressureNode.Watermark = "";
            // 
            // uiLabel20
            // 
            this.uiLabel20.AutoSize = true;
            this.uiLabel20.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel20.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel20.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel20.Location = new System.Drawing.Point(471, 228);
            this.uiLabel20.Name = "uiLabel20";
            this.uiLabel20.Size = new System.Drawing.Size(74, 21);
            this.uiLabel20.TabIndex = 29;
            this.uiLabel20.Text = "采水压力";
            // 
            // uiLabel24
            // 
            this.uiLabel24.AutoSize = true;
            this.uiLabel24.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel24.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel24.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel24.Location = new System.Drawing.Point(373, 228);
            this.uiLabel24.Name = "uiLabel24";
            this.uiLabel24.Size = new System.Drawing.Size(38, 21);
            this.uiLabel24.TabIndex = 88;
            this.uiLabel24.Text = "kPa";
            // 
            // txtWaterPressureNormalLowerLimit
            // 
            this.txtWaterPressureNormalLowerLimit.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtWaterPressureNormalLowerLimit.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtWaterPressureNormalLowerLimit.Location = new System.Drawing.Point(206, 222);
            this.txtWaterPressureNormalLowerLimit.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtWaterPressureNormalLowerLimit.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtWaterPressureNormalLowerLimit.Name = "txtWaterPressureNormalLowerLimit";
            this.txtWaterPressureNormalLowerLimit.Padding = new System.Windows.Forms.Padding(5);
            this.txtWaterPressureNormalLowerLimit.ShowText = false;
            this.txtWaterPressureNormalLowerLimit.Size = new System.Drawing.Size(160, 32);
            this.txtWaterPressureNormalLowerLimit.TabIndex = 3;
            this.txtWaterPressureNormalLowerLimit.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtWaterPressureNormalLowerLimit.Watermark = "";
            // 
            // uiLabel25
            // 
            this.uiLabel25.AutoSize = true;
            this.uiLabel25.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel25.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel25.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel25.Location = new System.Drawing.Point(62, 228);
            this.uiLabel25.Name = "uiLabel25";
            this.uiLabel25.Size = new System.Drawing.Size(138, 21);
            this.uiLabel25.TabIndex = 87;
            this.uiLabel25.Text = "采水正常压力下限";
            // 
            // cmbPumpCurrentNode
            // 
            this.cmbPumpCurrentNode.DataSource = null;
            this.cmbPumpCurrentNode.DropDownAutoWidth = true;
            this.cmbPumpCurrentNode.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbPumpCurrentNode.FillColor = System.Drawing.Color.White;
            this.cmbPumpCurrentNode.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cmbPumpCurrentNode.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbPumpCurrentNode.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbPumpCurrentNode.Location = new System.Drawing.Point(550, 278);
            this.cmbPumpCurrentNode.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbPumpCurrentNode.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbPumpCurrentNode.Name = "cmbPumpCurrentNode";
            this.cmbPumpCurrentNode.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbPumpCurrentNode.Size = new System.Drawing.Size(205, 29);
            this.cmbPumpCurrentNode.SymbolSize = 24;
            this.cmbPumpCurrentNode.TabIndex = 14;
            this.cmbPumpCurrentNode.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbPumpCurrentNode.Watermark = "";
            // 
            // uiLabel26
            // 
            this.uiLabel26.AutoSize = true;
            this.uiLabel26.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel26.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel26.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel26.Location = new System.Drawing.Point(471, 282);
            this.uiLabel26.Name = "uiLabel26";
            this.uiLabel26.Size = new System.Drawing.Size(74, 21);
            this.uiLabel26.TabIndex = 90;
            this.uiLabel26.Text = "水泵电流";
            // 
            // uiLabel28
            // 
            this.uiLabel28.AutoSize = true;
            this.uiLabel28.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel28.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel28.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel28.Location = new System.Drawing.Point(373, 120);
            this.uiLabel28.Name = "uiLabel28";
            this.uiLabel28.Size = new System.Drawing.Size(21, 21);
            this.uiLabel28.TabIndex = 95;
            this.uiLabel28.Text = "A";
            // 
            // txtPumpCurrentLowerLimit
            // 
            this.txtPumpCurrentLowerLimit.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtPumpCurrentLowerLimit.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtPumpCurrentLowerLimit.Location = new System.Drawing.Point(206, 114);
            this.txtPumpCurrentLowerLimit.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtPumpCurrentLowerLimit.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtPumpCurrentLowerLimit.Name = "txtPumpCurrentLowerLimit";
            this.txtPumpCurrentLowerLimit.Padding = new System.Windows.Forms.Padding(5);
            this.txtPumpCurrentLowerLimit.ShowText = false;
            this.txtPumpCurrentLowerLimit.Size = new System.Drawing.Size(160, 32);
            this.txtPumpCurrentLowerLimit.TabIndex = 1;
            this.txtPumpCurrentLowerLimit.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtPumpCurrentLowerLimit.Watermark = "";
            // 
            // uiLabel29
            // 
            this.uiLabel29.AutoSize = true;
            this.uiLabel29.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel29.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel29.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel29.Location = new System.Drawing.Point(62, 120);
            this.uiLabel29.Name = "uiLabel29";
            this.uiLabel29.Size = new System.Drawing.Size(138, 21);
            this.uiLabel29.TabIndex = 94;
            this.uiLabel29.Text = "水泵运行电流下限";
            // 
            // uiLabel30
            // 
            this.uiLabel30.AutoSize = true;
            this.uiLabel30.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel30.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel30.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel30.Location = new System.Drawing.Point(373, 336);
            this.uiLabel30.Name = "uiLabel30";
            this.uiLabel30.Size = new System.Drawing.Size(26, 21);
            this.uiLabel30.TabIndex = 98;
            this.uiLabel30.Text = "秒";
            // 
            // txtPressureJudgeTime
            // 
            this.txtPressureJudgeTime.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtPressureJudgeTime.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtPressureJudgeTime.Location = new System.Drawing.Point(206, 330);
            this.txtPressureJudgeTime.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtPressureJudgeTime.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtPressureJudgeTime.Name = "txtPressureJudgeTime";
            this.txtPressureJudgeTime.Padding = new System.Windows.Forms.Padding(5);
            this.txtPressureJudgeTime.ShowText = false;
            this.txtPressureJudgeTime.Size = new System.Drawing.Size(160, 32);
            this.txtPressureJudgeTime.TabIndex = 5;
            this.txtPressureJudgeTime.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtPressureJudgeTime.Watermark = "";
            // 
            // uiLabel31
            // 
            this.uiLabel31.AutoSize = true;
            this.uiLabel31.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel31.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel31.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel31.Location = new System.Drawing.Point(62, 336);
            this.uiLabel31.Name = "uiLabel31";
            this.uiLabel31.Size = new System.Drawing.Size(138, 21);
            this.uiLabel31.TabIndex = 97;
            this.uiLabel31.Text = "采水压力判断时间";
            // 
            // cmbWaterAnomalyMarkNode
            // 
            this.cmbWaterAnomalyMarkNode.DataSource = null;
            this.cmbWaterAnomalyMarkNode.DropDownAutoWidth = true;
            this.cmbWaterAnomalyMarkNode.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbWaterAnomalyMarkNode.FillColor = System.Drawing.Color.White;
            this.cmbWaterAnomalyMarkNode.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cmbWaterAnomalyMarkNode.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbWaterAnomalyMarkNode.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbWaterAnomalyMarkNode.Location = new System.Drawing.Point(550, 332);
            this.cmbWaterAnomalyMarkNode.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbWaterAnomalyMarkNode.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbWaterAnomalyMarkNode.Name = "cmbWaterAnomalyMarkNode";
            this.cmbWaterAnomalyMarkNode.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbWaterAnomalyMarkNode.Size = new System.Drawing.Size(205, 29);
            this.cmbWaterAnomalyMarkNode.SymbolSize = 24;
            this.cmbWaterAnomalyMarkNode.TabIndex = 15;
            this.cmbWaterAnomalyMarkNode.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbWaterAnomalyMarkNode.Watermark = "";
            // 
            // uiLabel34
            // 
            this.uiLabel34.AutoSize = true;
            this.uiLabel34.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel34.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel34.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel34.Location = new System.Drawing.Point(439, 336);
            this.uiLabel34.Name = "uiLabel34";
            this.uiLabel34.Size = new System.Drawing.Size(106, 21);
            this.uiLabel34.TabIndex = 104;
            this.uiLabel34.Text = "采水异常标志";
            // 
            // uiPanel1
            // 
            this.uiPanel1.Controls.Add(this.btnCancel);
            this.uiPanel1.Controls.Add(this.btnSave);
            this.uiPanel1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.uiPanel1.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiPanel1.Location = new System.Drawing.Point(5, 741);
            this.uiPanel1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiPanel1.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiPanel1.Name = "uiPanel1";
            this.uiPanel1.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uiPanel1.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.Top;
            this.uiPanel1.Size = new System.Drawing.Size(790, 45);
            this.uiPanel1.TabIndex = 25;
            this.uiPanel1.Text = null;
            this.uiPanel1.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnCancel.Location = new System.Drawing.Point(688, 7);
            this.btnCancel.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(100, 35);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "取消";
            this.btnCancel.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            // 
            // btnSave
            // 
            this.btnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSave.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnSave.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSave.Location = new System.Drawing.Point(571, 7);
            this.btnSave.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(100, 35);
            this.btnSave.TabIndex = 0;
            this.btnSave.Text = "保存";
            this.btnSave.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // rdbEvenPointABucketSampling
            // 
            this.rdbEvenPointABucketSampling.AutoSize = true;
            this.rdbEvenPointABucketSampling.Checked = true;
            this.rdbEvenPointABucketSampling.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbEvenPointABucketSampling.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.rdbEvenPointABucketSampling.Location = new System.Drawing.Point(60, 494);
            this.rdbEvenPointABucketSampling.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbEvenPointABucketSampling.Name = "rdbEvenPointABucketSampling";
            this.rdbEvenPointABucketSampling.Size = new System.Drawing.Size(140, 26);
            this.rdbEvenPointABucketSampling.TabIndex = 8;
            this.rdbEvenPointABucketSampling.Text = "偶数点A桶采样";
            // 
            // rdbEvenPointBBucketSampling
            // 
            this.rdbEvenPointBBucketSampling.AutoSize = true;
            this.rdbEvenPointBBucketSampling.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbEvenPointBBucketSampling.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.rdbEvenPointBBucketSampling.Location = new System.Drawing.Point(260, 494);
            this.rdbEvenPointBBucketSampling.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbEvenPointBBucketSampling.Name = "rdbEvenPointBBucketSampling";
            this.rdbEvenPointBBucketSampling.Size = new System.Drawing.Size(139, 26);
            this.rdbEvenPointBBucketSampling.TabIndex = 9;
            this.rdbEvenPointBBucketSampling.Text = "偶数点B桶采样";
            // 
            // uiLabel6
            // 
            this.uiLabel6.AutoSize = true;
            this.uiLabel6.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel6.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel6.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel6.Location = new System.Drawing.Point(373, 388);
            this.uiLabel6.Name = "uiLabel6";
            this.uiLabel6.Size = new System.Drawing.Size(26, 21);
            this.uiLabel6.TabIndex = 109;
            this.uiLabel6.Text = "秒";
            // 
            // txtSamplerSampleWaitingTime
            // 
            this.txtSamplerSampleWaitingTime.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtSamplerSampleWaitingTime.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtSamplerSampleWaitingTime.Location = new System.Drawing.Point(206, 384);
            this.txtSamplerSampleWaitingTime.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtSamplerSampleWaitingTime.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtSamplerSampleWaitingTime.Name = "txtSamplerSampleWaitingTime";
            this.txtSamplerSampleWaitingTime.Padding = new System.Windows.Forms.Padding(5);
            this.txtSamplerSampleWaitingTime.ShowText = false;
            this.txtSamplerSampleWaitingTime.Size = new System.Drawing.Size(160, 32);
            this.txtSamplerSampleWaitingTime.TabIndex = 6;
            this.txtSamplerSampleWaitingTime.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtSamplerSampleWaitingTime.Watermark = "";
            // 
            // uiLabel7
            // 
            this.uiLabel7.AutoSize = true;
            this.uiLabel7.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel7.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel7.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel7.Location = new System.Drawing.Point(46, 390);
            this.uiLabel7.Name = "uiLabel7";
            this.uiLabel7.Size = new System.Drawing.Size(154, 21);
            this.uiLabel7.TabIndex = 108;
            this.uiLabel7.Text = "采样器采水等待时间";
            // 
            // FrmPollutionCollectionConfig
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(800, 787);
            this.Controls.Add(this.uiLabel6);
            this.Controls.Add(this.txtSamplerSampleWaitingTime);
            this.Controls.Add(this.uiLabel7);
            this.Controls.Add(this.rdbEvenPointBBucketSampling);
            this.Controls.Add(this.rdbEvenPointABucketSampling);
            this.Controls.Add(this.uiPanel1);
            this.Controls.Add(this.cmbWaterAnomalyMarkNode);
            this.Controls.Add(this.uiLabel34);
            this.Controls.Add(this.uiLabel30);
            this.Controls.Add(this.txtPressureJudgeTime);
            this.Controls.Add(this.uiLabel31);
            this.Controls.Add(this.uiLabel28);
            this.Controls.Add(this.txtPumpCurrentLowerLimit);
            this.Controls.Add(this.uiLabel29);
            this.Controls.Add(this.cmbPumpCurrentNode);
            this.Controls.Add(this.uiLabel26);
            this.Controls.Add(this.uiLabel24);
            this.Controls.Add(this.txtWaterPressureNormalLowerLimit);
            this.Controls.Add(this.uiLabel25);
            this.Controls.Add(this.cmbWaterPressureNode);
            this.Controls.Add(this.uiLabel20);
            this.Controls.Add(this.cmbPump2Node);
            this.Controls.Add(this.uiLabel11);
            this.Controls.Add(this.cmbPump1Node);
            this.Controls.Add(this.uiLabel9);
            this.Controls.Add(this.cmbPumpUsingMode);
            this.Controls.Add(this.uiLabel12);
            this.Controls.Add(this.uiLabel13);
            this.Controls.Add(this.txtSampleWaitingTime);
            this.Controls.Add(this.uiLabel14);
            this.Controls.Add(this.uiLabel4);
            this.Controls.Add(this.txtWaterPipePluggingLowerLimit);
            this.Controls.Add(this.uiLabel5);
            this.Controls.Add(this.uiLabel2);
            this.Controls.Add(this.txtWaterPipeBlastingLowerLimit);
            this.Controls.Add(this.uiLabel3);
            this.Controls.Add(this.uiLabel1);
            this.Controls.Add(this.txtPumpCurrentUpperLimit);
            this.Controls.Add(this.uiLabel8);
            this.EscClose = true;
            this.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FrmPollutionCollectionConfig";
            this.Padding = new System.Windows.Forms.Padding(5, 35, 5, 1);
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.Text = "混采参数配置";
            this.ZoomScaleRect = new System.Drawing.Rectangle(15, 15, 800, 450);
            this.Load += new System.EventHandler(this.FrmPollutionCollectionConfig_Load);
            this.uiPanel1.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private Sunny.UI.UITextBox txtPumpCurrentUpperLimit;
        private Sunny.UI.UILabel uiLabel8;
        private Sunny.UI.UILabel uiLabel1;
        private Sunny.UI.UILabel uiLabel2;
        private Sunny.UI.UITextBox txtWaterPipeBlastingLowerLimit;
        private Sunny.UI.UILabel uiLabel3;
        private Sunny.UI.UILabel uiLabel4;
        private Sunny.UI.UITextBox txtWaterPipePluggingLowerLimit;
        private Sunny.UI.UILabel uiLabel5;
        private Sunny.UI.UILabel uiLabel12;
        private Sunny.UI.UILabel uiLabel13;
        private Sunny.UI.UITextBox txtSampleWaitingTime;
        private Sunny.UI.UILabel uiLabel14;
        private Sunny.UI.UIComboBox cmbPumpUsingMode;
        private Sunny.UI.UILabel uiLabel9;
        private Sunny.UI.UIComboBox cmbPump1Node;
        private Sunny.UI.UIComboBox cmbPump2Node;
        private Sunny.UI.UILabel uiLabel11;
        private Sunny.UI.UIComboBox cmbWaterPressureNode;
        private Sunny.UI.UILabel uiLabel20;
        private Sunny.UI.UILabel uiLabel24;
        private Sunny.UI.UITextBox txtWaterPressureNormalLowerLimit;
        private Sunny.UI.UILabel uiLabel25;
        private Sunny.UI.UIComboBox cmbPumpCurrentNode;
        private Sunny.UI.UILabel uiLabel26;
        private Sunny.UI.UILabel uiLabel28;
        private Sunny.UI.UITextBox txtPumpCurrentLowerLimit;
        private Sunny.UI.UILabel uiLabel29;
        private Sunny.UI.UILabel uiLabel30;
        private Sunny.UI.UITextBox txtPressureJudgeTime;
        private Sunny.UI.UILabel uiLabel31;
        private Sunny.UI.UIComboBox cmbWaterAnomalyMarkNode;
        private Sunny.UI.UILabel uiLabel34;
        private Sunny.UI.UIPanel uiPanel1;
        private Sunny.UI.UIButton btnCancel;
        private Sunny.UI.UIButton btnSave;
        private Sunny.UI.UIRadioButton rdbEvenPointABucketSampling;
        private Sunny.UI.UIRadioButton rdbEvenPointBBucketSampling;
        private Sunny.UI.UILabel uiLabel6;
        private Sunny.UI.UITextBox txtSamplerSampleWaitingTime;
        private Sunny.UI.UILabel uiLabel7;
    }
}