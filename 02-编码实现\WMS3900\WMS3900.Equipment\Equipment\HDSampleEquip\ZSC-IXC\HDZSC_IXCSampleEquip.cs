﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using Fpi.Alarm;
using Fpi.Communication;
using Fpi.Communication.Converter;
using Fpi.Communication.Interfaces;
using Fpi.Devices;
using Fpi.Devices.DeviceProtocols;
using Fpi.WMS3000.Equipment.HD;
using Fpi.WMS3000.Equipment.Interface;
using Fpi.WMS3000.Equipment.UI;

namespace Fpi.WMS3000.Equipment
{
    public class HDZSC_IXCSampleEquip : Device, IMixedSampleDeviceOperation, ISampleBottleNum
    {
        #region 字段属性

        /// <summary>
        /// 设备参数
        /// </summary>
        public HDZSCIXCParam DeviceParams { get; } = new HDZSCIXCParam();

        #endregion

        #region 构造

        public HDZSC_IXCSampleEquip()
        {
            this.DeviceType = eDeviceType.AUXILI;

            if(string.IsNullOrEmpty(this.Description))
            {
                this.Description = "恒达采样器 ZSC-IXC";
            }

            if(this.id.Contains("DefaultID"))
            {
                this.id = "HDZSCIXCEquip";
            }

            if(this.name.Contains("DefaultName"))
            {
                this.name = "恒达采样器 ZSC-IXC";
            }

            if(string.IsNullOrEmpty(this.AlarmGroupId))
            {
                this.AlarmGroupId = "HDZSCIXCEquip";
            }

            if(string.IsNullOrEmpty(this.AlarmSourceId))
            {
                this.AlarmSourceId = "HDZSCIXCEquip";
            }

            if(string.IsNullOrEmpty(this.ProtocolImp))
            {
                this.ProtocolImp = typeof(ModbusProtocol).FullName;
            }
        }

        #endregion

        #region 公共（重写）方法

        public override string ToString()
        {
            return string.IsNullOrEmpty(this.name) || this.name.Contains("DefaultName") ? "恒达采样器 ZSC-IXC" : this.name;
        }

        /// <summary>
        /// 报警设置
        /// </summary>
        public override void SetDeviceAlarmList()
        {
            base.SetDeviceAlarmList();
            this.DeviceAlarmList.Add("1", "瓶满");
            this.DeviceAlarmList.Add("2", "转盘故障");
            this.DeviceAlarmList.Add("3", "升降杆故障");
            this.DeviceAlarmList.Add("4", "采样口无水");
            this.DeviceAlarmList.Add("5", "采样桶无水");
            this.DeviceAlarmList.Add("6", "温度计故障");
            this.DeviceAlarmList.Add("7", "药品不足");
            this.DeviceAlarmList.Add("8", "泵管寿命不足");
            this.DeviceAlarmList.Add("9", "药品保质期超期");
        }

        /// <summary>
        /// 设备状态参数查看界面
        /// </summary>
        public override UserControl GetDeviceParamUC()
        {
            return new UC_HDZSCIXCParam(this);
        }

        public override void GetDeviceData()
        {
            try
            {
                {
                    byte[] dataSend = { byte.Parse(this.Addr), 0x03, 0x00, 0x01, 0x00, 0x25 };
                    IByteStream bs = new ByteArrayWrap(dataSend);
                    IByteStream bsRecv = SendToDevice(id, bs);
                    if(bsRecv == null)
                    {
                        throw new Exception("读取第一包参数无数据回应!");
                    }
                    byte[] dataReceive = bsRecv.GetBytes();

                    if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                    {
                        throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                    }
                    if(dataReceive.Length < 77)
                    {
                        throw new Exception("读取第一包参数回应数据长度不合法！");
                    }
                    if(dataReceive[2] != 0x4A)
                    {
                        throw new Exception("读取第一包参数回应数据错位！");
                    }

                    DeviceParams.UpdateParamPackageOne(dataReceive, 3);

                    // 解析报警信息位
                    for(int i = 1; i <= 16; i++)
                    {
                        if((DeviceParams.AlarmFlags & 0x00000001) == 0x00000001)
                        {
                            AlarmManager.GetInstance().AddAlarm(AlarmSourceId, i.ToString());
                        }
                        else
                        {
                            AlarmManager.GetInstance().RemoveAlarm(AlarmSourceId, i.ToString());
                        }
                    }
                }

                {
                    byte[] dataSend = { byte.Parse(this.Addr), 0x03, 0x01, 0x50, 0x00, 0x0A };
                    IByteStream bs = new ByteArrayWrap(dataSend);
                    IByteStream bsRecv = SendToDevice(id, bs);
                    if(bsRecv == null)
                    {
                        throw new Exception("读取第二包参数无数据回应!");
                    }
                    byte[] dataReceive = bsRecv.GetBytes();

                    if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                    {
                        throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                    }
                    if(dataReceive.Length < 23)
                    {
                        throw new Exception("读取第二包参数回应数据长度不合法！");
                    }
                    if(dataReceive[2] != 0x14)
                    {
                        throw new Exception("读取第二包参数回应数据错位！");
                    }

                    DeviceParams.UpdateParamPackageTwo(dataReceive, 3);
                }

                //消除通信异常报警
                AlarmManager.GetInstance().RemoveAlarm(AlarmSourceId, ComErrorAlarmCodeId);
                this._communicationErrorCount = 0;
            }
            catch(Exception ex)
            {
                if(this._communicationErrorCount++ >= 3)
                {
                    //增加通信异常报警
                    AlarmManager.GetInstance().AddAlarm(AlarmSourceId, ComErrorAlarmCodeId);
                }

                throw new Exception($"[{name}]读数失败：{ex.Message}");
            }
            finally
            {
                this.SetRealDataToNode();
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 校时
        /// </summary>
        /// <param name="time"></param>
        public void SetDeviceTime(DateTime time)
        {
            try
            {
                byte[] dataSend =
                  { byte.Parse(Addr), 0x10, 0x00, 0x90, 0x00, 0x03, 0x06,
                    (byte) (time.Year - 2000),
                    (byte) (time.Month),
                    (byte) (time.Day),
                    (byte) (time.Hour),
                    (byte) (time.Minute),
                    (byte) (time.Second)
                };
                IByteStream bs = new ByteArrayWrap(dataSend);
                IByteStream bsRecv = SendToDevice(id, bs);
                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]校准时间失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 复位排空
        /// </summary>
        [Description("复位排空")]
        public void SetImmediateStopAndReset()
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange([byte.Parse(Addr), 0x10, 0x00, 0xA3, 0x00, 0x01, 0x02]);
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(1)); // 0001：停止当前工作，并排空复位
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]复位排空触发失败：{ex.Message}");
            }
        }

        #region 全被动模式控制方法

        /// <summary>
        /// A桶采水
        /// </summary>
        public void SetImmediateWaterSamplingA()
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange([byte.Parse(Addr), 0x10, 0x01, 0x02, 0x00, 0x01, 0x02]);
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(1)); // 0001：开始往A桶里采水并开启全被动操作
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]A桶采水触发失败：{ex.Message}");
            }
        }

        /// <summary>
        /// B桶采水
        /// </summary>
        public void SetImmediateWaterSamplingB()
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange([byte.Parse(Addr), 0x10, 0x01, 0x03, 0x00, 0x01, 0x02]);
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(1)); // 0001：开始往B桶里采水并开启全被动操作
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]B桶采水触发失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 采水量设置
        /// </summary>
        /// <param name="volume">采水量(ml)</param>
        public void SetABSamplingVolume(int volume)
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange([byte.Parse(Addr), 0x10, 0x01, 0x04, 0x00, 0x01, 0x02]);
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(volume));
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]采水量设置失败：{ex.Message}");
            }
        }

        /// <summary>
        /// A桶供样
        /// </summary>
        public void SetImmediateSupplyA()
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange([byte.Parse(Addr), 0x10, 0x01, 0x05, 0x00, 0x01, 0x02]);
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(1)); // 0001：开始把A桶的水供给外部
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]A桶供样触发失败：{ex.Message}");
            }
        }

        /// <summary>
        /// B桶供样
        /// </summary>
        public void SetImmediateSupplyB()
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange([byte.Parse(Addr), 0x10, 0x01, 0x06, 0x00, 0x01, 0x02]);
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(1)); // 0001：开始把B桶的水供给外部
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]B桶供样触发失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 供样时间设置
        /// </summary>
        /// <param name="time">供样时间(min)</param>
        public void SetABSupplyTime(int time)
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange([byte.Parse(Addr), 0x10, 0x01, 0x07, 0x00, 0x01, 0x02]);
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(time));
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]供样时间设置失败：{ex.Message}");
            }
        }

        /// <summary>
        /// A桶留样
        /// </summary>
        public void SetImmediateRetainA()
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange([byte.Parse(Addr), 0x10, 0x01, 0x08, 0x00, 0x01, 0x02]);
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(1)); // 0001：开始把A桶的水打到留样瓶
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]A桶留样触发失败：{ex.Message}");
            }
        }

        /// <summary>
        /// B桶留样
        /// </summary>
        public void SetImmediateRetainB()
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange([byte.Parse(Addr), 0x10, 0x01, 0x09, 0x00, 0x01, 0x02]);
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(1)); // 0001：开始把B桶的水打到留样瓶
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]B桶留样触发失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 留样量设置
        /// </summary>
        /// <param name="volume">留样量(ml)</param>
        public void SetPassiveRetainVolume(int volume)
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange([byte.Parse(Addr), 0x10, 0x01, 0x0A, 0x00, 0x01, 0x02]);
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(volume));
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]留样量设置失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 供样管路排空
        public void SetSupplyPipeDrain()
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange([byte.Parse(Addr), 0x10, 0x01, 0x0B, 0x00, 0x01, 0x02]);
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(1)); // 0001：当前供样管路的水排空
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]供样管路排空失败：{ex.Message}");
            }
        }

        /// <summary>
        /// A桶排空
        /// </summary>
        public void SetBucketADrain()
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange([byte.Parse(Addr), 0x10, 0x01, 0x0C, 0x00, 0x01, 0x02]);
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(1)); // 0001：开始把A桶的水排空
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]A桶排空失败：{ex.Message}");
            }
        }

        /// <summary>
        /// B桶排空
        /// </summary>
        public void SetBucketBDrain()
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange([byte.Parse(Addr), 0x10, 0x01, 0x0D, 0x00, 0x01, 0x02]);
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(1)); // 0001：开始把B桶的水排空
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]B桶排空失败：{ex.Message}");
            }
        }

        #endregion

        #region 留样瓶控制方法

        /// <summary>
        /// 指定留样瓶排空
        /// </summary>
        /// <param name="bottleNumber">留样瓶号(1-24)</param>
        public void SetSpecificBottleDrain(int bottleNumber)
        {
            try
            {
                if(bottleNumber < 1 || bottleNumber > 24)
                {
                    throw new ArgumentException("留样瓶号必须在1-24之间");
                }

                List<byte> dataSend = new List<byte>();
                dataSend.AddRange([byte.Parse(Addr), 0x10, 0x01, 0x10, 0x00, 0x01, 0x02]);
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(bottleNumber));
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]指定留样瓶排空失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 全部留样瓶排空
        /// </summary>
        public void SetAllBottlesDrain()
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange([byte.Parse(Addr), 0x10, 0x01, 0x11, 0x00, 0x01, 0x02]);
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(1)); // 0x0001：全部留样瓶排空
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]全部留样瓶排空失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 指定留样瓶润洗
        /// </summary>
        /// <param name="bottleNumber">留样瓶号(1-24)</param>
        public void SetSpecificBottleRinse(int bottleNumber)
        {
            try
            {
                if(bottleNumber < 1 || bottleNumber > 24)
                {
                    throw new ArgumentException("留样瓶号必须在1-24之间");
                }

                List<byte> dataSend = new List<byte>();
                dataSend.AddRange([byte.Parse(Addr), 0x10, 0x01, 0x12, 0x00, 0x01, 0x02]);
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(bottleNumber));
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]指定留样瓶润洗失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 全部留样瓶润洗
        /// </summary>
        public void SetAllBottlesRinse()
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange([byte.Parse(Addr), 0x10, 0x01, 0x13, 0x00, 0x01, 0x02]);
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(1)); // 0x0001：全部留样瓶润洗
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]全部留样瓶润洗失败：{ex.Message}");
            }
        }

        #endregion

        #endregion

        #region ISampleBottleNum

        /// <summary>
        /// 读取当前采样瓶编号
        /// </summary>
        public int GetCurrentBottleNum()
        {
            return DeviceParams.CurrentBottleNumber;
        }

        /// <summary>
        /// 读取最大采样瓶编号
        /// </summary>
        public int GetMaxBottleNum()
        {
            return 24;
        }

        #endregion

        #region IMixedSampleDeviceOperation

        /// <summary>
        /// 启动采样器操作
        /// </summary>
        /// <param name="operType">操作类型</param>
        [Description("启动采样器操作")]
        public void StartOper(eMixedSampleDeviceOperType operType)
        {
            try
            {
                switch(operType)
                {
                    case eMixedSampleDeviceOperType.A桶采水:
                        SetImmediateWaterSamplingA();
                        break;

                    case eMixedSampleDeviceOperType.B桶采水:
                        SetImmediateWaterSamplingB();
                        break;

                    case eMixedSampleDeviceOperType.A桶供样:
                        SetImmediateSupplyA();
                        break;

                    case eMixedSampleDeviceOperType.B桶供样:
                        SetImmediateSupplyB();
                        break;

                    case eMixedSampleDeviceOperType.A桶留样:
                        SetImmediateRetainA();
                        break;

                    case eMixedSampleDeviceOperType.B桶留样:
                        SetImmediateRetainB();
                        break;

                    case eMixedSampleDeviceOperType.A桶排空:
                        SetBucketADrain();
                        break;

                    case eMixedSampleDeviceOperType.B桶排空:
                        SetBucketBDrain();
                        break;

                    case eMixedSampleDeviceOperType.供样管路排空:
                        SetSupplyPipeDrain();
                        break;

                    case eMixedSampleDeviceOperType.复位排空:
                        SetImmediateStopAndReset();
                        break;

                    case eMixedSampleDeviceOperType.全部留样瓶排空:
                        SetAllBottlesDrain();
                        break;

                    case eMixedSampleDeviceOperType.全部留样瓶润洗:
                        SetAllBottlesRinse();
                        break;

                    default:
                        throw new ArgumentException($"不支持的操作类型：{operType}");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]启动操作[{operType}]失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取指定桶采水量
        /// </summary>
        /// <param name="samplingBucket"></param>
        /// <returns></returns>
        public int GetBucketWaterVolume(eSamplingBucket samplingBucket)
        {
            if(samplingBucket == eSamplingBucket.A桶)
            {
                return DeviceParams.BucketAWaterVolume;
            }
            else
            {
                return DeviceParams.BucketBWaterVolume;
            }
        }

        #endregion
    }
}