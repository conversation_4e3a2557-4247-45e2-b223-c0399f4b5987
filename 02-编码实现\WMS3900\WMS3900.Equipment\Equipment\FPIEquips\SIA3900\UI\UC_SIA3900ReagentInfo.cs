﻿using System;
using System.Collections.Generic;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    public partial class UC_SIA3900ReagentInfo : UIUserControl
    {
        #region 字段属性

        private SIA3900Equipment _device;

        /// <summary>
        /// 存储所有试剂子控件
        /// </summary>
        private List<UC_OneSIA3900ReagentInfo> _ucReagentInfoList = new List<UC_OneSIA3900ReagentInfo>();

        /// <summary>
        /// 核查液控件
        /// </summary>
        private UC_SIA3900CheckFluidInfo _ucCheckFluidInfo;

        #endregion

        #region 构造

        public UC_SIA3900ReagentInfo()
        {
            InitializeComponent();
        }

        #endregion

        #region 公共方法

        internal void SetTragetDevice(SIA3900Equipment device)
        {
            _device = device;
            InitUI();
            RefreshUI();
        }

        internal void RefreshUI()
        {
            foreach(var uc in _ucReagentInfoList)
            {
                uc.RefreshUI();
            }
            _ucCheckFluidInfo.RefreshUI();
        }

        #endregion

        #region 事件

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            RefreshUI();
        }

        #endregion

        #region 私有方法

        private void InitUI()
        {
            if(_device != null)
            {
                tabMain.Controls.Clear();

                for(int i = 0; i < _device.ReagentMaintenance.ReagentInfos.Count; i++)
                {
                    UC_OneSIA3900ReagentInfo reagentInfo = new (_device.ReagentMaintenance.ReagentInfos[i]);
                    _ucReagentInfoList.Add(reagentInfo);
                    tabMain.Controls.Add(reagentInfo, i % tabMain.ColumnCount, i / tabMain.ColumnCount);
                }

                _ucCheckFluidInfo = new(_device);          
                tabMain.Controls.Add(_ucCheckFluidInfo, 5, 1);
            }
        }

        #endregion
    }
}