﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Fpi.Communication.Manager;
using Fpi.HB.Business.Protocols.Helper;
using Fpi.HB.Business.Protocols.Interface;
using Fpi.Json;
using Fpi.Util.ObjectRelated;
using Newtonsoft.Json;

namespace Fpi.WMS3000.SystemConfig.SmartPatrol.Config
{
    /// <summary>
    /// 智能巡检管理类
    /// 巡检触发，过程控制，结果保存
    /// </summary>
    public class SmartPatrolManager : BaseJsonNode
    {
        #region 字段属性

        /// <summary>
        /// 数据传输通道编号
        /// </summary>
        public string UploadPipesId;

        /// <summary>
        /// 巡检单元列表
        /// </summary>
        [Description("巡检单元列表")]
        [JsonIgnore]
        public List<SingleUnitSmartPatrolBase> PatrolUnitList = new List<SingleUnitSmartPatrolBase>();

        /// <summary>
        /// 当前正在运行的单元的序号
        /// </summary>
        [Description("当前正在运行的单元的序号")]
        [JsonIgnore]
        public int CurrnetUnitIndex;

        /// <summary>
        /// 最新巡检结果
        /// </summary>
        [Description("最新巡检结果")]
        public SmartPatrolResult LatestSmartPatrolResult;

        /// <summary>
        /// 巡检状态
        /// </summary>
        [Description("巡检状态")]
        [JsonIgnore]
        public ePatrolState PatrolState;

        /// <summary>
        /// 任务取消Token
        /// </summary>
        [JsonIgnore]
        public CancellationTokenSource CancelToken;

        #endregion

        #region 单例

        private SmartPatrolManager()
        {
            // 图像算法单元
            PatrolUnitList.Add(new TXSFUnitSmartPatrol());
            // 控制单元
            PatrolUnitList.Add(new KZUnitSmartPatrol());
            // 采水单元
            PatrolUnitList.Add(new CSUnitSmartPatrol());
            // 预处理单元
            PatrolUnitList.Add(new YCLUnitSmartPatrol());
            // 配水单元
            PatrolUnitList.Add(new PSUnitSmartPatrol());
            // 分析单元
            PatrolUnitList.Add(new FXUnitSmartPatrol());
            // 质控单元
            PatrolUnitList.Add(new ZKUnitSmartPatrol());
            // 试剂存储单元
            PatrolUnitList.Add(new SJUnitSmartPatrol());
            // 留样单元
            PatrolUnitList.Add(new LYUnitSmartPatrol());
            // 辅助单元
            PatrolUnitList.Add(new FZUnitSmartPatrol());
            // 废液收集单元
            PatrolUnitList.Add(new FYUnitSmartPatrol());
            // 视频监控单元
            PatrolUnitList.Add(new SPUnitSmartPatrol());

            loadJson();
        }

        [JsonIgnore]
        private static readonly object lockObj = new object();

        [JsonIgnore]
        private static SmartPatrolManager _instance = null;

        public static SmartPatrolManager GetInstance()
        {
            lock(lockObj)
            {
                if(_instance == null)
                {
                    _instance = new SmartPatrolManager();
                }
            }

            return _instance;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 巡检状态锁
        /// </summary>
        [JsonIgnore]
        private static readonly object SyncObj = new object();

        /// <summary>
        /// 开始巡检流程
        /// </summary>
        public bool StartDoPatrol(ePatrolTriggerType patrolTriggerType)
        {
            lock(SyncObj)
            {
                // 当前巡检中，拒绝执行新任务
                if(PatrolState == ePatrolState.巡检中)
                {
                    return false;
                }
            }

            // 创建CancellationTokenSource对象。
            CancelToken = new CancellationTokenSource();

            // 异步执行巡检流程
            Task.Run(() =>
            {
                try
                {
                    // 新建巡检结果信息
                    LatestSmartPatrolResult = new SmartPatrolResult(patrolTriggerType);

                    lock(SyncObj)
                    {
                        PatrolState = ePatrolState.巡检中;
                    }

                    // 巡检开始
                    OnPatrolStartEvent();

                    // 遍历执行巡检
                    CurrnetUnitIndex = 0;
                    foreach(var model in PatrolUnitList)
                    {
                        // 判断流程有没有被手动取消
                        if(CancelToken.IsCancellationRequested)
                        {
                            throw new Exception();
                        }

                        // 巡检进度更新
                        CurrnetUnitIndex++;

                        // 巡检开始通知
                        OnSingleUnitPatrolStart(model);

                        // 执行巡检
                        model.StartPatrol();

                        // 界面触发时，每个模块检测完成延时几秒，使得巡检过程可视化
                        if(patrolTriggerType == ePatrolTriggerType.手动触发)
                        {
                            Thread.Sleep(1000);
                        }

                        // 模块巡检结果汇总到总结果区
                        if(model.PatrolResult != null)
                        {
                            LatestSmartPatrolResult.AddModelResult(model.PatrolResult);
                        }

                        // 巡检结束通知
                        OnSingleUnitPatrolEnd(model);
                    }

                    // 保存巡检结果信息到数据库
                    LatestSmartPatrolResult.SaveToDb();

                    // 打印结构属性描述，测试对接使用
                    //File.WriteAllText(Path.Combine(Application.StartupPath, "巡检信息结构.txt"), ObjectPrinter.PrintObjectProperties(LatestSmartPatrolResult));

                    // 上传数据到平台
                    UploadDataToPlatform();

                    lock(SyncObj)
                    {
                        PatrolState = ePatrolState.巡检完成;
                    }
                }
                catch(OperationCanceledException e)
                {
                    lock(SyncObj)
                    {
                        PatrolState = ePatrolState.巡检异常;
                    }
                    SmartPatrolLogHelper.WritePatrolLog($"智能巡检执行出错：触发中断巡检流程！");
                }
                catch(Exception e)
                {
                    lock(SyncObj)
                    {
                        PatrolState = ePatrolState.巡检异常;
                    }
                    SmartPatrolLogHelper.WritePatrolLog($"智能巡检执行出错：{e.Message}");
                }
                finally
                {
                    // 保存巡检结果到缓存
                    Save();

                    // 巡检结束
                    OnPatrolEndEvent();
                }
            });

            return true;
        }

        /// <summary>
        /// 中断巡检流程
        /// </summary>
        public void StopDoPatrol()
        {
            if(PatrolState == ePatrolState.巡检中)
            {
                CancelToken?.Cancel();
            }
        }

        #endregion

        #region 事件

        #region 整体

        /// <summary>
        /// 巡检开始
        /// </summary>
        public event Action PatrolStartEvent;

        /// <summary>
        /// 巡检结束
        /// </summary>
        public event Action PatrolEndEvent;

        /// <summary>
        /// 巡检开始
        /// </summary>
        /// <param name="model"></param>
        private void OnPatrolStartEvent()
        {
            try
            {
                if(PatrolStartEvent != null)
                {
                    PatrolStartEvent();
                }
            }
            catch
            {
            }
        }

        /// <summary>
        /// 巡检结束
        /// </summary>
        /// <param name="model"></param>
        private void OnPatrolEndEvent()
        {
            try
            {
                if(PatrolEndEvent != null)
                {
                    PatrolEndEvent();
                }
            }
            catch
            {
            }
        }

        #endregion

        #region 单元

        /// <summary>
        /// 某一单元巡检开始
        /// </summary>
        public event Action<SingleUnitSmartPatrolBase> SingleUnitPatrolStartEvent;

        /// <summary>
        /// 某一单元巡检结束
        /// </summary>
        public event Action<SingleUnitSmartPatrolBase> SingleUnitPatrolEndEvent;

        /// <summary>
        /// 某一单元巡检开始
        /// </summary>
        /// <param name="model"></param>
        private void OnSingleUnitPatrolStart(SingleUnitSmartPatrolBase model)
        {
            try
            {
                if(SingleUnitPatrolStartEvent != null)
                {
                    SingleUnitPatrolStartEvent(model);
                }
            }
            catch
            {
            }
        }

        /// <summary>
        /// 某一单元巡检结束
        /// </summary>
        /// <param name="model"></param>
        private void OnSingleUnitPatrolEnd(SingleUnitSmartPatrolBase model)
        {
            try
            {
                if(SingleUnitPatrolEndEvent != null)
                {
                    SingleUnitPatrolEndEvent(model);
                }
            }
            catch
            {
            }
        }

        #endregion

        /// <summary>
        /// 清空流程状态事件通知
        /// </summary>
        public void ClearEvent()
        {
            if(PatrolStartEvent != null)
            {
                Delegate[] delegAry = PatrolStartEvent.GetInvocationList();
                foreach(Delegate @delegate in delegAry)
                {
                    var deleg = (Action)@delegate;
                    PatrolStartEvent -= deleg;
                }
            }

            if(PatrolEndEvent != null)
            {
                Delegate[] delegAry = PatrolEndEvent.GetInvocationList();
                foreach(Delegate @delegate in delegAry)
                {
                    var deleg = (Action)@delegate;
                    PatrolEndEvent -= deleg;
                }
            }

            if(SingleUnitPatrolStartEvent != null)
            {
                Delegate[] delegAry = SingleUnitPatrolStartEvent.GetInvocationList();
                foreach(Delegate @delegate in delegAry)
                {
                    var deleg = (Action<SingleUnitSmartPatrolBase>)@delegate;
                    SingleUnitPatrolStartEvent -= deleg;
                }
            }

            if(SingleUnitPatrolEndEvent != null)
            {
                Delegate[] delegAry = SingleUnitPatrolEndEvent.GetInvocationList();
                foreach(Delegate @delegate in delegAry)
                {
                    var deleg = (Action<SingleUnitSmartPatrolBase>)@delegate;
                    SingleUnitPatrolEndEvent -= deleg;
                }
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 上传数据到平台
        /// </summary>
        private void UploadDataToPlatform()
        {
            if(!string.IsNullOrEmpty(UploadPipesId))
            {
                foreach(Pipe pipe in PortManager.GetInstance().pipes)
                {
                    // 通道启用，其类型正确
                    if(pipe.valid && pipe.id.ToLower().StartsWith("remote") && UploadPipesId.Contains(pipe.name) && pipe.Protocol.Sender is IDataUpload dataUpload && dataUpload.UploadDataType == typeof(eFpiHttpUploadDataType))
                    {
                        Task.Run(() =>
                        {
                            try
                            {
                                SmartPatrolLogHelper.WritePatrolLog($"开始使用{pipe.name}上传巡检报告...");
                                dataUpload.UploadData((int)eFpiHttpUploadDataType.智能巡检报告);
                                SmartPatrolLogHelper.WritePatrolLog($"使用{pipe.name}上传巡检报告成功！");
                            }
                            catch (Exception e)
                            {
                                SmartPatrolLogHelper.WritePatrolLog($"使用{pipe.name}上传巡检报告出错：{e.Message}");
                            }
                        });
                    }
                }
            }
        }

        #endregion
    }
}