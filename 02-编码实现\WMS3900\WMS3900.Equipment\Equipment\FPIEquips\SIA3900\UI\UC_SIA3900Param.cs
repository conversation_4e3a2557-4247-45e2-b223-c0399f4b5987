﻿using System;
using System.Windows.Forms;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    public partial class UC_SIA3900Param : UIUserControl
    {
        #region 字段属性

        private SIA3900Equipment _device;

        #endregion

        #region 构造

        public UC_SIA3900Param()
        {
            InitializeComponent();
        }

        public UC_SIA3900Param(SIA3900Equipment device)
           : this()
        {
            _device = device;
        }

        #endregion

        #region 事件

        private void UC_SIA3900Param_Load(object sender, EventArgs e)
        {
            uc_SIA3900DeviceState.SetTragetDevice(_device);
            uc_SIA3900StateAlarm.SetTragetParams(_device.StateAlarm, new Padding(35, 0, 0, 5));
            uc_SIA3900MeasureParam.SetTragetParams(_device.MeasureParams, new Padding(35, 0, 0, 5));
            uc_SIA3900DeviceKeyParams.SetTragetParams(_device.DeviceKeyParams, new Padding(35, 0, 0, 5));
            uc_SIA3900DeviceOperControl.SetTragetDevice(_device);
            uc_SIA3900DeviceParamsSet.SetTragetDevice(_device);
            uc_SIA3900CalibrateCoefficientDetail.SetTragetDevice(_device);
            uc_SIA3900ReagentInfo.SetTragetDevice(_device);          
            uc_SIA3900HistoryDate.SetTragetParams(_device.HistoryDate);
            uc_SIA3900CurrentLog.SetRelatedLogs(_device.LogArea.LogQueue);
            uc_DeviceAllAlarm.SetAlarmSource(_device?.AlarmSourceId, new Padding(45, 0, 0, 5));
            uc_SIA3900HistoryLog.SetTragetDevice(_device);
            uc_DeviceLifeMonitor.SetTragetDevice(_device);
            uc_DiagnosisRecords.SetTragetDevice(_device);
        }

        #endregion
    }
}