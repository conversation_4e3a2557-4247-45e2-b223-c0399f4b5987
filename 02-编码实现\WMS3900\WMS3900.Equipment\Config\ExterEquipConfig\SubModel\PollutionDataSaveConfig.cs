﻿using System.ComponentModel;
using Fpi.Data.Config;
using Newtonsoft.Json;

namespace Fpi.WMS3000.Equipment.Config
{
    /// <summary>
    /// 污染源数据存储配置
    /// </summary>
    public class PollutionDataSaveConfig
    {
        #region 可存储属性

        /// <summary>
        /// 分钟小时数据计算方式
        /// </summary>
        public eDataCalculateMethod MinuteDataCalculateMethod { get; set; }

        /// <summary>
        /// 日数据计算方式
        /// </summary>
        public eDataCalculateMethod DayDataCalculateMethod { get; set; }

        /// <summary>
        /// 累计流量因子编码
        /// </summary>
        [Description("累计流量因子编码")]
        public string TotalFlowNodeId { get; set; }

        #endregion

        #region 无需存储属性

        /// <summary>
        /// 累计流量因子
        /// </summary>
        [Description("累计流量因子")]
        [JsonIgnore]
        public ValueNode TotalFlowNode => DataManager.GetInstance().GetValueNodeById(TotalFlowNodeId);

        #endregion
    }
}