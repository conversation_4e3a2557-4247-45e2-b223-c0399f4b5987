﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{63A37282-FEA3-4F07-98F1-164045B58D8B}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Fpi.WMS3000.Equipment</RootNamespace>
    <AssemblyName>Fpi.WMS3900.Equipment</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\Product\Debug\bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\..\Product\Release\bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevComponents.DotNetBar2, Version=*********, Culture=neutral, PublicKeyToken=c39c3242a43eee2b, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\DevComponents.DotNetBar2.dll</HintPath>
    </Reference>
    <Reference Include="HZH_Controls">
      <HintPath>..\FpiDLL\HZH_Controls.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.JScript">
      <HintPath>C:\Windows\Microsoft.NET\Framework\v2.0.50727\Microsoft.JScript.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=*******, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\SunnyUI.dll</HintPath>
    </Reference>
    <Reference Include="SunnyUI.Common, Version=*******, Culture=neutral, PublicKeyToken=5a271fb7ba597231, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\SunnyUI.Common.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Common\CommonFunctionHelper.cs" />
    <Compile Include="Common\DataConvertHelper.cs" />
    <Compile Include="Common\CustomAttribute\VisibleAttribute.cs" />
    <Compile Include="Common\Enums.cs" />
    <Compile Include="Common\DeviceLogInfo.cs" />
    <Compile Include="Common\UI\UC\UC_OneNodeValue.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Common\UI\UC\UC_OneNodeValue.Designer.cs">
      <DependentUpon>UC_OneNodeValue.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\UI\UC_DeviceHistoryLogQuery.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Common\UI\UC_DeviceHistoryLogQuery.Designer.cs">
      <DependentUpon>UC_DeviceHistoryLogQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\UI\UC\UC_OneParamData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Common\UI\UC\UC_OneParamData.Designer.cs">
      <DependentUpon>UC_OneParamData.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\UI\UC\UC_OneAlarmState.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Common\UI\UC\UC_OneAlarmState.Designer.cs">
      <DependentUpon>UC_OneAlarmState.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\UI\UC_DeviceAllAlarm.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Common\UI\UC_DeviceAllAlarm.Designer.cs">
      <DependentUpon>UC_DeviceAllAlarm.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\UI\UC_DeviceDataChannels.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Common\UI\UC_DeviceDataChannels.Designer.cs">
      <DependentUpon>UC_DeviceDataChannels.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\UI\UC_DeviceParamData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Common\UI\UC_DeviceParamData.Designer.cs">
      <DependentUpon>UC_DeviceParamData.cs</DependentUpon>
    </Compile>
    <Compile Include="Config\Enums.cs" />
    <Compile Include="Config\ExterEquipConfig\ExterEquipConfigManager.cs" />
    <Compile Include="Config\ExterEquipConfig\SubModel\CameraSelectConfig.cs" />
    <Compile Include="Config\ExterEquipConfig\SubModel\EntranceSelectConfig.cs" />
    <Compile Include="Config\ExterEquipConfig\SubModel\PollutionDataSaveConfig.cs" />
    <Compile Include="Config\ExterEquipConfig\SubModel\StationEnvConfig.cs" />
    <Compile Include="Config\ExterEquipConfig\SubModel\WaterDistributeConfig.cs" />
    <Compile Include="Config\GlobalData\GlobalDataCache.cs" />
    <Compile Include="Config\GlobalData\ModuleState\StationModuleState.cs" />
    <Compile Include="Config\GlobalData\ModuleState\WaterPretreatmentModuleState.cs" />
    <Compile Include="Config\GlobalData\ModuleState\WaterDistributionModuleState.cs" />
    <Compile Include="Config\GlobalData\ModuleState\WaterCollectionModuleState.cs" />
    <Compile Include="Config\ExterEquipConfig\SubModel\AirConditioningConfig.cs" />
    <Compile Include="Config\ExterEquipConfig\SubModel\DeviceSelectConfig.cs" />
    <Compile Include="Config\ExterEquipConfig\SubModel\AirVentilatorConfig.cs" />
    <Compile Include="Config\ExterEquipConfig\SubModel\AlarmLimitConfig.cs" />
    <Compile Include="Config\ExterEquipConfig\SubModel\DehumidifierConfig.cs" />
    <Compile Include="Config\ExterEquipConfig\SubModel\WaterPretreatmentConfig.cs" />
    <Compile Include="Config\ExterEquipConfig\SubModel\WaterCollectionConfig.cs" />
    <Compile Include="Config\ExterEquipConfig\SubModel\WeightConfig.cs" />
    <Compile Include="Config\ExterEquipConfig\SubModel\PduConfig.cs" />
    <Compile Include="Config\GlobalData\StationStateInfo.cs" />
    <Compile Include="Config\SystemAlarm\SystemAlarmHelper.cs" />
    <Compile Include="Equipment\ExterEquips\DLEquip\UI\UC\UC_OneTempNodeValue.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\ExterEquips\DLEquip\UI\UC\UC_OneTempNodeValue.Designer.cs">
      <DependentUpon>UC_OneTempNodeValue.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\ExterEquips\DLEquip\UI\UC_DL10BDeviceState.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\ExterEquips\DLEquip\UI\UC_DL10BDeviceState.Designer.cs">
      <DependentUpon>UC_DL10BDeviceState.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\ExterEquips\DSEquip\DS6CNZDESEquipment.cs" />
    <Compile Include="Equipment\ExterEquips\DSEquip\Params\DS6CNZDESParam.cs" />
    <Compile Include="Equipment\ExterEquips\DSEquip\UI\UC_DS6CNZDESDeviceState.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\ExterEquips\DSEquip\UI\UC_DS6CNZDESDeviceState.Designer.cs">
      <DependentUpon>UC_DS6CNZDESDeviceState.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\ExterEquips\JDRKEquip\JDRKRSEquip.cs" />
    <Compile Include="Equipment\ExterEquips\JDRKEquip\Params\JDRKRSParam.cs" />
    <Compile Include="Equipment\ExterEquips\JDRKEquip\UI\UC_JDRKRSDeviceState.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\ExterEquips\JDRKEquip\UI\UC_JDRKRSDeviceState.Designer.cs">
      <DependentUpon>UC_JDRKRSDeviceState.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\ExterEquips\JiuBoEquip\JiuBoWL1A2Equipment.cs" />
    <Compile Include="Equipment\ExterEquips\LJEquip\UI\UC\UC_OneRoadWeightParam.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\ExterEquips\LJEquip\UI\UC\UC_OneRoadWeightParam.Designer.cs">
      <DependentUpon>UC_OneRoadWeightParam.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\ExterEquips\LXEquip\LXAM125Equipment.cs" />
    <Compile Include="Equipment\ExterEquips\LXEquip\Params\AM125Param.cs" />
    <Compile Include="Equipment\ExterEquips\DLEquip\DL10BEquipment.cs" />
    <Compile Include="Equipment\ExterEquips\LJEquip\LJWD200Equipment.cs" />
    <Compile Include="Equipment\ExterEquips\LJEquip\Params\LJWD200WeightParam.cs" />
    <Compile Include="Equipment\ExterEquips\LXEquip\UI\UC_LXAM125DeviceState.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\ExterEquips\LXEquip\UI\UC_LXAM125DeviceState.Designer.cs">
      <DependentUpon>UC_LXAM125DeviceState.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\ExterEquips\SHEquip\SHSWQ7300Equip.cs" />
    <Compile Include="Equipment\ExterEquips\TPRSEquip\UI\UC\UC_OneRoadPduParam.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\ExterEquips\TPRSEquip\UI\UC\UC_OneRoadPduParam.Designer.cs">
      <DependentUpon>UC_OneRoadPduParam.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\ExterEquips\YstEquip\Params\YstEA900Param.cs" />
    <Compile Include="Equipment\ExterEquips\YstEquip\UI\UC_YstEA900DeviceState.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\ExterEquips\YstEquip\UI\UC_YstEA900DeviceState.Designer.cs">
      <DependentUpon>UC_YstEA900DeviceState.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\ExterEquips\ZTEquip\Config\NB2EnumDefine.cs" />
    <Compile Include="Equipment\ExterEquips\ZTEquip\UI\UC_ZTNB2LEDeviceState.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\ExterEquips\ZTEquip\UI\UC_ZTNB2LEDeviceState.Designer.cs">
      <DependentUpon>UC_ZTNB2LEDeviceState.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\ExterEquips\ZTEquip\ZTNB2LEEquipment.cs" />
    <Compile Include="Equipment\ExterEquips\ZTEquip\Params\NB2Param.cs" />
    <Compile Include="Equipment\ExterEquips\TPRSEquip\Params\PduElectricParam.cs" />
    <Compile Include="Equipment\ExterEquips\TPRSEquip\TPRSXC3007Equipment.cs" />
    <Compile Include="Equipment\ExterEquips\SHEquip\SHTur300Equip.cs" />
    <Compile Include="Equipment\ExterEquips\ZZEquip\ZZWL300Equip.cs" />
    <Compile Include="Equipment\FPIEquips\QCD3900\Config\QCD3900EnumDefine.cs" />
    <Compile Include="Equipment\FPIEquips\QCD3900\Params\QCD3900LogArea.cs" />
    <Compile Include="Equipment\FPIEquips\QCD3900\Params\QCD3900MeasureParam.cs" />
    <Compile Include="Equipment\FPIEquips\QCD3900\Params\QCD3900ElementLifeInfo.cs" />
    <Compile Include="Equipment\FPIEquips\QCD3900\UI\UC\UC_OneElementLifeInfo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\QCD3900\UI\UC\UC_OneElementLifeInfo.Designer.cs">
      <DependentUpon>UC_OneElementLifeInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\QCD3900\UI\UC\UC_OneQCD3900ElementControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\QCD3900\UI\UC\UC_OneQCD3900ElementControl.Designer.cs">
      <DependentUpon>UC_OneQCD3900ElementControl.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\QCD3900\UI\UC\UC_OneQCD3900LiquidInfo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\QCD3900\UI\UC\UC_OneQCD3900LiquidInfo.Designer.cs">
      <DependentUpon>UC_OneQCD3900LiquidInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900CompInfo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900CompInfo.Designer.cs">
      <DependentUpon>UC_QCD3900CompInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900FlowControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900FlowControl.Designer.cs">
      <DependentUpon>UC_QCD3900FlowControl.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900Maintain.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900Maintain.Designer.cs">
      <DependentUpon>UC_QCD3900Maintain.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900ElementControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900ElementControl.Designer.cs">
      <DependentUpon>UC_QCD3900ElementControl.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900MeasureParam.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900MeasureParam.Designer.cs">
      <DependentUpon>UC_QCD3900MeasureParam.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900LiquidInfo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900LiquidInfo.Designer.cs">
      <DependentUpon>UC_QCD3900LiquidInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900Param.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900Param.Designer.cs">
      <DependentUpon>UC_QCD3900Param.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\Config\SIA3900EnumDefine.cs" />
    <Compile Include="Equipment\FPIEquips\SIA3900\Params\SIA3900CalibrateCoefficient.cs" />
    <Compile Include="Equipment\FPIEquips\SIA3900\Params\SIA3900CalibrateCoefficientDetail.cs" />
    <Compile Include="Equipment\FPIEquips\SIA3900\Params\SIA3900DeviceKeyParams.cs" />
    <Compile Include="Equipment\FPIEquips\SIA3900\Params\SIA3900DiagnosisRecords.cs" />
    <Compile Include="Equipment\FPIEquips\SIA3900\Params\SIA3900HistoryDate.cs" />
    <Compile Include="Equipment\FPIEquips\SIA3900\Params\SIA3900HomePageStatus.cs" />
    <Compile Include="Equipment\FPIEquips\SIA3900\Params\SIA3900LifeMonitor.cs" />
    <Compile Include="Equipment\FPIEquips\SIA3900\Params\SIA3900LogArea.cs" />
    <Compile Include="Equipment\FPIEquips\SIA3900\Params\SIA3900MeasureParam.cs" />
    <Compile Include="Equipment\FPIEquips\SIA3900\Params\SIA3900ReagentMaintenance.cs" />
    <Compile Include="Equipment\FPIEquips\SIA3900\Params\SIA3900StateAlarm.cs" />
    <Compile Include="Equipment\FPIEquips\SIA3900\SIA3900Equipment.cs" />
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\FrmSIA3900CalibrateCoefficientData.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\FrmSIA3900CalibrateCoefficientData.Designer.cs">
      <DependentUpon>FrmSIA3900CalibrateCoefficientData.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\FrmSIA3900KeyParams.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\FrmSIA3900KeyParams.Designer.cs">
      <DependentUpon>FrmSIA3900KeyParams.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\UC\UC_OneSIA3900ReagentInfo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\UC\UC_OneSIA3900ReagentInfo.Designer.cs">
      <DependentUpon>UC_OneSIA3900ReagentInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\UC\UC_SIA3900CheckFluidInfo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\UC\UC_SIA3900CheckFluidInfo.Designer.cs">
      <DependentUpon>UC_SIA3900CheckFluidInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\UC\UC_SIA3900DiagnosisQueryData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\UC\UC_SIA3900DiagnosisQueryData.Designer.cs">
      <DependentUpon>UC_SIA3900DiagnosisQueryData.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900CalibrateCoefficientDetail.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900CalibrateCoefficientDetail.Designer.cs">
      <DependentUpon>UC_SIA3900CalibrateCoefficientDetail.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900DeviceLifeMonitor.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900DeviceLifeMonitor.Designer.cs">
      <DependentUpon>UC_SIA3900DeviceLifeMonitor.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900DeviceOperControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900DeviceOperControl.Designer.cs">
      <DependentUpon>UC_SIA3900DeviceOperControl.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900DeviceParamsSet.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900DeviceParamsSet.Designer.cs">
      <DependentUpon>UC_SIA3900DeviceParamsSet.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900DeviceState.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900DeviceState.Designer.cs">
      <DependentUpon>UC_SIA3900DeviceState.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\UI\UC_DeviceCurrentLogQuery.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Common\UI\UC_DeviceCurrentLogQuery.Designer.cs">
      <DependentUpon>UC_DeviceCurrentLogQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900DiagnosisRecords.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900DiagnosisRecords.Designer.cs">
      <DependentUpon>UC_SIA3900DiagnosisRecords.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900Param.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900Param.Designer.cs">
      <DependentUpon>UC_SIA3900Param.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900ReagentInfo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900ReagentInfo.Designer.cs">
      <DependentUpon>UC_SIA3900ReagentInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\Config\WCS3900EnumDefine.cs" />
    <Compile Include="Equipment\FPIEquips\WCS3900\Params\MeasureParam\WCS3900CommonParam.cs" />
    <Compile Include="Equipment\FPIEquips\WCS3900\Params\MeasureParam\WCS3900MeasureParamBase.cs" />
    <Compile Include="Equipment\FPIEquips\WCS3900\Params\MeasureParam\WCS3900TempMeasureParam.cs" />
    <Compile Include="Equipment\FPIEquips\WCS3900\Params\MeasureParam\WCS3900TurbMeasureParam.cs" />
    <Compile Include="Equipment\FPIEquips\WCS3900\Params\MeasureParam\WCS3900ConduMeasureParam.cs" />
    <Compile Include="Equipment\FPIEquips\WCS3900\Params\MeasureParam\WCS3900OxyMeasureParam.cs" />
    <Compile Include="Equipment\FPIEquips\WCS3900\Params\MeasureParam\WCS3900PHMeasureParam.cs" />
    <Compile Include="Equipment\FPIEquips\WCS3900\Params\WCS3900ElementLifeInfo.cs" />
    <Compile Include="Equipment\FPIEquips\WCS3900\Params\WCS3900LogArea.cs" />
    <Compile Include="Equipment\FPIEquips\WCS3900\Params\WCS3900DeviceStateParam.cs" />
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\MeasureParam\UC_WCS3900MeasureData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\MeasureParam\UC_WCS3900MeasureData.Designer.cs">
      <DependentUpon>UC_WCS3900MeasureData.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\MeasureParam\UC_WCS3900CheckData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\MeasureParam\UC_WCS3900CheckData.Designer.cs">
      <DependentUpon>UC_WCS3900CheckData.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\UC\UC_OneWCS3900CheckParam.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\UC\UC_OneWCS3900CheckParam.Designer.cs">
      <DependentUpon>UC_OneWCS3900CheckParam.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\UC\UC_OneWCS3900ElementState.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\UC\UC_OneWCS3900ElementState.Designer.cs">
      <DependentUpon>UC_OneWCS3900ElementState.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\UC\UC_OneWCS3900LiquidInfo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\UC\UC_OneWCS3900LiquidInfo.Designer.cs">
      <DependentUpon>UC_OneWCS3900LiquidInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\UC\UC_OneWCS3900LevelState.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\UC\UC_OneWCS3900LevelState.Designer.cs">
      <DependentUpon>UC_OneWCS3900LevelState.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\UC\UC_OneWCS3900ElementControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\UC\UC_OneWCS3900ElementControl.Designer.cs">
      <DependentUpon>UC_OneWCS3900ElementControl.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\UC\UC_OneWCS3900NodeParam.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\UC\UC_OneWCS3900NodeParam.Designer.cs">
      <DependentUpon>UC_OneWCS3900NodeParam.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\UC\UC_OneWMS3900ReagentInfo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\UC\UC_OneWMS3900ReagentInfo.Designer.cs">
      <DependentUpon>UC_OneWMS3900ReagentInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\UC_WCS3900AllMeasureData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\UC_WCS3900AllMeasureData.Designer.cs">
      <DependentUpon>UC_WCS3900AllMeasureData.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\UC_WCS3900DeviceState.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\UC_WCS3900DeviceState.Designer.cs">
      <DependentUpon>UC_WCS3900DeviceState.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\UC_WCS3900Maintain.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\UC_WCS3900Maintain.Designer.cs">
      <DependentUpon>UC_WCS3900Maintain.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\UC_WCS3900Param.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\UI\UC_WCS3900Param.Designer.cs">
      <DependentUpon>UC_WCS3900Param.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\WCS3900\WCS3900Equip.cs" />
    <Compile Include="Equipment\HDSampleEquip\ZSC-IXC\HDZSC_IXCSampleEquip.cs" />
    <Compile Include="Equipment\HDSampleEquip\ZSC-IXC\Params\SampleDoorLogHelper.cs" />
    <Compile Include="Equipment\HDSampleEquip\ZSC-IXC\Params\HDZSCIXCParam.cs" />
    <Compile Include="Equipment\HDSampleEquip\ZSC-IXC\UI\UC_SampleDoorLogQuery.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\HDSampleEquip\ZSC-IXC\UI\UC_SampleDoorLogQuery.Designer.cs">
      <DependentUpon>UC_SampleDoorLogQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\HDSampleEquip\ZSC-IXC\UI\UC_HDZSCIXCDeviceOperControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\HDSampleEquip\ZSC-IXC\UI\UC_HDZSCIXCDeviceOperControl.Designer.cs">
      <DependentUpon>UC_HDZSCIXCDeviceOperControl.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\HDSampleEquip\ZSC-IXC\UI\UC_HDZSCIXCParam.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\HDSampleEquip\ZSC-IXC\UI\UC_HDZSCIXCParam.Designer.cs">
      <DependentUpon>UC_HDZSCIXCParam.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\HolliasEquip\PLC_Hollias_2012.cs" />
    <Compile Include="Equipment\HolliasEquip\UI\UC_Hollias2012Param.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\HolliasEquip\UI\UC_Hollias2012Param.designer.cs">
      <DependentUpon>UC_Hollias2012Param.cs</DependentUpon>
    </Compile>
    <Compile Include="Interface\IMeasureDeviceOperation.cs" />
    <Compile Include="Equipment\FPIEquips\QCD3900\Params\QCD3900DeviceStateParam.cs" />
    <Compile Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900DeviceState.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900DeviceState.Designer.cs">
      <DependentUpon>UC_QCD3900DeviceState.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\GB2005RecvEquip\GB212RecvEquipment.cs" />
    <Compile Include="Equipment\HDSampleEquip\ZSC-VII\HDZSC_VIISampleEquip.cs" />
    <Compile Include="Equipment\HDSampleEquip\ZSC-VII\UI\UC_HDZSCVIIParam.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Equipment\HDSampleEquip\ZSC-VII\UI\UC_HDZSCVIIParam.designer.cs">
      <DependentUpon>UC_HDZSCVIIParam.cs</DependentUpon>
    </Compile>
    <Compile Include="Equipment\FPIEquips\QCD3900\QCD3900Equip.cs" />
    <Compile Include="Equipment\ExterEquips\YstEquip\YstEA900UpsEquipment.cs" />
    <Compile Include="Interface\IAirConditionControl.cs" />
    <Compile Include="Interface\IRefreshUI.cs" />
    <Compile Include="Interface\IProbeWash.cs" />
    <Compile Include="Common\EventHandlerDefine.cs" />
    <Compile Include="Interface\IQCDataGet.cs" />
    <Compile Include="Interface\IDeviceRangeSet.cs" />
    <Compile Include="Interface\IDeviceNotify.cs" />
    <Compile Include="Interface\IMixedSampleDeviceOperation.cs" />
    <Compile Include="Interface\ISampleBottleNum.cs" />
    <Compile Include="Interface\ISIA3900DeviceParamTransfer.cs" />
    <Compile Include="Interface\IDeviceKeyParams.cs" />
    <Compile Include="Interface\IWCSDeviceOperation.cs" />
    <Compile Include="Interface\IQCDeviceOperation.cs" />
    <Compile Include="Interface\ISampleDeviceOperation.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Fpi.Alarm\Fpi.Alarm.csproj">
      <Project>{E714875C-0EC1-4C0F-8571-D0F631430C82}</Project>
      <Name>Fpi.Alarm</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Camera\Fpi.Camera.csproj">
      <Project>{1007e2b0-01aa-4bc4-9753-29e75615c1a0}</Project>
      <Name>Fpi.Camera</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Communication\Fpi.Communication.csproj">
      <Project>{D95F58B1-2E07-4D52-BA26-3F9B6EEACF29}</Project>
      <Name>Fpi.Communication</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Data\Fpi.Data.csproj">
      <Project>{07B7E9D5-5D00-4815-9409-0D7466A09F96}</Project>
      <Name>Fpi.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.DB\Fpi.DB.csproj">
      <Project>{89D85957-BA9E-4BD9-99FE-7B73B6176A6F}</Project>
      <Name>Fpi.DB</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Device\Fpi.Device.csproj">
      <Project>{88FEF5D2-E039-4AC0-942B-442F23755978}</Project>
      <Name>Fpi.Device</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Entrance\Fpi.Entrance.csproj">
      <Project>{1a61d7d4-aedd-487f-84d9-7ae75b3b1aef}</Project>
      <Name>Fpi.Entrance</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.HB.Business\Fpi.HB.Business.csproj">
      <Project>{13650425-1448-4DF5-884F-B7CD466ECB24}</Project>
      <Name>Fpi.HB.Business</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Instrument\Fpi.Instrument.csproj">
      <Project>{E8D1EB85-2B23-4622-8CDE-80D5F850CC74}</Project>
      <Name>Fpi.Instrument</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Json\Fpi.Json.csproj">
      <Project>{958c97c1-360f-4434-9c37-6c6030eb5fcd}</Project>
      <Name>Fpi.Json</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Log\Fpi.Log.csproj">
      <Project>{C7C2425F-8926-43C6-996E-47205531C604}</Project>
      <Name>Fpi.Log</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Operation\Fpi.Operation.csproj">
      <Project>{1657672d-6fba-47a5-8c40-0da507d578f2}</Project>
      <Name>Fpi.Operation</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Timer\Fpi.Timer.csproj">
      <Project>{1DC3DD73-A4F5-4CA4-96D3-43712267C864}</Project>
      <Name>Fpi.Timer</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.Common\Fpi.UI.Common.csproj">
      <Project>{C238E665-75B4-4EDA-B574-A37F2794BA54}</Project>
      <Name>Fpi.UI.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.PC\Fpi.UI.PC.csproj">
      <Project>{2D502016-B3B3-43FF-9BAE-AD1D2A18D42E}</Project>
      <Name>Fpi.UI.PC</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.User\Fpi.User.csproj">
      <Project>{7d3e1d03-9f81-4b4a-b6b3-334d829db1f4}</Project>
      <Name>Fpi.User</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Util\Fpi.Util.csproj">
      <Project>{6E37D7B3-8D08-4EF3-A924-3B87982AB246}</Project>
      <Name>Fpi.Util</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Xml\Fpi.Xml.csproj">
      <Project>{3AF9654D-39EE-4BE9-8553-A9BB9B83A33B}</Project>
      <Name>Fpi.Xml</Name>
    </ProjectReference>
    <ProjectReference Include="..\WMS3900.DB\Fpi.WMS3900.DB.csproj">
      <Project>{4e6961d6-ba42-4cb1-89a7-c25557a2f82a}</Project>
      <Name>Fpi.WMS3900.DB</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Common\UI\UC\UC_OneNodeValue.resx">
      <DependentUpon>UC_OneNodeValue.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Common\UI\UC_DeviceHistoryLogQuery.resx">
      <DependentUpon>UC_DeviceHistoryLogQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Common\UI\UC\UC_OneParamData.resx">
      <DependentUpon>UC_OneParamData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Common\UI\UC\UC_OneAlarmState.resx">
      <DependentUpon>UC_OneAlarmState.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Common\UI\UC_DeviceAllAlarm.resx">
      <DependentUpon>UC_DeviceAllAlarm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Common\UI\UC_DeviceDataChannels.resx">
      <DependentUpon>UC_DeviceDataChannels.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Common\UI\UC_DeviceParamData.resx">
      <DependentUpon>UC_DeviceParamData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\ExterEquips\DLEquip\UI\UC\UC_OneTempNodeValue.resx">
      <DependentUpon>UC_OneTempNodeValue.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\ExterEquips\DLEquip\UI\UC_DL10BDeviceState.resx">
      <DependentUpon>UC_DL10BDeviceState.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\ExterEquips\DSEquip\UI\UC_DS6CNZDESDeviceState.resx">
      <DependentUpon>UC_DS6CNZDESDeviceState.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\ExterEquips\JDRKEquip\UI\UC_JDRKRSDeviceState.resx">
      <DependentUpon>UC_JDRKRSDeviceState.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\ExterEquips\LJEquip\UI\UC\UC_OneRoadWeightParam.resx">
      <DependentUpon>UC_OneRoadWeightParam.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\ExterEquips\LXEquip\UI\UC_LXAM125DeviceState.resx">
      <DependentUpon>UC_LXAM125DeviceState.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\ExterEquips\TPRSEquip\UI\UC\UC_OneRoadPduParam.resx">
      <DependentUpon>UC_OneRoadPduParam.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\ExterEquips\YstEquip\UI\UC_YstEA900DeviceState.resx">
      <DependentUpon>UC_YstEA900DeviceState.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\ExterEquips\ZTEquip\UI\UC_ZTNB2LEDeviceState.resx">
      <DependentUpon>UC_ZTNB2LEDeviceState.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\QCD3900\UI\UC\UC_OneElementLifeInfo.resx">
      <DependentUpon>UC_OneElementLifeInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\QCD3900\UI\UC\UC_OneQCD3900ElementControl.resx">
      <DependentUpon>UC_OneQCD3900ElementControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\QCD3900\UI\UC\UC_OneQCD3900LiquidInfo.resx">
      <DependentUpon>UC_OneQCD3900LiquidInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900CompInfo.resx">
      <DependentUpon>UC_QCD3900CompInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900FlowControl.resx">
      <DependentUpon>UC_QCD3900FlowControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900Maintain.resx">
      <DependentUpon>UC_QCD3900Maintain.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900ElementControl.resx">
      <DependentUpon>UC_QCD3900ElementControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900MeasureParam.resx">
      <DependentUpon>UC_QCD3900MeasureParam.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900LiquidInfo.resx">
      <DependentUpon>UC_QCD3900LiquidInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900DeviceState.resx">
      <DependentUpon>UC_QCD3900DeviceState.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\QCD3900\UI\UC_QCD3900Param.resx">
      <DependentUpon>UC_QCD3900Param.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\SIA3900\UI\FrmSIA3900CalibrateCoefficientData.resx">
      <DependentUpon>FrmSIA3900CalibrateCoefficientData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\SIA3900\UI\FrmSIA3900KeyParams.resx">
      <DependentUpon>FrmSIA3900KeyParams.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\SIA3900\UI\UC\UC_OneSIA3900ReagentInfo.resx">
      <DependentUpon>UC_OneSIA3900ReagentInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\SIA3900\UI\UC\UC_SIA3900CheckFluidInfo.resx">
      <DependentUpon>UC_SIA3900CheckFluidInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\SIA3900\UI\UC\UC_SIA3900DiagnosisQueryData.resx">
      <DependentUpon>UC_SIA3900DiagnosisQueryData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900CalibrateCoefficientDetail.resx">
      <DependentUpon>UC_SIA3900CalibrateCoefficientDetail.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900DeviceLifeMonitor.resx">
      <DependentUpon>UC_SIA3900DeviceLifeMonitor.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900DeviceOperControl.resx">
      <DependentUpon>UC_SIA3900DeviceOperControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900DeviceParamsSet.resx">
      <DependentUpon>UC_SIA3900DeviceParamsSet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900DeviceState.resx">
      <DependentUpon>UC_SIA3900DeviceState.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Common\UI\UC_DeviceCurrentLogQuery.resx">
      <DependentUpon>UC_DeviceCurrentLogQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900DiagnosisRecords.resx">
      <DependentUpon>UC_SIA3900DiagnosisRecords.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900Param.resx">
      <DependentUpon>UC_SIA3900Param.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\SIA3900\UI\UC_SIA3900ReagentInfo.resx">
      <DependentUpon>UC_SIA3900ReagentInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\WCS3900\UI\MeasureParam\UC_WCS3900MeasureData.resx">
      <DependentUpon>UC_WCS3900MeasureData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\WCS3900\UI\MeasureParam\UC_WCS3900CheckData.resx">
      <DependentUpon>UC_WCS3900CheckData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\WCS3900\UI\UC\UC_OneWCS3900CheckParam.resx">
      <DependentUpon>UC_OneWCS3900CheckParam.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\WCS3900\UI\UC\UC_OneWCS3900ElementState.resx">
      <DependentUpon>UC_OneWCS3900ElementState.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\WCS3900\UI\UC\UC_OneWCS3900LiquidInfo.resx">
      <DependentUpon>UC_OneWCS3900LiquidInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\WCS3900\UI\UC\UC_OneWCS3900LevelState.resx">
      <DependentUpon>UC_OneWCS3900LevelState.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\WCS3900\UI\UC\UC_OneWCS3900ElementControl.resx">
      <DependentUpon>UC_OneWCS3900ElementControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\WCS3900\UI\UC\UC_OneWCS3900NodeParam.resx">
      <DependentUpon>UC_OneWCS3900NodeParam.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\WCS3900\UI\UC\UC_OneWMS3900ReagentInfo.resx">
      <DependentUpon>UC_OneWMS3900ReagentInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\WCS3900\UI\UC_WCS3900AllMeasureData.resx">
      <DependentUpon>UC_WCS3900AllMeasureData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\WCS3900\UI\UC_WCS3900DeviceState.resx">
      <DependentUpon>UC_WCS3900DeviceState.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\WCS3900\UI\UC_WCS3900Maintain.resx">
      <DependentUpon>UC_WCS3900Maintain.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\FPIEquips\WCS3900\UI\UC_WCS3900Param.resx">
      <DependentUpon>UC_WCS3900Param.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\HDSampleEquip\ZSC-IXC\UI\UC_SampleDoorLogQuery.resx">
      <DependentUpon>UC_SampleDoorLogQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\HDSampleEquip\ZSC-IXC\UI\UC_HDZSCIXCDeviceOperControl.resx">
      <DependentUpon>UC_HDZSCIXCDeviceOperControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\HDSampleEquip\ZSC-IXC\UI\UC_HDZSCIXCParam.resx">
      <DependentUpon>UC_HDZSCIXCParam.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\HDSampleEquip\ZSC-VII\UI\UC_HDZSCVIIParam.resx">
      <DependentUpon>UC_HDZSCVIIParam.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Equipment\HolliasEquip\UI\UC_Hollias2012Param.resx">
      <DependentUpon>UC_Hollias2012Param.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>