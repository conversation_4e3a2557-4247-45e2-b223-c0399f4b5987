﻿using System;
using Fpi.WMS3000.Equipment.WCS3900;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    public partial class UC_OneWMS3900ReagentInfo : UIUserControl
    {
        #region 属性字段

        /// <summary>
        /// 公共寄存器类型
        /// </summary>
        private eWCSNodeType _commonParamType;

        /// <summary>
        /// 标液类型
        /// </summary>
        private eStandardSolutionType _standardSolutionType;

        private WCS3900Equip _device;

        private const string ErrorInfo = "— — —";

        #endregion

        #region 构造

        public UC_OneWMS3900ReagentInfo()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件



        #endregion

        #region 公有方法

        public void RefreshUI()
        {
            var commonParam = _device.CommonParam.CommonParams[_commonParamType];

            switch(_standardSolutionType)
            {
                case eStandardSolutionType.标液1:
                    UpdateUI(commonParam.Liquid1TotalNum, commonParam.Liquid1Allowance, commonParam.Liquid1ChangeTime, commonParam.Liquid1GuaranteePeriod);
                    break;
                case eStandardSolutionType.标液2:
                    UpdateUI(commonParam.Liquid2TotalNum, commonParam.Liquid2Allowance, commonParam.Liquid2ChangeTime, commonParam.Liquid2GuaranteePeriod);
                    break;
                case eStandardSolutionType.标液3:
                    UpdateUI(commonParam.Liquid3TotalNum, commonParam.Liquid3Allowance, commonParam.Liquid3ChangeTime, commonParam.Liquid3GuaranteePeriod);
                    break;
                case eStandardSolutionType.无氧标液:
                    UpdateUI(commonParam.Liquid1TotalNum, commonParam.Liquid1Allowance, commonParam.Liquid1ChangeTime, commonParam.Liquid1GuaranteePeriod);
                    break;
                case eStandardSolutionType.饱和氧标液:
                    UpdateUI(commonParam.Liquid2TotalNum, commonParam.Liquid2Allowance, commonParam.Liquid2ChangeTime, commonParam.Liquid2GuaranteePeriod);
                    break;
            }
        }

        public void BindDataToDevice(eWCSNodeType eWCSNodeType, eStandardSolutionType eStandardSolutionType, WCS3900Equip device)
        {
            gbStateInfo.Text = $"{eStandardSolutionType}";
            _commonParamType = eWCSNodeType;
            _standardSolutionType = eStandardSolutionType;
            _device = device;
            RefreshUI();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 刷新控件方法
        /// </summary>
        /// <param name="totalNum">总量</param>
        /// <param name="allowance">余量</param>
        /// <param name="chageTime">更换时间</param>
        /// <param name="expiration">保质期</param>
        private void UpdateUI(float totalNum, float allowance, DateTime chageTime, int expiration)
        {
            lblTotalNum.Text = totalNum == -1 ? ErrorInfo : totalNum.ToString() + "ml";
            lblAllowance.Text = allowance == -1 ? ErrorInfo : allowance.ToString() + "ml";
            lblChangeTime.Text = chageTime == DateTime.MinValue ? ErrorInfo : chageTime.ToString("yyyy-MM-dd");
            lblExpiration.Text = expiration == -1 ? ErrorInfo : expiration.ToString() + "天";
            if(chageTime == DateTime.MinValue || expiration == -1)
            {
                lblRemainDays.Text = ErrorInfo;
            }
            else
            {
                int remainDays = expiration - (DateTime.Now - chageTime).Days;
                lblRemainDays.Text = remainDays.ToString() + "天";
            }
        }

        #endregion
    }
}
