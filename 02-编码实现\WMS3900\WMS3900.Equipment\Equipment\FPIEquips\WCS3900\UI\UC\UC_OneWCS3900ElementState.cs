﻿using Fpi.WMS3000.Equipment.WCS3900;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    /// <summary>
    /// 单个器件状态显示
    /// </summary>
    public partial class UC_OneWCS3900ElementState : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 对应设备
        /// </summary>
        private WCS3900Equip _device;

        /// <summary>
        /// 因子类型
        /// </summary>
        private eWCSNodeType _eWCSNodeType;

        /// <summary>
        /// 对应的器件类型
        /// </summary>
        private eWCS3900ElementType _elementType = 0;

        private const string ErrorInfo = "— — —";

        #endregion

        #region 构造

        public UC_OneWCS3900ElementState()
        {
            InitializeComponent();
        }

        public UC_OneWCS3900ElementState(WCS3900Equip device, eWCSNodeType wcsNodeType, eWCS3900ElementType elementType) : this()
        {
            _device = device;
            _eWCSNodeType = wcsNodeType;
            _elementType = elementType;
            gbMain.Text = _elementType.ToString();
        }

        #endregion

        #region 公共方法

        internal void RefreshUI()
        {
            if(_device != null && _device.CommonParam.CommonParams.ContainsKey(_eWCSNodeType))
            {
                var oneCommonParam = _device.CommonParam.CommonParams[_eWCSNodeType];
                if(oneCommonParam.ElementState.ContainsKey(_elementType))
                {
                    lblState.Text = oneCommonParam.ElementState[_elementType] ? "开" : "关";
                }
                else
                {
                    lblState.Text = ErrorInfo;
                }
            }
            else
            {
                lblState.Text = ErrorInfo;
            }
        }

        #endregion
    }
}
