﻿using System;
using Fpi.Data.Config;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.SystemConfig;
using Fpi.WMS3000.SystemConfig.ImagePatrol.Config;

namespace Fpi.WMS3000.SystemOperation.Helper
{
    /// <summary>
    /// 操作帮助类
    /// </summary>
    public static class OpHelper
    {
        /// <summary>
        /// 开关量状态切换
        /// 添加重试次数，指定次数后都控制失败，返回false
        /// </summary>
        /// <returns></returns>
        public static bool StateNodeSwitch(StateNode stateNode, bool state, int retryCount = 3)
        {
            bool hasControl = false;
            if(stateNode != null)
            {
                // 记录开关量因子旧值
                bool oldState = stateNode.GetValue();

                string stateStr = state ? "打开" : "关闭";
                // 记录开始日志
                SystemLogHelper.WriteOperationLog($"开始{stateStr}{stateNode.name}");

                // 修改开关量因子值
                stateNode.SetValue(state);

                int index = 1;
                string errorInfo = string.Empty;
                while(index <= retryCount)
                {
                    try
                    {
                        //SystemLogHelper.WriteOperationLog($"第{index}次下发{stateNode.name}控制指令");
                        // 发送修改值至PLC
                        hasControl = SystemManager.GetInstance().WriteOneSwitch(stateNode);
                        if(!hasControl)
                        {
                            index++;
                        }
                        else
                        {
                            break;
                        }
                    }
                    catch(Exception ex)
                    {
                        index++;
                        errorInfo = ex.Message;
                    }
                }

                // 重复指定次数后仍未控制成功
                if(!hasControl)
                {
                    // 恢复开关量因子旧值
                    stateNode.SetValue(oldState);
                    SystemLogHelper.WriteOperationLog($"{stateStr}{stateNode.name}失败：{errorInfo}");
                }
                else
                {
                    SystemLogHelper.WriteOperationLog($"{stateStr}{stateNode.name}成功！");
                }
            }

            return hasControl;
        }


        /// <summary>
        /// 采水报警状态判断
        /// </summary>
        public static void CheckWaterCollectionAlarmState()
        {
            // 采水点偏移
            if(ImagePatrolManager.GetInstance().LatestImagePatrolResult?.WaterTrapMigration == eWaterTrapMigration.采水点偏移)
            {
                GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterCollectionAlarmState = eWaterCollectionAlarmState.取水点偏移;
            }
            // 采水设备丢失
            else if(ImagePatrolManager.GetInstance().LatestImagePatrolResult?.WaterTrapMigration == eWaterTrapMigration.采水设备丢失)
            {
                GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterCollectionAlarmState = eWaterCollectionAlarmState.采水设备丢失;
            }
            // 水泵1/2#故障
            else if((GlobalDataCache.GetInstance().WaterCollectionModuleData.LastUsedPump == ePumpUsed.泵一 && GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump1State != eWaterPumpState.正常 && GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump1State != eWaterPumpState.空闲) || (GlobalDataCache.GetInstance().WaterCollectionModuleData.LastUsedPump == ePumpUsed.泵二 && GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump2State != eWaterPumpState.正常 && GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump2State != eWaterPumpState.空闲))
            {
                GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterCollectionAlarmState = eWaterCollectionAlarmState.水泵故障;
            }
            // 管路堵塞或破损
            else if(GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPipeState != eWaterPipeState.正常)
            {
                GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterCollectionAlarmState = eWaterCollectionAlarmState.管路堵塞或破损;
            }
            // 人为干扰
            else if(ImagePatrolManager.GetInstance().LatestImagePatrolResult?.WaterTrapIntrusion != eModuleWorkingState.正常)
            {
                GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterCollectionAlarmState = eWaterCollectionAlarmState.人为干扰;
            }
            // 采水头搁浅
            //else if()
            //{
            //    GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterCollectionAlarmState = eWaterCollectionAlarmState.采水头搁浅;
            //}
            else
            {
                GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterCollectionAlarmState = eWaterCollectionAlarmState.正常;
            }
        }

    }
}
