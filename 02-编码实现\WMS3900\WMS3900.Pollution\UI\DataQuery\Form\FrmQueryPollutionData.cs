﻿using System.Windows.Forms;
using Fpi.UI.PC.DockForms;
using Sunny.UI;

namespace Fpi.WMS3000.Pollution.UI.DataQuery
{
    /// <summary>
    /// 污染源数据查询
    /// </summary>
    public partial class FrmQueryPollutionData : BaseWindow
    {
        public FrmQueryPollutionData()
        {
            InitializeComponent();
        }

        private void FrmQueryPollutionData_Load(object sender, System.EventArgs e)
        {
            {
                var page = new UIPage();
                var uc = new UC_QuerySecondPollutionData();
                page.Controls.Add(uc);
                uc.Dock = DockStyle.Fill;
                page.Text = uc.TitleName;
                tabMain.AddPage(page);
            }

            {
                var page = new UIPage();
                var uc = new UC_QueryMinutePollutionData();
                page.Controls.Add(uc);
                uc.Dock = DockStyle.Fill;
                page.Text = uc.TitleName;
                tabMain.AddPage(page);
            }

            {
                var page = new UIPage();
                var uc = new UC_QueryTenMinutePollutionData();
                page.Controls.Add(uc);
                uc.Dock = DockStyle.Fill;
                page.Text = uc.TitleName;
                tabMain.AddPage(page);
            }

            {
                var page = new UIPage();
                var uc = new UC_QueryHourPollutionData();
                page.Controls.Add(uc);
                uc.Dock = DockStyle.Fill;
                page.Text = uc.TitleName;
                tabMain.AddPage(page);
            }

            {
                var page = new UIPage();
                var uc = new UC_QueryDayPollutionData();
                page.Controls.Add(uc);
                uc.Dock = DockStyle.Fill;
                page.Text = uc.TitleName;
                tabMain.AddPage(page);
            }
        }
    }
}