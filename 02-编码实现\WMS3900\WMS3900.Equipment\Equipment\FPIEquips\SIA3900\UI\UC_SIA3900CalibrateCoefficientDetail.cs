﻿using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using Fpi.Devices;
using Fpi.WMS3000.Equipment.SIA3900;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    public partial class UC_SIA3900CalibrateCoefficientDetail : UIUserControl
    {
        #region 字段属性

        private SIA3900Equipment _device;

        private List<UC_DeviceParamData> _coefficientDetails = new();

        private bool _isShow;

        /// <summary>
        /// 是否显示刷新按钮
        /// </summary>
        [Description("是否显示刷新按钮")]
        public bool IsRefresh { get => _isShow; set { _isShow = value; btnRefresh.Visible = value; } }

        #endregion

        #region 构造

        public UC_SIA3900CalibrateCoefficientDetail()
        {
            InitializeComponent();
        }

        public UC_SIA3900CalibrateCoefficientDetail(SIA3900CalibrateCoefficient calibrateCoefficient, string type) : this()
        {
            IsRefresh = false;
            uc_SIA3900CalibrateCoefficient.CanRefresh = IsRefresh;      
            InitUI(calibrateCoefficient, type);
        }

        #endregion

        #region 事件

        private void btnRefresh_Click(object sender, System.EventArgs e)
        {
            if(_device != null)
            {
                foreach(var item in _coefficientDetails)
                {
                    item.RefreshUI();
                }
            }
        }

        #endregion

        #region 公共方法

        internal void SetTragetDevice(SIA3900Equipment device)
        {
            _device = device;
            uc_SIA3900CalibrateCoefficient.CanRefresh = IsRefresh;
            if(_device != null)
            {
                InitUI(_device.CalibrateCoefficient, device.TypeDesc);
            }
        }

        #endregion

        #region 私有方法

        private void InitUI(SIA3900CalibrateCoefficient calibrateCoefficient, string type)
        {
            if(!type.Equals(eDeviceMeasureType.CODMn.ToString()))
            {
                if(calibrateCoefficient.RoutineCalibrateCoefficient != null)
                {
                    uc_SIA3900CalibrateCoefficient.SetTragetParams(calibrateCoefficient.RoutineCalibrateCoefficient);

                    // 根据标定点数确定详细标定区展示
                    int count = calibrateCoefficient.RoutineCalibrateCoefficient.CalibrateCount;
                    for(int i = 0; i < count; i++)
                    {
                        UC_DeviceParamData deviceParamData = new()
                        {
                            Text = $"标定点{i + 1}参数",
                            CanRefresh = false,
                            Width = pnlMain.Width / 2 -30,
                            Height = 690
                        };
                        deviceParamData.SetTragetParams(calibrateCoefficient.CalibrateCoefficientDetail.PointCoefficients[i],new Padding(103,0,0,0));
                        _coefficientDetails.Add(deviceParamData);

                        pnlMain.Controls.Add(deviceParamData);
                    }
                }
            }
            else
            {
                uc_SIA3900CalibrateCoefficient.SetTragetParams(calibrateCoefficient.GZCalibrateCoefficient);
            }
        }

        #endregion
    }
}