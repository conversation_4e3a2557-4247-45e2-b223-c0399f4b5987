﻿using System;
using System.Collections.Generic;
using Fpi.WMS3000.Equipment.QCD3900;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    /// <summary>
    /// 其他器件信息
    /// </summary>
    public partial class UC_QCD3900CompInfo : UIUserControl
    {
        #region 字段属性

        private QCD3900Equip _device;

        private const string ErrorInfo = "— — — — —";

        /// <summary>
        /// 控件集合
        /// </summary>
        private List<UC_OneElementLifeInfo> _elementLifeInfos = new List<UC_OneElementLifeInfo>();

        #endregion

        #region 构造

        public UC_QCD3900CompInfo()
        {
            InitializeComponent();
        }

        #endregion

        #region 公共方法

        internal void SetTragetDevice(QCD3900Equip device)
        {
            _device = device;
            foreach(eQCD3900ElementToAnsysisType item in Enum.GetValues(typeof(eQCD3900ElementToAnsysisType)))
            {
                if(!(item == eQCD3900ElementToAnsysisType.纯水泵PU1 || item == eQCD3900ElementToAnsysisType.定容泵PU2 ||
                    item == eQCD3900ElementToAnsysisType.样品杯气泵PU3 || item == eQCD3900ElementToAnsysisType.标样杯气泵PU4))
                {
                    _elementLifeInfos.Add(new UC_OneElementLifeInfo(_device, _device.ElementLifeInfos.ElementParamInfos[item]));
                }
                else
                {
                    _elementLifeInfos.Add(new UC_OneElementLifeInfo(_device, _device.ElementLifeInfos.ElementParamInfos[item], true));
                }
            }

            // 分两行添加控件
            for(int i = 0; i < _elementLifeInfos.Count; i++)
            {
                if(i < 6)
                {
                    tabElementInfos.Controls.Add(_elementLifeInfos[i], i, 0);
                }
                else
                {
                    tabElementInfos.Controls.Add(_elementLifeInfos[i], i - 6, 1);
                }
            }
            RefreshUI();
        }

        internal void RefreshUI()
        {
            if(_device != null)
            {
                for(int i = 0; i < _elementLifeInfos.Count; i++)
                {
                    _elementLifeInfos[i].RefreshUI();
                }
            }
        }

        #endregion

        #region 事件

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            RefreshUI();
        }

        #endregion
    }
}