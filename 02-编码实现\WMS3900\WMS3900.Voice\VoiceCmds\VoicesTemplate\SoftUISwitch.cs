﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Fpi.UI.PC.Config;
using Fpi.UI.PC.DockForms;
using Fpi.Users;
using Fpi.WMS3000.Voice.Helper;
using Fpi.WMS3000.Voice.Interface;
using Fpi.Xml;

namespace Fpi.WMS3000.Voice.VoicesTemplate
{
    /// <summary>
    /// 软件界面切换
    /// </summary>
    public class SoftUISwitch : CustomVoiceCmd, IVoicesTemplateConfigView
    {
        #region 字段属性

        /// <summary>
        /// 是否初始化
        /// </summary>
        private bool _inited;

        /// <summary>
        /// 对应界面节点
        /// </summary>
        private IdNameNode _uiItem;

        #endregion

        #region 公共方法（重写）

        public override string ToString()
        {
            return "软件界面切换";
        }

        public override string CustomDo(int paran)
        {
            try
            {
                if(!_inited)
                {
                    InitProperty();
                }

                var currentUser = UserManager.GetInstance().GetCurrentUser();

                // 权限及启用检查
                if(_uiItem is BaseItem baseItem)
                {
                    if(!baseItem.Visible || !baseItem.Enabled)
                    {
                        throw new Exception($"{baseItem.name}界面未启用！");
                    }
                    else if(!baseItem.HasMenuRight(currentUser))
                    {
                        throw new Exception($"当前用户{currentUser.name}无权限打开{baseItem.name}界面！");
                    }
                }
                else if(_uiItem is InLookItem inLookItem)
                {
                    if(!inLookItem.Visible || !inLookItem.Enabled)
                    {
                        throw new Exception($"{inLookItem.name}界面未启用！");
                    }
                    else if(!inLookItem.HasMenuRight(currentUser))
                    {
                        throw new Exception($"当前用户{currentUser.name}无权限打开{inLookItem.name}界面！");
                    }
                }

                // 处理异步更新界面
                var syncContext = SynchronizationContext.Current;

                syncContext.Post(state =>
                {
                    try
                    {
                        if(_uiItem is BaseItem baseItem)
                        {
                            baseItem.ItemClick();
                        }
                        else if(_uiItem is InLookItem inLookItem)
                        {
                            InLookForm.ShowInLookItemForm(inLookItem);
                        }
                        else
                        {
                            throw new Exception($"当前配置页面{_uiItem.name}不是合法的界面项！");
                        }

                        VoiceLogHelper.Info($"[{CmdConfig.name}]命令执行成功，切换页面至{_uiItem.name}。");
                    }
                    catch(Exception e)
                    {
                        VoiceLogHelper.Info($"[{CmdConfig.name}]命令执行出错：{e.Message}");
                    }
                }, null);

                return $"好的，正在打开{_uiItem.name}界面";
            }
            catch(Exception ex)
            {
                VoiceLogHelper.Info($"[{CmdConfig.name}]命令执行出错: {ex.Message}");
                return $"抱歉，执行出错，{ex.Message}";
            }
        }

        #endregion

        #region IVoicesTemplateConfigView 成员

        public IVoiceConfig TemplateConfigView => new UC_SoftUISwitch();

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化参数
        /// </summary>
        private void InitProperty()
        {
            string uiItemId = CmdConfig.GetPropertyValue(GlobalNameDefine.PropertyName_UIItemId);
            _uiItem = VoiceHelper.GetFormParams().FirstOrDefault(uiItem => uiItem.id == uiItemId);
            if(_uiItem == null)
            {
                throw new Exception("要打开的界面未配置！");
            }

            _inited = true;
        }

        #endregion
    }
}