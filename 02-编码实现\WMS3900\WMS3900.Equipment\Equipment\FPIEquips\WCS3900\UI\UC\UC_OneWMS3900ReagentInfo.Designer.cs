﻿namespace Fpi.WMS3000.Equipment.UI
{
    partial class UC_OneWMS3900ReagentInfo
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if(disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.gbStateInfo = new Sunny.UI.UIGroupBox();
            this.lblRemainDays = new Sunny.UI.UILabel();
            this.lblMaxUsageCountTitle = new Sunny.UI.UILabel();
            this.lblChangeTime = new Sunny.UI.UILabel();
            this.uiLabel2 = new Sunny.UI.UILabel();
            this.lblAllowance = new Sunny.UI.UILabel();
            this.lblExpiration = new Sunny.UI.UILabel();
            this.lblTotalNum = new Sunny.UI.UILabel();
            this.lblUsageCountTitle = new Sunny.UI.UILabel();
            this.uiLabel5 = new Sunny.UI.UILabel();
            this.uiLabel8 = new Sunny.UI.UILabel();
            this.gbStateInfo.SuspendLayout();
            this.SuspendLayout();
            // 
            // gbStateInfo
            // 
            this.gbStateInfo.Controls.Add(this.lblRemainDays);
            this.gbStateInfo.Controls.Add(this.lblMaxUsageCountTitle);
            this.gbStateInfo.Controls.Add(this.lblChangeTime);
            this.gbStateInfo.Controls.Add(this.uiLabel2);
            this.gbStateInfo.Controls.Add(this.lblAllowance);
            this.gbStateInfo.Controls.Add(this.lblExpiration);
            this.gbStateInfo.Controls.Add(this.lblTotalNum);
            this.gbStateInfo.Controls.Add(this.lblUsageCountTitle);
            this.gbStateInfo.Controls.Add(this.uiLabel5);
            this.gbStateInfo.Controls.Add(this.uiLabel8);
            this.gbStateInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbStateInfo.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbStateInfo.Location = new System.Drawing.Point(0, 0);
            this.gbStateInfo.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbStateInfo.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbStateInfo.Name = "gbStateInfo";
            this.gbStateInfo.Padding = new System.Windows.Forms.Padding(0, 32, 0, 0);
            this.gbStateInfo.Size = new System.Drawing.Size(430, 222);
            this.gbStateInfo.TabIndex = 1;
            this.gbStateInfo.Text = "试剂信息";
            this.gbStateInfo.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // lblRemainDays
            // 
            this.lblRemainDays.AutoSize = true;
            this.lblRemainDays.BackColor = System.Drawing.Color.Transparent;
            this.lblRemainDays.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.lblRemainDays.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblRemainDays.Location = new System.Drawing.Point(125, 164);
            this.lblRemainDays.Name = "lblRemainDays";
            this.lblRemainDays.Size = new System.Drawing.Size(80, 21);
            this.lblRemainDays.TabIndex = 6;
            this.lblRemainDays.Text = "----------";
            // 
            // lblMaxUsageCountTitle
            // 
            this.lblMaxUsageCountTitle.AutoSize = true;
            this.lblMaxUsageCountTitle.BackColor = System.Drawing.Color.Transparent;
            this.lblMaxUsageCountTitle.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.lblMaxUsageCountTitle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblMaxUsageCountTitle.Location = new System.Drawing.Point(17, 163);
            this.lblMaxUsageCountTitle.Name = "lblMaxUsageCountTitle";
            this.lblMaxUsageCountTitle.Size = new System.Drawing.Size(106, 21);
            this.lblMaxUsageCountTitle.TabIndex = 11;
            this.lblMaxUsageCountTitle.Text = "剩余使用天数";
            // 
            // lblChangeTime
            // 
            this.lblChangeTime.AutoSize = true;
            this.lblChangeTime.BackColor = System.Drawing.Color.Transparent;
            this.lblChangeTime.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.lblChangeTime.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblChangeTime.Location = new System.Drawing.Point(125, 109);
            this.lblChangeTime.Name = "lblChangeTime";
            this.lblChangeTime.Size = new System.Drawing.Size(80, 21);
            this.lblChangeTime.TabIndex = 4;
            this.lblChangeTime.Text = "----------";
            // 
            // uiLabel2
            // 
            this.uiLabel2.AutoSize = true;
            this.uiLabel2.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel2.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel2.Location = new System.Drawing.Point(49, 108);
            this.uiLabel2.Name = "uiLabel2";
            this.uiLabel2.Size = new System.Drawing.Size(74, 21);
            this.uiLabel2.TabIndex = 9;
            this.uiLabel2.Text = "更换时间";
            // 
            // lblAllowance
            // 
            this.lblAllowance.AutoSize = true;
            this.lblAllowance.BackColor = System.Drawing.Color.Transparent;
            this.lblAllowance.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.lblAllowance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblAllowance.Location = new System.Drawing.Point(314, 53);
            this.lblAllowance.Name = "lblAllowance";
            this.lblAllowance.Size = new System.Drawing.Size(80, 21);
            this.lblAllowance.TabIndex = 3;
            this.lblAllowance.Text = "----------";
            // 
            // lblExpiration
            // 
            this.lblExpiration.AutoSize = true;
            this.lblExpiration.BackColor = System.Drawing.Color.Transparent;
            this.lblExpiration.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.lblExpiration.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblExpiration.Location = new System.Drawing.Point(314, 108);
            this.lblExpiration.Name = "lblExpiration";
            this.lblExpiration.Size = new System.Drawing.Size(80, 21);
            this.lblExpiration.TabIndex = 5;
            this.lblExpiration.Text = "----------";
            // 
            // lblTotalNum
            // 
            this.lblTotalNum.AutoSize = true;
            this.lblTotalNum.BackColor = System.Drawing.Color.Transparent;
            this.lblTotalNum.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.lblTotalNum.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblTotalNum.Location = new System.Drawing.Point(125, 53);
            this.lblTotalNum.Name = "lblTotalNum";
            this.lblTotalNum.Size = new System.Drawing.Size(80, 21);
            this.lblTotalNum.TabIndex = 2;
            this.lblTotalNum.Text = "----------";
            // 
            // lblUsageCountTitle
            // 
            this.lblUsageCountTitle.AutoSize = true;
            this.lblUsageCountTitle.BackColor = System.Drawing.Color.Transparent;
            this.lblUsageCountTitle.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.lblUsageCountTitle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblUsageCountTitle.Location = new System.Drawing.Point(254, 108);
            this.lblUsageCountTitle.Name = "lblUsageCountTitle";
            this.lblUsageCountTitle.Size = new System.Drawing.Size(58, 21);
            this.lblUsageCountTitle.TabIndex = 10;
            this.lblUsageCountTitle.Text = "保质期";
            // 
            // uiLabel5
            // 
            this.uiLabel5.AutoSize = true;
            this.uiLabel5.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel5.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiLabel5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel5.Location = new System.Drawing.Point(238, 53);
            this.uiLabel5.Name = "uiLabel5";
            this.uiLabel5.Size = new System.Drawing.Size(74, 21);
            this.uiLabel5.TabIndex = 8;
            this.uiLabel5.Text = "试剂余量";
            // 
            // uiLabel8
            // 
            this.uiLabel8.AutoSize = true;
            this.uiLabel8.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel8.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiLabel8.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel8.Location = new System.Drawing.Point(49, 53);
            this.uiLabel8.Name = "uiLabel8";
            this.uiLabel8.Size = new System.Drawing.Size(74, 21);
            this.uiLabel8.TabIndex = 7;
            this.uiLabel8.Text = "试剂总量";
            // 
            // UC_OneWMS3900ReagentInfo
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.gbStateInfo);
            this.Name = "UC_OneWMS3900ReagentInfo";
            this.Size = new System.Drawing.Size(430, 222);
            this.gbStateInfo.ResumeLayout(false);
            this.gbStateInfo.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private Sunny.UI.UIGroupBox gbStateInfo;
        private Sunny.UI.UILabel lblRemainDays;
        private Sunny.UI.UILabel lblMaxUsageCountTitle;
        private Sunny.UI.UILabel lblChangeTime;
        private Sunny.UI.UILabel uiLabel2;
        private Sunny.UI.UILabel lblAllowance;
        private Sunny.UI.UILabel lblExpiration;
        private Sunny.UI.UILabel lblTotalNum;
        private Sunny.UI.UILabel lblUsageCountTitle;
        private Sunny.UI.UILabel uiLabel5;
        private Sunny.UI.UILabel uiLabel8;
    }
}
