﻿namespace Fpi.WMS3000.Equipment.UI
{
    partial class UC_WCS3900Maintain
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.gbElementControl = new Sunny.UI.UIGroupBox();
            this.pnlElementControl = new System.Windows.Forms.FlowLayoutPanel();
            this.gbFlowControl = new Sunny.UI.UIGroupBox();
            this.btnCalibrate = new Sunny.UI.UIButton();
            this.uiLabel1 = new Sunny.UI.UILabel();
            this.btnCompare = new Sunny.UI.UIButton();
            this.chkCalibrateTurbidity = new Sunny.UI.UICheckBox();
            this.chkCalibrateOxygen = new Sunny.UI.UICheckBox();
            this.chkCalibrateElectric = new Sunny.UI.UICheckBox();
            this.chkCalibratePH = new Sunny.UI.UICheckBox();
            this.pnlTurbidity = new Sunny.UI.UIPanel();
            this.rdbTurbidityCheck3 = new Sunny.UI.UIRadioButton();
            this.rdbTurbidityCheck2 = new Sunny.UI.UIRadioButton();
            this.rdbTurbidityCheck1 = new Sunny.UI.UIRadioButton();
            this.rdbTurbidityAuto = new Sunny.UI.UIRadioButton();
            this.pnlOxygen = new Sunny.UI.UIPanel();
            this.rdbOxygenCheck2 = new Sunny.UI.UIRadioButton();
            this.rdbOxygenCheck1 = new Sunny.UI.UIRadioButton();
            this.rdbOxygenAuto = new Sunny.UI.UIRadioButton();
            this.pnlElectric = new Sunny.UI.UIPanel();
            this.rdbElectricCheck3 = new Sunny.UI.UIRadioButton();
            this.rdbElectricCheck2 = new Sunny.UI.UIRadioButton();
            this.rdbElectricCheck1 = new Sunny.UI.UIRadioButton();
            this.rdbElectricAuto = new Sunny.UI.UIRadioButton();
            this.chkTemp = new Sunny.UI.UICheckBox();
            this.chkTurbidity = new Sunny.UI.UICheckBox();
            this.chkOxygen = new Sunny.UI.UICheckBox();
            this.chkElectric = new Sunny.UI.UICheckBox();
            this.pnlPH = new Sunny.UI.UIPanel();
            this.rdbPHChech3 = new Sunny.UI.UIRadioButton();
            this.rdbPHChech2 = new Sunny.UI.UIRadioButton();
            this.rdbPHChech1 = new Sunny.UI.UIRadioButton();
            this.rdbPHAuto = new Sunny.UI.UIRadioButton();
            this.chkPH = new Sunny.UI.UICheckBox();
            this.uiLine4 = new Sunny.UI.UILine();
            this.btnCheck = new Sunny.UI.UIButton();
            this.uiLine26 = new Sunny.UI.UILine();
            this.btnStop = new Sunny.UI.UIButton();
            this.btnEmpty = new Sunny.UI.UIButton();
            this.btnMeasure = new Sunny.UI.UIButton();
            this.gbTimeCalibrate = new Sunny.UI.UIGroupBox();
            this.btnTimeCalibrate = new Sunny.UI.UIButton();
            this.gbWorkModel = new Sunny.UI.UIGroupBox();
            this.cmbWorkModel = new Sunny.UI.UIComboBox();
            this.btnSwitchWorkModel = new Sunny.UI.UIButton();
            this.gbCheckParamReadSet = new Sunny.UI.UIGroupBox();
            this.uc_TempCheckParam = new Fpi.WMS3000.Equipment.UI.UC_OneWCS3900CheckParam();
            this.uc_TurbCheckParam = new Fpi.WMS3000.Equipment.UI.UC_OneWCS3900CheckParam();
            this.uc_OxyCheckParam = new Fpi.WMS3000.Equipment.UI.UC_OneWCS3900CheckParam();
            this.uc_ConduCheckParam = new Fpi.WMS3000.Equipment.UI.UC_OneWCS3900CheckParam();
            this.uc_PHCheckParam = new Fpi.WMS3000.Equipment.UI.UC_OneWCS3900CheckParam();
            this.txtMotherLiquorValue = new Sunny.UI.UITextBox();
            this.uiTextBox1 = new Sunny.UI.UITextBox();
            this.uiGroupBox1 = new Sunny.UI.UIGroupBox();
            this.gbElementControl.SuspendLayout();
            this.gbFlowControl.SuspendLayout();
            this.pnlTurbidity.SuspendLayout();
            this.pnlOxygen.SuspendLayout();
            this.pnlElectric.SuspendLayout();
            this.pnlPH.SuspendLayout();
            this.gbTimeCalibrate.SuspendLayout();
            this.gbWorkModel.SuspendLayout();
            this.gbCheckParamReadSet.SuspendLayout();
            this.uiGroupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // gbElementControl
            // 
            this.gbElementControl.Controls.Add(this.pnlElementControl);
            this.gbElementControl.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbElementControl.Location = new System.Drawing.Point(1, 1);
            this.gbElementControl.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbElementControl.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbElementControl.Name = "gbElementControl";
            this.gbElementControl.Padding = new System.Windows.Forms.Padding(1, 32, 1, 1);
            this.gbElementControl.Size = new System.Drawing.Size(840, 894);
            this.gbElementControl.TabIndex = 0;
            this.gbElementControl.Text = "器件控制";
            this.gbElementControl.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // pnlElementControl
            // 
            this.pnlElementControl.AutoScroll = true;
            this.pnlElementControl.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.pnlElementControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlElementControl.FlowDirection = System.Windows.Forms.FlowDirection.TopDown;
            this.pnlElementControl.Location = new System.Drawing.Point(1, 32);
            this.pnlElementControl.Name = "pnlElementControl";
            this.pnlElementControl.Padding = new System.Windows.Forms.Padding(4, 1, 1, 1);
            this.pnlElementControl.Size = new System.Drawing.Size(838, 861);
            this.pnlElementControl.TabIndex = 0;
            // 
            // gbFlowControl
            // 
            this.gbFlowControl.Controls.Add(this.btnCalibrate);
            this.gbFlowControl.Controls.Add(this.uiLabel1);
            this.gbFlowControl.Controls.Add(this.btnCompare);
            this.gbFlowControl.Controls.Add(this.chkCalibrateTurbidity);
            this.gbFlowControl.Controls.Add(this.chkCalibrateOxygen);
            this.gbFlowControl.Controls.Add(this.chkCalibrateElectric);
            this.gbFlowControl.Controls.Add(this.chkCalibratePH);
            this.gbFlowControl.Controls.Add(this.pnlTurbidity);
            this.gbFlowControl.Controls.Add(this.pnlOxygen);
            this.gbFlowControl.Controls.Add(this.pnlElectric);
            this.gbFlowControl.Controls.Add(this.chkTemp);
            this.gbFlowControl.Controls.Add(this.chkTurbidity);
            this.gbFlowControl.Controls.Add(this.chkOxygen);
            this.gbFlowControl.Controls.Add(this.chkElectric);
            this.gbFlowControl.Controls.Add(this.pnlPH);
            this.gbFlowControl.Controls.Add(this.chkPH);
            this.gbFlowControl.Controls.Add(this.uiLine4);
            this.gbFlowControl.Controls.Add(this.btnCheck);
            this.gbFlowControl.Controls.Add(this.uiLine26);
            this.gbFlowControl.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbFlowControl.Location = new System.Drawing.Point(1103, 1);
            this.gbFlowControl.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbFlowControl.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbFlowControl.Name = "gbFlowControl";
            this.gbFlowControl.Padding = new System.Windows.Forms.Padding(1, 32, 1, 1);
            this.gbFlowControl.Size = new System.Drawing.Size(512, 443);
            this.gbFlowControl.TabIndex = 5;
            this.gbFlowControl.Text = "流程控制";
            this.gbFlowControl.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // btnCalibrate
            // 
            this.btnCalibrate.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnCalibrate.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnCalibrate.Location = new System.Drawing.Point(375, 405);
            this.btnCalibrate.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnCalibrate.Name = "btnCalibrate";
            this.btnCalibrate.Size = new System.Drawing.Size(110, 29);
            this.btnCalibrate.TabIndex = 42;
            this.btnCalibrate.Text = "标定";
            this.btnCalibrate.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnCalibrate.Click += new System.EventHandler(this.btnCalibrate_Click);
            // 
            // uiLabel1
            // 
            this.uiLabel1.AutoSize = true;
            this.uiLabel1.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel1.Location = new System.Drawing.Point(32, 374);
            this.uiLabel1.Name = "uiLabel1";
            this.uiLabel1.Size = new System.Drawing.Size(42, 21);
            this.uiLabel1.TabIndex = 41;
            this.uiLabel1.Text = "参数";
            // 
            // btnCompare
            // 
            this.btnCompare.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnCompare.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnCompare.Location = new System.Drawing.Point(375, 306);
            this.btnCompare.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnCompare.Name = "btnCompare";
            this.btnCompare.Size = new System.Drawing.Size(110, 29);
            this.btnCompare.TabIndex = 40;
            this.btnCompare.Text = "比对";
            this.btnCompare.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnCompare.Click += new System.EventHandler(this.btnCompare_Click);
            // 
            // chkCalibrateTurbidity
            // 
            this.chkCalibrateTurbidity.AutoSize = true;
            this.chkCalibrateTurbidity.BackColor = System.Drawing.Color.Transparent;
            this.chkCalibrateTurbidity.Cursor = System.Windows.Forms.Cursors.Hand;
            this.chkCalibrateTurbidity.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.chkCalibrateTurbidity.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.chkCalibrateTurbidity.Location = new System.Drawing.Point(407, 372);
            this.chkCalibrateTurbidity.MinimumSize = new System.Drawing.Size(1, 1);
            this.chkCalibrateTurbidity.Name = "chkCalibrateTurbidity";
            this.chkCalibrateTurbidity.Size = new System.Drawing.Size(65, 26);
            this.chkCalibrateTurbidity.TabIndex = 39;
            this.chkCalibrateTurbidity.Text = "浊度";
            // 
            // chkCalibrateOxygen
            // 
            this.chkCalibrateOxygen.AutoSize = true;
            this.chkCalibrateOxygen.BackColor = System.Drawing.Color.Transparent;
            this.chkCalibrateOxygen.Cursor = System.Windows.Forms.Cursors.Hand;
            this.chkCalibrateOxygen.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.chkCalibrateOxygen.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.chkCalibrateOxygen.Location = new System.Drawing.Point(307, 372);
            this.chkCalibrateOxygen.MinimumSize = new System.Drawing.Size(1, 1);
            this.chkCalibrateOxygen.Name = "chkCalibrateOxygen";
            this.chkCalibrateOxygen.Size = new System.Drawing.Size(81, 26);
            this.chkCalibrateOxygen.TabIndex = 38;
            this.chkCalibrateOxygen.Text = "溶解氧";
            // 
            // chkCalibrateElectric
            // 
            this.chkCalibrateElectric.AutoSize = true;
            this.chkCalibrateElectric.BackColor = System.Drawing.Color.Transparent;
            this.chkCalibrateElectric.Cursor = System.Windows.Forms.Cursors.Hand;
            this.chkCalibrateElectric.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.chkCalibrateElectric.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.chkCalibrateElectric.Location = new System.Drawing.Point(207, 372);
            this.chkCalibrateElectric.MinimumSize = new System.Drawing.Size(1, 1);
            this.chkCalibrateElectric.Name = "chkCalibrateElectric";
            this.chkCalibrateElectric.Size = new System.Drawing.Size(81, 26);
            this.chkCalibrateElectric.TabIndex = 37;
            this.chkCalibrateElectric.Text = "电导率";
            // 
            // chkCalibratePH
            // 
            this.chkCalibratePH.AutoSize = true;
            this.chkCalibratePH.BackColor = System.Drawing.Color.Transparent;
            this.chkCalibratePH.Cursor = System.Windows.Forms.Cursors.Hand;
            this.chkCalibratePH.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.chkCalibratePH.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.chkCalibratePH.Location = new System.Drawing.Point(115, 372);
            this.chkCalibratePH.MinimumSize = new System.Drawing.Size(1, 1);
            this.chkCalibratePH.Name = "chkCalibratePH";
            this.chkCalibratePH.Size = new System.Drawing.Size(55, 26);
            this.chkCalibratePH.TabIndex = 36;
            this.chkCalibratePH.Text = "PH";
            // 
            // pnlTurbidity
            // 
            this.pnlTurbidity.Controls.Add(this.rdbTurbidityCheck3);
            this.pnlTurbidity.Controls.Add(this.rdbTurbidityCheck2);
            this.pnlTurbidity.Controls.Add(this.rdbTurbidityCheck1);
            this.pnlTurbidity.Controls.Add(this.rdbTurbidityAuto);
            this.pnlTurbidity.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.pnlTurbidity.Location = new System.Drawing.Point(97, 208);
            this.pnlTurbidity.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlTurbidity.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlTurbidity.Name = "pnlTurbidity";
            this.pnlTurbidity.Size = new System.Drawing.Size(389, 35);
            this.pnlTurbidity.TabIndex = 34;
            this.pnlTurbidity.Text = null;
            this.pnlTurbidity.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rdbTurbidityCheck3
            // 
            this.rdbTurbidityCheck3.AutoSize = true;
            this.rdbTurbidityCheck3.BackColor = System.Drawing.Color.Transparent;
            this.rdbTurbidityCheck3.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbTurbidityCheck3.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbTurbidityCheck3.Location = new System.Drawing.Point(310, 4);
            this.rdbTurbidityCheck3.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbTurbidityCheck3.Name = "rdbTurbidityCheck3";
            this.rdbTurbidityCheck3.Size = new System.Drawing.Size(74, 26);
            this.rdbTurbidityCheck3.TabIndex = 3;
            this.rdbTurbidityCheck3.Text = "核查3";
            // 
            // rdbTurbidityCheck2
            // 
            this.rdbTurbidityCheck2.AutoSize = true;
            this.rdbTurbidityCheck2.BackColor = System.Drawing.Color.Transparent;
            this.rdbTurbidityCheck2.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbTurbidityCheck2.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbTurbidityCheck2.Location = new System.Drawing.Point(210, 4);
            this.rdbTurbidityCheck2.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbTurbidityCheck2.Name = "rdbTurbidityCheck2";
            this.rdbTurbidityCheck2.Size = new System.Drawing.Size(74, 26);
            this.rdbTurbidityCheck2.TabIndex = 2;
            this.rdbTurbidityCheck2.Text = "核查2";
            // 
            // rdbTurbidityCheck1
            // 
            this.rdbTurbidityCheck1.AutoSize = true;
            this.rdbTurbidityCheck1.BackColor = System.Drawing.Color.Transparent;
            this.rdbTurbidityCheck1.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbTurbidityCheck1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbTurbidityCheck1.Location = new System.Drawing.Point(110, 4);
            this.rdbTurbidityCheck1.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbTurbidityCheck1.Name = "rdbTurbidityCheck1";
            this.rdbTurbidityCheck1.Size = new System.Drawing.Size(74, 26);
            this.rdbTurbidityCheck1.TabIndex = 1;
            this.rdbTurbidityCheck1.Text = "核查1";
            // 
            // rdbTurbidityAuto
            // 
            this.rdbTurbidityAuto.AutoSize = true;
            this.rdbTurbidityAuto.BackColor = System.Drawing.Color.Transparent;
            this.rdbTurbidityAuto.Checked = true;
            this.rdbTurbidityAuto.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbTurbidityAuto.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbTurbidityAuto.Location = new System.Drawing.Point(19, 4);
            this.rdbTurbidityAuto.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbTurbidityAuto.Name = "rdbTurbidityAuto";
            this.rdbTurbidityAuto.Size = new System.Drawing.Size(65, 26);
            this.rdbTurbidityAuto.TabIndex = 0;
            this.rdbTurbidityAuto.Text = "自动";
            // 
            // pnlOxygen
            // 
            this.pnlOxygen.Controls.Add(this.rdbOxygenCheck2);
            this.pnlOxygen.Controls.Add(this.rdbOxygenCheck1);
            this.pnlOxygen.Controls.Add(this.rdbOxygenAuto);
            this.pnlOxygen.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.pnlOxygen.Location = new System.Drawing.Point(97, 158);
            this.pnlOxygen.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlOxygen.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlOxygen.Name = "pnlOxygen";
            this.pnlOxygen.Size = new System.Drawing.Size(389, 35);
            this.pnlOxygen.TabIndex = 33;
            this.pnlOxygen.Text = null;
            this.pnlOxygen.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rdbOxygenCheck2
            // 
            this.rdbOxygenCheck2.AutoSize = true;
            this.rdbOxygenCheck2.BackColor = System.Drawing.Color.Transparent;
            this.rdbOxygenCheck2.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbOxygenCheck2.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbOxygenCheck2.Location = new System.Drawing.Point(210, 4);
            this.rdbOxygenCheck2.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbOxygenCheck2.Name = "rdbOxygenCheck2";
            this.rdbOxygenCheck2.Size = new System.Drawing.Size(113, 26);
            this.rdbOxygenCheck2.TabIndex = 2;
            this.rdbOxygenCheck2.Text = "饱和氧核查";
            // 
            // rdbOxygenCheck1
            // 
            this.rdbOxygenCheck1.AutoSize = true;
            this.rdbOxygenCheck1.BackColor = System.Drawing.Color.Transparent;
            this.rdbOxygenCheck1.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbOxygenCheck1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbOxygenCheck1.Location = new System.Drawing.Point(110, 4);
            this.rdbOxygenCheck1.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbOxygenCheck1.Name = "rdbOxygenCheck1";
            this.rdbOxygenCheck1.Size = new System.Drawing.Size(97, 26);
            this.rdbOxygenCheck1.TabIndex = 1;
            this.rdbOxygenCheck1.Text = "无氧核查";
            // 
            // rdbOxygenAuto
            // 
            this.rdbOxygenAuto.AutoSize = true;
            this.rdbOxygenAuto.BackColor = System.Drawing.Color.Transparent;
            this.rdbOxygenAuto.Checked = true;
            this.rdbOxygenAuto.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbOxygenAuto.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbOxygenAuto.Location = new System.Drawing.Point(19, 4);
            this.rdbOxygenAuto.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbOxygenAuto.Name = "rdbOxygenAuto";
            this.rdbOxygenAuto.Size = new System.Drawing.Size(65, 26);
            this.rdbOxygenAuto.TabIndex = 0;
            this.rdbOxygenAuto.Text = "自动";
            // 
            // pnlElectric
            // 
            this.pnlElectric.Controls.Add(this.rdbElectricCheck3);
            this.pnlElectric.Controls.Add(this.rdbElectricCheck2);
            this.pnlElectric.Controls.Add(this.rdbElectricCheck1);
            this.pnlElectric.Controls.Add(this.rdbElectricAuto);
            this.pnlElectric.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.pnlElectric.Location = new System.Drawing.Point(97, 108);
            this.pnlElectric.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlElectric.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlElectric.Name = "pnlElectric";
            this.pnlElectric.Size = new System.Drawing.Size(389, 35);
            this.pnlElectric.TabIndex = 32;
            this.pnlElectric.Text = null;
            this.pnlElectric.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rdbElectricCheck3
            // 
            this.rdbElectricCheck3.AutoSize = true;
            this.rdbElectricCheck3.BackColor = System.Drawing.Color.Transparent;
            this.rdbElectricCheck3.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbElectricCheck3.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbElectricCheck3.Location = new System.Drawing.Point(310, 4);
            this.rdbElectricCheck3.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbElectricCheck3.Name = "rdbElectricCheck3";
            this.rdbElectricCheck3.Size = new System.Drawing.Size(74, 26);
            this.rdbElectricCheck3.TabIndex = 3;
            this.rdbElectricCheck3.Text = "核查3";
            // 
            // rdbElectricCheck2
            // 
            this.rdbElectricCheck2.AutoSize = true;
            this.rdbElectricCheck2.BackColor = System.Drawing.Color.Transparent;
            this.rdbElectricCheck2.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbElectricCheck2.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbElectricCheck2.Location = new System.Drawing.Point(210, 4);
            this.rdbElectricCheck2.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbElectricCheck2.Name = "rdbElectricCheck2";
            this.rdbElectricCheck2.Size = new System.Drawing.Size(74, 26);
            this.rdbElectricCheck2.TabIndex = 2;
            this.rdbElectricCheck2.Text = "核查2";
            // 
            // rdbElectricCheck1
            // 
            this.rdbElectricCheck1.AutoSize = true;
            this.rdbElectricCheck1.BackColor = System.Drawing.Color.Transparent;
            this.rdbElectricCheck1.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbElectricCheck1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbElectricCheck1.Location = new System.Drawing.Point(110, 4);
            this.rdbElectricCheck1.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbElectricCheck1.Name = "rdbElectricCheck1";
            this.rdbElectricCheck1.Size = new System.Drawing.Size(74, 26);
            this.rdbElectricCheck1.TabIndex = 1;
            this.rdbElectricCheck1.Text = "核查1";
            // 
            // rdbElectricAuto
            // 
            this.rdbElectricAuto.AutoSize = true;
            this.rdbElectricAuto.BackColor = System.Drawing.Color.Transparent;
            this.rdbElectricAuto.Checked = true;
            this.rdbElectricAuto.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbElectricAuto.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbElectricAuto.Location = new System.Drawing.Point(19, 4);
            this.rdbElectricAuto.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbElectricAuto.Name = "rdbElectricAuto";
            this.rdbElectricAuto.Size = new System.Drawing.Size(65, 26);
            this.rdbElectricAuto.TabIndex = 0;
            this.rdbElectricAuto.Text = "自动";
            // 
            // chkTemp
            // 
            this.chkTemp.AutoSize = true;
            this.chkTemp.BackColor = System.Drawing.Color.Transparent;
            this.chkTemp.Cursor = System.Windows.Forms.Cursors.Hand;
            this.chkTemp.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.chkTemp.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.chkTemp.Location = new System.Drawing.Point(9, 264);
            this.chkTemp.MinimumSize = new System.Drawing.Size(1, 1);
            this.chkTemp.Name = "chkTemp";
            this.chkTemp.Size = new System.Drawing.Size(65, 26);
            this.chkTemp.TabIndex = 31;
            this.chkTemp.Text = "水温";
            // 
            // chkTurbidity
            // 
            this.chkTurbidity.AutoSize = true;
            this.chkTurbidity.BackColor = System.Drawing.Color.Transparent;
            this.chkTurbidity.Cursor = System.Windows.Forms.Cursors.Hand;
            this.chkTurbidity.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.chkTurbidity.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.chkTurbidity.Location = new System.Drawing.Point(9, 213);
            this.chkTurbidity.MinimumSize = new System.Drawing.Size(1, 1);
            this.chkTurbidity.Name = "chkTurbidity";
            this.chkTurbidity.Size = new System.Drawing.Size(65, 26);
            this.chkTurbidity.TabIndex = 29;
            this.chkTurbidity.Text = "浊度";
            // 
            // chkOxygen
            // 
            this.chkOxygen.AutoSize = true;
            this.chkOxygen.BackColor = System.Drawing.Color.Transparent;
            this.chkOxygen.Cursor = System.Windows.Forms.Cursors.Hand;
            this.chkOxygen.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.chkOxygen.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.chkOxygen.Location = new System.Drawing.Point(9, 162);
            this.chkOxygen.MinimumSize = new System.Drawing.Size(1, 1);
            this.chkOxygen.Name = "chkOxygen";
            this.chkOxygen.Size = new System.Drawing.Size(81, 26);
            this.chkOxygen.TabIndex = 27;
            this.chkOxygen.Text = "溶解氧";
            // 
            // chkElectric
            // 
            this.chkElectric.AutoSize = true;
            this.chkElectric.BackColor = System.Drawing.Color.Transparent;
            this.chkElectric.Cursor = System.Windows.Forms.Cursors.Hand;
            this.chkElectric.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.chkElectric.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.chkElectric.Location = new System.Drawing.Point(9, 112);
            this.chkElectric.MinimumSize = new System.Drawing.Size(1, 1);
            this.chkElectric.Name = "chkElectric";
            this.chkElectric.Size = new System.Drawing.Size(81, 26);
            this.chkElectric.TabIndex = 25;
            this.chkElectric.Text = "电导率";
            // 
            // pnlPH
            // 
            this.pnlPH.Controls.Add(this.rdbPHChech3);
            this.pnlPH.Controls.Add(this.rdbPHChech2);
            this.pnlPH.Controls.Add(this.rdbPHChech1);
            this.pnlPH.Controls.Add(this.rdbPHAuto);
            this.pnlPH.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.pnlPH.Location = new System.Drawing.Point(96, 58);
            this.pnlPH.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlPH.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlPH.Name = "pnlPH";
            this.pnlPH.Size = new System.Drawing.Size(389, 35);
            this.pnlPH.TabIndex = 24;
            this.pnlPH.Text = null;
            this.pnlPH.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rdbPHChech3
            // 
            this.rdbPHChech3.AutoSize = true;
            this.rdbPHChech3.BackColor = System.Drawing.Color.Transparent;
            this.rdbPHChech3.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbPHChech3.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbPHChech3.Location = new System.Drawing.Point(310, 4);
            this.rdbPHChech3.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbPHChech3.Name = "rdbPHChech3";
            this.rdbPHChech3.Size = new System.Drawing.Size(74, 26);
            this.rdbPHChech3.TabIndex = 3;
            this.rdbPHChech3.Text = "核查3";
            // 
            // rdbPHChech2
            // 
            this.rdbPHChech2.AutoSize = true;
            this.rdbPHChech2.BackColor = System.Drawing.Color.Transparent;
            this.rdbPHChech2.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbPHChech2.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbPHChech2.Location = new System.Drawing.Point(210, 4);
            this.rdbPHChech2.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbPHChech2.Name = "rdbPHChech2";
            this.rdbPHChech2.Size = new System.Drawing.Size(74, 26);
            this.rdbPHChech2.TabIndex = 2;
            this.rdbPHChech2.Text = "核查2";
            // 
            // rdbPHChech1
            // 
            this.rdbPHChech1.AutoSize = true;
            this.rdbPHChech1.BackColor = System.Drawing.Color.Transparent;
            this.rdbPHChech1.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbPHChech1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbPHChech1.Location = new System.Drawing.Point(110, 4);
            this.rdbPHChech1.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbPHChech1.Name = "rdbPHChech1";
            this.rdbPHChech1.Size = new System.Drawing.Size(74, 26);
            this.rdbPHChech1.TabIndex = 1;
            this.rdbPHChech1.Text = "核查1";
            // 
            // rdbPHAuto
            // 
            this.rdbPHAuto.AutoSize = true;
            this.rdbPHAuto.BackColor = System.Drawing.Color.Transparent;
            this.rdbPHAuto.Checked = true;
            this.rdbPHAuto.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbPHAuto.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbPHAuto.Location = new System.Drawing.Point(19, 4);
            this.rdbPHAuto.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbPHAuto.Name = "rdbPHAuto";
            this.rdbPHAuto.Size = new System.Drawing.Size(65, 26);
            this.rdbPHAuto.TabIndex = 0;
            this.rdbPHAuto.Text = "自动";
            // 
            // chkPH
            // 
            this.chkPH.AutoSize = true;
            this.chkPH.BackColor = System.Drawing.Color.Transparent;
            this.chkPH.Cursor = System.Windows.Forms.Cursors.Hand;
            this.chkPH.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.chkPH.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.chkPH.Location = new System.Drawing.Point(9, 64);
            this.chkPH.MinimumSize = new System.Drawing.Size(1, 1);
            this.chkPH.Name = "chkPH";
            this.chkPH.Size = new System.Drawing.Size(55, 26);
            this.chkPH.TabIndex = 23;
            this.chkPH.Text = "PH";
            // 
            // uiLine4
            // 
            this.uiLine4.BackColor = System.Drawing.Color.Transparent;
            this.uiLine4.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLine4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLine4.Location = new System.Drawing.Point(2, 336);
            this.uiLine4.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiLine4.Name = "uiLine4";
            this.uiLine4.Size = new System.Drawing.Size(509, 29);
            this.uiLine4.TabIndex = 5;
            this.uiLine4.Text = "标定";
            // 
            // btnCheck
            // 
            this.btnCheck.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnCheck.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnCheck.Location = new System.Drawing.Point(240, 306);
            this.btnCheck.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnCheck.Name = "btnCheck";
            this.btnCheck.Size = new System.Drawing.Size(110, 29);
            this.btnCheck.TabIndex = 19;
            this.btnCheck.Text = "核查";
            this.btnCheck.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnCheck.Click += new System.EventHandler(this.btnTempCheck_Click);
            // 
            // uiLine26
            // 
            this.uiLine26.BackColor = System.Drawing.Color.Transparent;
            this.uiLine26.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLine26.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLine26.Location = new System.Drawing.Point(2, 27);
            this.uiLine26.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiLine26.Name = "uiLine26";
            this.uiLine26.Size = new System.Drawing.Size(509, 29);
            this.uiLine26.TabIndex = 0;
            this.uiLine26.Text = "核查、比对";
            // 
            // btnStop
            // 
            this.btnStop.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnStop.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnStop.Location = new System.Drawing.Point(77, 100);
            this.btnStop.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnStop.Name = "btnStop";
            this.btnStop.Size = new System.Drawing.Size(110, 29);
            this.btnStop.TabIndex = 22;
            this.btnStop.Text = "紧急停止";
            this.btnStop.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnStop.Click += new System.EventHandler(this.btnStop_Click);
            // 
            // btnEmpty
            // 
            this.btnEmpty.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnEmpty.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnEmpty.Location = new System.Drawing.Point(77, 52);
            this.btnEmpty.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnEmpty.Name = "btnEmpty";
            this.btnEmpty.Size = new System.Drawing.Size(110, 29);
            this.btnEmpty.TabIndex = 21;
            this.btnEmpty.Text = "复位排空清洗";
            this.btnEmpty.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnEmpty.Click += new System.EventHandler(this.btnEmpty_Click);
            // 
            // btnMeasure
            // 
            this.btnMeasure.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnMeasure.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnMeasure.Location = new System.Drawing.Point(77, 148);
            this.btnMeasure.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnMeasure.Name = "btnMeasure";
            this.btnMeasure.Size = new System.Drawing.Size(110, 29);
            this.btnMeasure.TabIndex = 20;
            this.btnMeasure.Text = "五参数测量";
            this.btnMeasure.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnMeasure.Click += new System.EventHandler(this.btnMeasure_Click);
            // 
            // gbTimeCalibrate
            // 
            this.gbTimeCalibrate.Controls.Add(this.btnTimeCalibrate);
            this.gbTimeCalibrate.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbTimeCalibrate.Location = new System.Drawing.Point(842, 121);
            this.gbTimeCalibrate.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbTimeCalibrate.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbTimeCalibrate.Name = "gbTimeCalibrate";
            this.gbTimeCalibrate.Padding = new System.Windows.Forms.Padding(0, 32, 0, 0);
            this.gbTimeCalibrate.Size = new System.Drawing.Size(258, 119);
            this.gbTimeCalibrate.TabIndex = 3;
            this.gbTimeCalibrate.Text = "时间校准";
            this.gbTimeCalibrate.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // btnTimeCalibrate
            // 
            this.btnTimeCalibrate.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnTimeCalibrate.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnTimeCalibrate.Location = new System.Drawing.Point(172, 57);
            this.btnTimeCalibrate.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnTimeCalibrate.Name = "btnTimeCalibrate";
            this.btnTimeCalibrate.Size = new System.Drawing.Size(76, 29);
            this.btnTimeCalibrate.TabIndex = 0;
            this.btnTimeCalibrate.Text = "校准";
            this.btnTimeCalibrate.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnTimeCalibrate.Click += new System.EventHandler(this.btnTimeCalibrate_Click);
            // 
            // gbWorkModel
            // 
            this.gbWorkModel.Controls.Add(this.cmbWorkModel);
            this.gbWorkModel.Controls.Add(this.btnSwitchWorkModel);
            this.gbWorkModel.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbWorkModel.Location = new System.Drawing.Point(842, 1);
            this.gbWorkModel.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbWorkModel.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbWorkModel.Name = "gbWorkModel";
            this.gbWorkModel.Padding = new System.Windows.Forms.Padding(0, 32, 0, 0);
            this.gbWorkModel.Size = new System.Drawing.Size(258, 119);
            this.gbWorkModel.TabIndex = 2;
            this.gbWorkModel.Text = "工作模式";
            this.gbWorkModel.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // cmbWorkModel
            // 
            this.cmbWorkModel.DataSource = null;
            this.cmbWorkModel.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbWorkModel.FillColor = System.Drawing.Color.White;
            this.cmbWorkModel.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cmbWorkModel.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbWorkModel.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbWorkModel.Location = new System.Drawing.Point(11, 53);
            this.cmbWorkModel.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbWorkModel.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbWorkModel.Name = "cmbWorkModel";
            this.cmbWorkModel.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbWorkModel.Size = new System.Drawing.Size(125, 29);
            this.cmbWorkModel.SymbolSize = 24;
            this.cmbWorkModel.TabIndex = 0;
            this.cmbWorkModel.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbWorkModel.Watermark = "";
            // 
            // btnSwitchWorkModel
            // 
            this.btnSwitchWorkModel.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnSwitchWorkModel.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSwitchWorkModel.Location = new System.Drawing.Point(161, 53);
            this.btnSwitchWorkModel.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnSwitchWorkModel.Name = "btnSwitchWorkModel";
            this.btnSwitchWorkModel.Size = new System.Drawing.Size(76, 29);
            this.btnSwitchWorkModel.TabIndex = 1;
            this.btnSwitchWorkModel.Text = "设置";
            this.btnSwitchWorkModel.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSwitchWorkModel.Click += new System.EventHandler(this.btnSwitchWorkModel_Click);
            // 
            // gbCheckParamReadSet
            // 
            this.gbCheckParamReadSet.Controls.Add(this.uc_TempCheckParam);
            this.gbCheckParamReadSet.Controls.Add(this.uc_TurbCheckParam);
            this.gbCheckParamReadSet.Controls.Add(this.uc_OxyCheckParam);
            this.gbCheckParamReadSet.Controls.Add(this.uc_ConduCheckParam);
            this.gbCheckParamReadSet.Controls.Add(this.uc_PHCheckParam);
            this.gbCheckParamReadSet.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbCheckParamReadSet.Location = new System.Drawing.Point(842, 447);
            this.gbCheckParamReadSet.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbCheckParamReadSet.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbCheckParamReadSet.Name = "gbCheckParamReadSet";
            this.gbCheckParamReadSet.Padding = new System.Windows.Forms.Padding(0, 32, 0, 0);
            this.gbCheckParamReadSet.Size = new System.Drawing.Size(773, 447);
            this.gbCheckParamReadSet.TabIndex = 6;
            this.gbCheckParamReadSet.Text = "核查参数读写";
            this.gbCheckParamReadSet.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uc_TempCheckParam
            // 
            this.uc_TempCheckParam.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_TempCheckParam.Location = new System.Drawing.Point(258, 234);
            this.uc_TempCheckParam.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_TempCheckParam.Name = "uc_TempCheckParam";
            this.uc_TempCheckParam.Padding = new System.Windows.Forms.Padding(1);
            this.uc_TempCheckParam.Radius = 10;
            this.uc_TempCheckParam.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_TempCheckParam.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_TempCheckParam.Size = new System.Drawing.Size(256, 210);
            this.uc_TempCheckParam.TabIndex = 4;
            this.uc_TempCheckParam.Text = "uC_OneWCS3900CheckParam4";
            this.uc_TempCheckParam.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // uc_TurbCheckParam
            // 
            this.uc_TurbCheckParam.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_TurbCheckParam.Location = new System.Drawing.Point(2, 234);
            this.uc_TurbCheckParam.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_TurbCheckParam.Name = "uc_TurbCheckParam";
            this.uc_TurbCheckParam.Padding = new System.Windows.Forms.Padding(1);
            this.uc_TurbCheckParam.Radius = 10;
            this.uc_TurbCheckParam.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_TurbCheckParam.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_TurbCheckParam.Size = new System.Drawing.Size(256, 210);
            this.uc_TurbCheckParam.TabIndex = 3;
            this.uc_TurbCheckParam.Text = "uC_OneWCS3900CheckParam5";
            this.uc_TurbCheckParam.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // uc_OxyCheckParam
            // 
            this.uc_OxyCheckParam.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_OxyCheckParam.Location = new System.Drawing.Point(514, 25);
            this.uc_OxyCheckParam.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_OxyCheckParam.Name = "uc_OxyCheckParam";
            this.uc_OxyCheckParam.Padding = new System.Windows.Forms.Padding(1);
            this.uc_OxyCheckParam.Radius = 10;
            this.uc_OxyCheckParam.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_OxyCheckParam.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_OxyCheckParam.Size = new System.Drawing.Size(256, 210);
            this.uc_OxyCheckParam.TabIndex = 2;
            this.uc_OxyCheckParam.Text = "uC_OneWCS3900CheckParam3";
            this.uc_OxyCheckParam.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // uc_ConduCheckParam
            // 
            this.uc_ConduCheckParam.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_ConduCheckParam.Location = new System.Drawing.Point(258, 25);
            this.uc_ConduCheckParam.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_ConduCheckParam.Name = "uc_ConduCheckParam";
            this.uc_ConduCheckParam.Padding = new System.Windows.Forms.Padding(1);
            this.uc_ConduCheckParam.Radius = 10;
            this.uc_ConduCheckParam.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_ConduCheckParam.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_ConduCheckParam.Size = new System.Drawing.Size(256, 210);
            this.uc_ConduCheckParam.TabIndex = 1;
            this.uc_ConduCheckParam.Text = "uC_OneWCS3900CheckParam2";
            this.uc_ConduCheckParam.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // uc_PHCheckParam
            // 
            this.uc_PHCheckParam.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_PHCheckParam.Location = new System.Drawing.Point(2, 25);
            this.uc_PHCheckParam.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_PHCheckParam.Name = "uc_PHCheckParam";
            this.uc_PHCheckParam.Padding = new System.Windows.Forms.Padding(1);
            this.uc_PHCheckParam.Radius = 10;
            this.uc_PHCheckParam.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_PHCheckParam.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_PHCheckParam.Size = new System.Drawing.Size(256, 210);
            this.uc_PHCheckParam.TabIndex = 0;
            this.uc_PHCheckParam.Text = "uC_OneWCS3900CheckParam1";
            this.uc_PHCheckParam.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // txtMotherLiquorValue
            // 
            this.txtMotherLiquorValue.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtMotherLiquorValue.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtMotherLiquorValue.Location = new System.Drawing.Point(77, 60);
            this.txtMotherLiquorValue.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtMotherLiquorValue.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtMotherLiquorValue.Name = "txtMotherLiquorValue";
            this.txtMotherLiquorValue.Padding = new System.Windows.Forms.Padding(5);
            this.txtMotherLiquorValue.ShowText = false;
            this.txtMotherLiquorValue.Size = new System.Drawing.Size(60, 27);
            this.txtMotherLiquorValue.TabIndex = 23;
            this.txtMotherLiquorValue.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtMotherLiquorValue.Watermark = "";
            // 
            // uiTextBox1
            // 
            this.uiTextBox1.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.uiTextBox1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiTextBox1.Location = new System.Drawing.Point(331, 60);
            this.uiTextBox1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiTextBox1.MinimumSize = new System.Drawing.Size(1, 16);
            this.uiTextBox1.Name = "uiTextBox1";
            this.uiTextBox1.Padding = new System.Windows.Forms.Padding(5);
            this.uiTextBox1.ShowText = false;
            this.uiTextBox1.Size = new System.Drawing.Size(60, 27);
            this.uiTextBox1.TabIndex = 27;
            this.uiTextBox1.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.uiTextBox1.Watermark = "";
            // 
            // uiGroupBox1
            // 
            this.uiGroupBox1.Controls.Add(this.btnMeasure);
            this.uiGroupBox1.Controls.Add(this.btnStop);
            this.uiGroupBox1.Controls.Add(this.btnEmpty);
            this.uiGroupBox1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiGroupBox1.Location = new System.Drawing.Point(842, 242);
            this.uiGroupBox1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiGroupBox1.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiGroupBox1.Name = "uiGroupBox1";
            this.uiGroupBox1.Padding = new System.Windows.Forms.Padding(0, 32, 0, 0);
            this.uiGroupBox1.Size = new System.Drawing.Size(258, 202);
            this.uiGroupBox1.TabIndex = 7;
            this.uiGroupBox1.Text = "系统控制";
            this.uiGroupBox1.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // UC_WCS3900Maintain
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.uiGroupBox1);
            this.Controls.Add(this.gbCheckParamReadSet);
            this.Controls.Add(this.gbWorkModel);
            this.Controls.Add(this.gbTimeCalibrate);
            this.Controls.Add(this.gbElementControl);
            this.Controls.Add(this.gbFlowControl);
            this.Name = "UC_WCS3900Maintain";
            this.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.Size = new System.Drawing.Size(1616, 896);
            this.gbElementControl.ResumeLayout(false);
            this.gbFlowControl.ResumeLayout(false);
            this.gbFlowControl.PerformLayout();
            this.pnlTurbidity.ResumeLayout(false);
            this.pnlTurbidity.PerformLayout();
            this.pnlOxygen.ResumeLayout(false);
            this.pnlOxygen.PerformLayout();
            this.pnlElectric.ResumeLayout(false);
            this.pnlElectric.PerformLayout();
            this.pnlPH.ResumeLayout(false);
            this.pnlPH.PerformLayout();
            this.gbTimeCalibrate.ResumeLayout(false);
            this.gbWorkModel.ResumeLayout(false);
            this.gbCheckParamReadSet.ResumeLayout(false);
            this.uiGroupBox1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion
        private Sunny.UI.UIGroupBox gbElementControl;
        private System.Windows.Forms.FlowLayoutPanel pnlElementControl;
        private Sunny.UI.UIGroupBox gbFlowControl;
        private Sunny.UI.UIGroupBox gbTimeCalibrate;
        private Sunny.UI.UIButton btnTimeCalibrate;
        private Sunny.UI.UIGroupBox gbWorkModel;
        private Sunny.UI.UIComboBox cmbWorkModel;
        private Sunny.UI.UIButton btnSwitchWorkModel;
        private Sunny.UI.UILine uiLine26;
        private Sunny.UI.UILine uiLine4;
        private Sunny.UI.UIButton btnEmpty;
        private Sunny.UI.UIButton btnMeasure;
        private Sunny.UI.UIButton btnCheck;
        private Sunny.UI.UIGroupBox gbCheckParamReadSet;
        private Sunny.UI.UITextBox txtMotherLiquorValue;
        private Sunny.UI.UITextBox uiTextBox1;
        private UI.UC_OneWCS3900CheckParam uc_ConduCheckParam;
        private UI.UC_OneWCS3900CheckParam uc_PHCheckParam;
        private UI.UC_OneWCS3900CheckParam uc_OxyCheckParam;
        private UI.UC_OneWCS3900CheckParam uc_TempCheckParam;
        private UI.UC_OneWCS3900CheckParam uc_TurbCheckParam;
        private Sunny.UI.UIButton btnStop;
        private Sunny.UI.UIPanel pnlPH;
        private Sunny.UI.UIRadioButton rdbPHChech3;
        private Sunny.UI.UIRadioButton rdbPHChech2;
        private Sunny.UI.UIRadioButton rdbPHChech1;
        private Sunny.UI.UIRadioButton rdbPHAuto;
        private Sunny.UI.UICheckBox chkPH;
        private Sunny.UI.UIPanel pnlElectric;
        private Sunny.UI.UIRadioButton rdbElectricCheck3;
        private Sunny.UI.UIRadioButton rdbElectricCheck2;
        private Sunny.UI.UIRadioButton rdbElectricCheck1;
        private Sunny.UI.UIRadioButton rdbElectricAuto;
        private Sunny.UI.UICheckBox chkTemp;
        private Sunny.UI.UICheckBox chkTurbidity;
        private Sunny.UI.UICheckBox chkOxygen;
        private Sunny.UI.UICheckBox chkElectric;
        private Sunny.UI.UIPanel pnlTurbidity;
        private Sunny.UI.UIRadioButton rdbTurbidityCheck3;
        private Sunny.UI.UIRadioButton rdbTurbidityCheck2;
        private Sunny.UI.UIRadioButton rdbTurbidityCheck1;
        private Sunny.UI.UIRadioButton rdbTurbidityAuto;
        private Sunny.UI.UIPanel pnlOxygen;
        private Sunny.UI.UIRadioButton rdbOxygenCheck2;
        private Sunny.UI.UIRadioButton rdbOxygenCheck1;
        private Sunny.UI.UIRadioButton rdbOxygenAuto;
        private Sunny.UI.UIButton btnCompare;
        private Sunny.UI.UICheckBox chkCalibrateTurbidity;
        private Sunny.UI.UICheckBox chkCalibrateOxygen;
        private Sunny.UI.UICheckBox chkCalibrateElectric;
        private Sunny.UI.UICheckBox chkCalibratePH;
        private Sunny.UI.UILabel uiLabel1;
        private Sunny.UI.UIGroupBox uiGroupBox1;
        private Sunny.UI.UIButton btnCalibrate;
    }
}
