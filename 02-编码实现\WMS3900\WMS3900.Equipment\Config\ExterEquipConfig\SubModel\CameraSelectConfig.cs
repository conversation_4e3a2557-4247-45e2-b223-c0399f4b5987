﻿using System.ComponentModel;
using Fpi.Camera;
using Newtonsoft.Json;

namespace Fpi.WMS3000.Equipment.Config
{
    /// <summary>
    /// 摄像机选择配置
    /// </summary>
    public class CameraSelectConfig
    {
        #region 可存储属性

        /// <summary>
        /// 取水口摄像机ID
        /// </summary>
        [Description("取水口摄像机ID")]
        public string WaterPointCameraId { get; set; }

        /// <summary>
        /// 监测站外围摄像机ID
        /// </summary>
        [Description("监测站外围摄像机ID")]
        public string StationOutsideCameraId { get; set; }

        /// <summary>
        /// 室内摄像机ID
        /// </summary>
        [Description("室内摄像机ID")]
        public string StationInsideCameraId { get; set; }

        /// <summary>
        /// 配水预处理沉砂池摄像机ID
        /// </summary>
        [Description("配水预处理沉砂池摄像机ID")]
        public string SandSinkCameraId { get; set; }

        /// <summary>
        /// 配水预处理管路摄像机ID
        /// </summary>
        [Description("配水预处理管路摄像机ID")]
        public string PipeCameraId { get; set; }

        /// <summary>
        ///五参数流通池摄像机ID
        /// </summary>
        [Description("五参数流通池摄像机ID")]
        public string FiveParamFlowPoolCameraId { get; set; }

        /// <summary>
        /// 五参数水桶摄像机ID
        /// </summary>
        [Description("五参数水桶摄像机ID")]
        public string FiveParamBucketCameraId { get; set; }

        /// <summary>
        /// 高指\COD氨氮分析仪摄像机ID
        /// </summary>
        [Description("高指\\COD氨氮分析仪摄像机ID")]
        public string CodMnNH4EquipCameraId { get; set; }

        /// <summary>
        /// 高指\COD氨氮质控仪摄像机ID
        /// </summary>
        [Description("高指\\COD氨氮质控仪摄像机ID")]
        public string CodMnNH4QCDCameraId { get; set; }

        /// <summary>
        /// 总磷总氮分析仪摄像机ID
        /// </summary>
        [Description("总磷总氮分析仪摄像机ID")]
        public string TPTNEquipCameraId { get; set; }

        /// <summary>
        /// 总磷总氮质控仪摄像机ID
        /// </summary>
        [Description("总磷总氮质控仪摄像机ID")]
        public string TPTNQCDCameraId { get; set; }

        #endregion

        #region 无需存储属性

        /// <summary>
        /// 取水口摄像机
        /// </summary>
        [Description("取水口摄像机")]
        [JsonIgnore]
        public BaseNETCamera WaterPointCamera => NETCameraManager.GetInstance().GetCameraById(WaterPointCameraId);

        /// <summary>
        /// 监测站外围摄像机
        /// </summary>
        [Description("监测站外围摄像机")]
        [JsonIgnore]
        public BaseNETCamera StationOutsideCamera => NETCameraManager.GetInstance().GetCameraById(StationOutsideCameraId);

        /// <summary>
        /// 室内摄像机
        /// </summary>
        [Description("室内摄像机")]
        [JsonIgnore]
        public BaseNETCamera StationInsideCamera => NETCameraManager.GetInstance().GetCameraById(StationInsideCameraId);

        /// <summary>
        /// 配水预处理沉砂池摄像机
        /// </summary>
        [Description("配水预处理沉砂池摄像机")]
        [JsonIgnore]
        public BaseNETCamera SandSinkCamera => NETCameraManager.GetInstance().GetCameraById(SandSinkCameraId);

        /// <summary>
        /// 配水预处理管路摄像机
        /// </summary>
        [Description("配水预处理管路摄像机")]
        [JsonIgnore]
        public BaseNETCamera PipeCamera => NETCameraManager.GetInstance().GetCameraById(PipeCameraId);

        /// <summary>
        /// 五参数流通池摄像机
        /// </summary>
        [Description("五参数流通池摄像机")]
        [JsonIgnore]
        public BaseNETCamera FiveParamFlowPoolCamera => NETCameraManager.GetInstance().GetCameraById(FiveParamFlowPoolCameraId);

        /// <summary>
        /// 五参数水桶摄像机
        /// </summary>
        [Description("五参数水桶摄像机")]
        [JsonIgnore]
        public BaseNETCamera FiveParamBucketCamera => NETCameraManager.GetInstance().GetCameraById(FiveParamBucketCameraId);

        /// <summary>
        /// 高指\COD氨氮分析仪摄像机
        /// </summary>
        [Description("高指\\COD氨氮分析仪摄像机")]
        [JsonIgnore]
        public BaseNETCamera CodMnNH4EquipCamera => NETCameraManager.GetInstance().GetCameraById(CodMnNH4EquipCameraId);

        /// <summary>
        /// 高指\COD氨氮质控仪摄像机
        /// </summary>
        [Description("高指\\COD氨氮质控仪摄像机")]
        [JsonIgnore]
        public BaseNETCamera CodMnNH4QCDCamera => NETCameraManager.GetInstance().GetCameraById(CodMnNH4QCDCameraId);

        /// <summary>
        /// 总磷总氮分析仪摄像机
        /// </summary>
        [Description("总磷总氮分析仪摄像机")]
        [JsonIgnore]
        public BaseNETCamera TPTNEquipCamera => NETCameraManager.GetInstance().GetCameraById(TPTNEquipCameraId);

        /// <summary>
        /// 总磷总氮质控仪摄像机
        /// </summary>
        [Description("总磷总氮质控仪摄像机")]
        [JsonIgnore]
        public BaseNETCamera TPTNQCDCamera => NETCameraManager.GetInstance().GetCameraById(TPTNQCDCameraId);

        #endregion

        #region 公共方法

        public void Check()
        {

        }

        #endregion
    }
}