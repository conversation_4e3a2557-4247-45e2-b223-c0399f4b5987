﻿namespace Fpi.WMS3000.Equipment.UI
{
    partial class UC_OneWCS3900CheckParam
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.gbMain = new Sunny.UI.UIGroupBox();
            this.txtCheckTime = new Sunny.UI.UITextBox();
            this.btnReadCheckTime = new Sunny.UI.UISymbolButton();
            this.btnSetCheckTime = new Sunny.UI.UISymbolButton();
            this.uiLabel5 = new Sunny.UI.UILabel();
            this.uiLabel2 = new Sunny.UI.UILabel();
            this.btnReadCheckInterVal = new Sunny.UI.UISymbolButton();
            this.btnSetCheckInterVal = new Sunny.UI.UISymbolButton();
            this.txtCheckInterVal = new Sunny.UI.UITextBox();
            this.btnReadCalcuCount = new Sunny.UI.UISymbolButton();
            this.btnSetCalcuCount = new Sunny.UI.UISymbolButton();
            this.uiLabel1 = new Sunny.UI.UILabel();
            this.txtCalcuCount = new Sunny.UI.UITextBox();
            this.gbMain.SuspendLayout();
            this.SuspendLayout();
            // 
            // gbMain
            // 
            this.gbMain.Controls.Add(this.txtCheckTime);
            this.gbMain.Controls.Add(this.btnReadCheckTime);
            this.gbMain.Controls.Add(this.btnSetCheckTime);
            this.gbMain.Controls.Add(this.uiLabel5);
            this.gbMain.Controls.Add(this.uiLabel2);
            this.gbMain.Controls.Add(this.btnReadCheckInterVal);
            this.gbMain.Controls.Add(this.btnSetCheckInterVal);
            this.gbMain.Controls.Add(this.txtCheckInterVal);
            this.gbMain.Controls.Add(this.btnReadCalcuCount);
            this.gbMain.Controls.Add(this.btnSetCalcuCount);
            this.gbMain.Controls.Add(this.uiLabel1);
            this.gbMain.Controls.Add(this.txtCalcuCount);
            this.gbMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbMain.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbMain.Location = new System.Drawing.Point(1, 1);
            this.gbMain.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbMain.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbMain.Name = "gbMain";
            this.gbMain.Padding = new System.Windows.Forms.Padding(0, 32, 0, 0);
            this.gbMain.Size = new System.Drawing.Size(254, 208);
            this.gbMain.TabIndex = 0;
            this.gbMain.Text = "参数名称";
            this.gbMain.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // txtCheckTime
            // 
            this.txtCheckTime.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtCheckTime.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtCheckTime.Location = new System.Drawing.Point(81, 46);
            this.txtCheckTime.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtCheckTime.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtCheckTime.Name = "txtCheckTime";
            this.txtCheckTime.Padding = new System.Windows.Forms.Padding(5);
            this.txtCheckTime.ShowText = false;
            this.txtCheckTime.Size = new System.Drawing.Size(60, 27);
            this.txtCheckTime.TabIndex = 0;
            this.txtCheckTime.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtCheckTime.Watermark = "";
            // 
            // btnReadCheckTime
            // 
            this.btnReadCheckTime.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnReadCheckTime.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnReadCheckTime.Location = new System.Drawing.Point(155, 46);
            this.btnReadCheckTime.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnReadCheckTime.Name = "btnReadCheckTime";
            this.btnReadCheckTime.Size = new System.Drawing.Size(43, 27);
            this.btnReadCheckTime.Symbol = 61508;
            this.btnReadCheckTime.SymbolSize = 0;
            this.btnReadCheckTime.TabIndex = 1;
            this.btnReadCheckTime.Text = "读取";
            this.btnReadCheckTime.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnReadCheckTime.Click += new System.EventHandler(this.btnReadCheckTime_Click);
            // 
            // btnSetCheckTime
            // 
            this.btnSetCheckTime.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnSetCheckTime.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSetCheckTime.Location = new System.Drawing.Point(204, 46);
            this.btnSetCheckTime.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnSetCheckTime.Name = "btnSetCheckTime";
            this.btnSetCheckTime.Size = new System.Drawing.Size(43, 27);
            this.btnSetCheckTime.Symbol = 61508;
            this.btnSetCheckTime.SymbolSize = 0;
            this.btnSetCheckTime.TabIndex = 2;
            this.btnSetCheckTime.Text = "设置";
            this.btnSetCheckTime.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSetCheckTime.Click += new System.EventHandler(this.btnSetCheckTime_Click);
            // 
            // uiLabel5
            // 
            this.uiLabel5.AutoSize = true;
            this.uiLabel5.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel5.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel5.Location = new System.Drawing.Point(5, 49);
            this.uiLabel5.Name = "uiLabel5";
            this.uiLabel5.Size = new System.Drawing.Size(74, 21);
            this.uiLabel5.TabIndex = 9;
            this.uiLabel5.Text = "核查时长";
            this.uiLabel5.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiLabel2
            // 
            this.uiLabel2.AutoSize = true;
            this.uiLabel2.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel2.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel2.Location = new System.Drawing.Point(5, 104);
            this.uiLabel2.Name = "uiLabel2";
            this.uiLabel2.Size = new System.Drawing.Size(74, 21);
            this.uiLabel2.TabIndex = 10;
            this.uiLabel2.Text = "核查间隔";
            this.uiLabel2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // btnReadCheckInterVal
            // 
            this.btnReadCheckInterVal.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnReadCheckInterVal.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnReadCheckInterVal.Location = new System.Drawing.Point(156, 101);
            this.btnReadCheckInterVal.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnReadCheckInterVal.Name = "btnReadCheckInterVal";
            this.btnReadCheckInterVal.Size = new System.Drawing.Size(43, 27);
            this.btnReadCheckInterVal.Symbol = 61508;
            this.btnReadCheckInterVal.SymbolSize = 0;
            this.btnReadCheckInterVal.TabIndex = 4;
            this.btnReadCheckInterVal.Text = "读取";
            this.btnReadCheckInterVal.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnReadCheckInterVal.Click += new System.EventHandler(this.btnReadCheckInterVal_Click);
            // 
            // btnSetCheckInterVal
            // 
            this.btnSetCheckInterVal.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnSetCheckInterVal.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSetCheckInterVal.Location = new System.Drawing.Point(205, 101);
            this.btnSetCheckInterVal.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnSetCheckInterVal.Name = "btnSetCheckInterVal";
            this.btnSetCheckInterVal.Size = new System.Drawing.Size(43, 27);
            this.btnSetCheckInterVal.Symbol = 61508;
            this.btnSetCheckInterVal.SymbolSize = 0;
            this.btnSetCheckInterVal.TabIndex = 5;
            this.btnSetCheckInterVal.Text = "设置";
            this.btnSetCheckInterVal.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSetCheckInterVal.Click += new System.EventHandler(this.btnSetCheckInterVal_Click);
            // 
            // txtCheckInterVal
            // 
            this.txtCheckInterVal.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtCheckInterVal.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtCheckInterVal.Location = new System.Drawing.Point(82, 101);
            this.txtCheckInterVal.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtCheckInterVal.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtCheckInterVal.Name = "txtCheckInterVal";
            this.txtCheckInterVal.Padding = new System.Windows.Forms.Padding(5);
            this.txtCheckInterVal.ShowText = false;
            this.txtCheckInterVal.Size = new System.Drawing.Size(60, 27);
            this.txtCheckInterVal.TabIndex = 3;
            this.txtCheckInterVal.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtCheckInterVal.Watermark = "";
            // 
            // btnReadCalcuCount
            // 
            this.btnReadCalcuCount.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnReadCalcuCount.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnReadCalcuCount.Location = new System.Drawing.Point(156, 156);
            this.btnReadCalcuCount.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnReadCalcuCount.Name = "btnReadCalcuCount";
            this.btnReadCalcuCount.Size = new System.Drawing.Size(43, 27);
            this.btnReadCalcuCount.Symbol = 61508;
            this.btnReadCalcuCount.SymbolSize = 0;
            this.btnReadCalcuCount.TabIndex = 7;
            this.btnReadCalcuCount.Text = "读取";
            this.btnReadCalcuCount.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnReadCalcuCount.Click += new System.EventHandler(this.btnReadCalcuCount_Click);
            // 
            // btnSetCalcuCount
            // 
            this.btnSetCalcuCount.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnSetCalcuCount.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSetCalcuCount.Location = new System.Drawing.Point(205, 156);
            this.btnSetCalcuCount.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnSetCalcuCount.Name = "btnSetCalcuCount";
            this.btnSetCalcuCount.Size = new System.Drawing.Size(43, 27);
            this.btnSetCalcuCount.Symbol = 61508;
            this.btnSetCalcuCount.SymbolSize = 0;
            this.btnSetCalcuCount.TabIndex = 8;
            this.btnSetCalcuCount.Text = "设置";
            this.btnSetCalcuCount.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSetCalcuCount.Click += new System.EventHandler(this.btnSetCalcuCount_Click);
            // 
            // uiLabel1
            // 
            this.uiLabel1.AutoSize = true;
            this.uiLabel1.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel1.Location = new System.Drawing.Point(5, 159);
            this.uiLabel1.Name = "uiLabel1";
            this.uiLabel1.Size = new System.Drawing.Size(74, 21);
            this.uiLabel1.TabIndex = 11;
            this.uiLabel1.Text = "方差组数";
            this.uiLabel1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // txtCalcuCount
            // 
            this.txtCalcuCount.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtCalcuCount.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtCalcuCount.Location = new System.Drawing.Point(82, 156);
            this.txtCalcuCount.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtCalcuCount.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtCalcuCount.Name = "txtCalcuCount";
            this.txtCalcuCount.Padding = new System.Windows.Forms.Padding(5);
            this.txtCalcuCount.ShowText = false;
            this.txtCalcuCount.Size = new System.Drawing.Size(60, 27);
            this.txtCalcuCount.TabIndex = 6;
            this.txtCalcuCount.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtCalcuCount.Watermark = "";
            // 
            // UC_OneWCS3900CheckParam
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.gbMain);
            this.Name = "UC_OneWCS3900CheckParam";
            this.Padding = new System.Windows.Forms.Padding(1);
            this.Radius = 10;
            this.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.Size = new System.Drawing.Size(256, 210);
            this.gbMain.ResumeLayout(false);
            this.gbMain.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion
        private Sunny.UI.UIGroupBox gbMain;
        private Sunny.UI.UITextBox txtCheckTime;
        private Sunny.UI.UISymbolButton btnReadCheckTime;
        private Sunny.UI.UISymbolButton btnSetCheckTime;
        private Sunny.UI.UILabel uiLabel5;
        private Sunny.UI.UILabel uiLabel2;
        private Sunny.UI.UISymbolButton btnReadCheckInterVal;
        private Sunny.UI.UISymbolButton btnSetCheckInterVal;
        private Sunny.UI.UITextBox txtCheckInterVal;
        private Sunny.UI.UISymbolButton btnReadCalcuCount;
        private Sunny.UI.UISymbolButton btnSetCalcuCount;
        private Sunny.UI.UILabel uiLabel1;
        private Sunny.UI.UITextBox txtCalcuCount;
    }
}
