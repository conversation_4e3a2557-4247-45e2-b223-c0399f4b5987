﻿using System;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Threading;
using System.Windows.Forms;
using Fpi.DB;
using Fpi.Devices;
using Fpi.UI.Common.PC;
using Fpi.UI.PC;
using Fpi.Util;
using Fpi.Util.StatusForm;
using Fpi.WMS3000.DB;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    public partial class UC_SIA3900DiagnosisQueryData : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 关联的诊断记录源
        /// </summary>
        public Device _device;

        #endregion

        #region 构造

        public UC_SIA3900DiagnosisQueryData()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        /// <summary>
        /// 构造
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void UC_SIA3900DiagnosisQueryData_Load(object sender, EventArgs e)
        {
            InitialDateTimePicker();
            SetDataGridViewHead();
        }

        /// <summary>
        /// 开始查询
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnQuery_Click(object sender, EventArgs e)
        {
            try
            {
                pnlTop.Enabled = false;
                lblPage.Text = @"?/?";

                pagination.PageChanged -= new UIPagination.OnPageChangeEventHandler(pagination_PageChanged);

                // 清空界面数据
                dgvData.Rows.Clear();

                pagination.TotalCount = 0;

                // 检查查询条件
                Check();

                // 查询数据数量
                QueryDataCount();

                // 刷新页面显示
                FlushView();
            }
            catch (Exception ex)
            {
                FpiMessageBox.ShowError($"查询数据错误：{ex.Message}");
            }
            finally
            {
                pagination.PageChanged += new UIPagination.OnPageChangeEventHandler(pagination_PageChanged);

                // 交出CPU控制权，处理消息队列中的其他消息
                Application.DoEvents();
                pnlTop.Enabled = true;
            }
        }

        /// <summary>
        /// 导出数据至excel
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnExcelExport_Click(object sender, EventArgs e)
        {
            try
            {
                pnlTop.Enabled = false;
                if (dgvData.Rows.Count == 0)
                {
                    throw new Exception("当前无数据可导出！");
                }

                // 导出时弹窗提示选取导出目录，及文件名称
                string filePath = Application.StartupPath + "\\query\\";
                if (!Directory.Exists(filePath))
                {
                    Directory.CreateDirectory(filePath);
                }

                // 第一次调用设置初始文件
                if (string.IsNullOrEmpty(saveFileDialog.FileName))
                {
                    saveFileDialog.InitialDirectory = filePath;
                }

                saveFileDialog.FileName =
                    $"{filePath}{_device?.name}设备诊断记录数据_{dtpStartTime.Value:yyyy-MM-dd HH-mm-ss}_{dtpEndTime.Value:yyyy-MM-dd HH-mm-ss}.xlsx";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    Stopwatch watch = new Stopwatch();
                    watch.Start();

                    FileExportHelper.SaveDataGridViewToExcelFile(dgvData, saveFileDialog.FileName);

                    watch.Stop();
                    float time = watch.ElapsedMilliseconds / 1000f;
                    MessageNotifier.ShowInfo($"导出操作耗时：{time}秒。");

                    if (FpiMessageBox.ShowQuestion("导出成功！是否定位到文件所在位置？") == DialogResult.Yes)
                    {
                        var psi = new ProcessStartInfo("Explorer.exe")
                        {
                            Arguments = "/e,/select," + saveFileDialog.FileName
                        };
                        // 打开导出文件所在位置
                        Process.Start(psi);
                    }
                }

            }
            catch (Exception ex)
            {
                FpiMessageBox.ShowError("数据导出错误:" + ex.Message);
            }
            finally
            {
                // 交出CPU控制权，处理消息队列中的其他消息
                Application.DoEvents();
                pnlTop.Enabled = true;
            }
        }

        /// <summary>
        /// 点击翻页
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="pagingSource"></param>
        /// <param name="curPage">第几页，和界面对应，从1开始，取数据要用pageIndex - 1</param>
        /// <param name="onePageCount">单页数据量，也就是PageSize值</param>
        private void pagination_PageChanged(object sender, object pagingSource, int curPage, int onePageCount)
        {
            try
            {
                this.lblPage.Text = $@"{curPage}/{pagination.PageCount}";

                // 显示等待界面
                UIFormServiceHelper.ShowWaitForm(this.ParentForm, "数据查询中，请稍候...");
                // 线程稍微停一下，否则下面执行很快时等待界面关闭不了。推测是windows消息通信机制问题。
                Thread.Sleep(50);

                // 清空原数据
                dgvData.Rows.Clear();

                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);

                string strSql = $"select * from {DbConfig.DEVICE_DIAGNOSISRECORDS_TABLE}  where datatime>='{dtpStartTime.Value.ToString(DbConfig.DATETIME_FORMAT)}' and datatime<='{dtpEndTime.Value.ToString(DbConfig.DATETIME_FORMAT)}'and sourceid='{_device?.id}' order by datatime desc limit {onePageCount} offset {onePageCount * (curPage - 1)}";
                IDataReader reader = DbAccess.ExecuteQueryReturnDataReader(strSql);

                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);

                if (reader != null)
                {
                    FillDataGridViewData(reader, curPage, onePageCount);
                }
            }
            finally
            {
                // 线程切换，防止最终进度界面无法关闭
                Thread.Sleep(100);
                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
            }
        }

        private void FillDataGridViewData(IDataReader reader, int curPage, int onePageCount)
        {
            try
            {
                // 打开进度条界面
                FpiStatusFormService.ShowStatusForm(onePageCount, "数据渲染中，请稍候...");

                // 进度条序号值
                int currentStep = 1;
                // 进度条满值
                int currentPageCount = onePageCount;
                // 若当前是最后一页，则数据不足onePageCount
                if (curPage * onePageCount > pagination.TotalCount)
                {
                    currentPageCount = pagination.TotalCount - (curPage - 1) * onePageCount;
                }

                // 表格中数据序号值
                int rowIndex = (curPage - 1) * onePageCount + 1;

                while (reader.Read())
                {
                    int index = dgvData.Rows.Add();
                    DataGridViewRow dr = dgvData.Rows[index];
                    dr.Cells[0].Value = rowIndex++;

                    dr.Cells[1].Value = Convert.ToDateTime(reader["datatime"]).ToString(DbConfig.DATETIME_FORMAT);

                    string message = reader["task"].ToString();
                    dr.Cells[2].Value = message;
                    dr.Cells[2].ToolTipText = message;
                    dr.Cells[2].Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    dr.Cells[3].Value = reader["result"].ToString();
                }

                FpiStatusFormService.SetDescription($"数据渲染中[{currentStep++}/{currentPageCount}]......");
                FpiStatusFormService.StepIt();
            }
            finally
            {
                // 线程切换，防止最终进度界面无法关闭
                Thread.Sleep(100);
                // 隐藏进度条界面
                FpiStatusFormService.HideStatusForm();
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 设置关联的数据源ID
        /// </summary>
        /// <param name="sourceId"></param>
        public void SetTragetDevice(Device device)
        {
            _device = device;
        }

        #endregion

        #region 私有方法

        #region 数据查询

        /// <summary>
        /// 检查查询条件输入是否合格
        /// </summary>
        private void Check()
        {
            if (_device == null)
            {
                throw new Exception("页面关联的数据源ID未配置！");
            }

            if (dtpStartTime.Value > dtpEndTime.Value)
            {
                throw new Exception("起始时间应不可大于结束时间！");
            }

            if (string.IsNullOrEmpty(txtRecordCount.Text))
            {
                throw new Exception("每页记录数输入不可为空！");
            }

            if (!int.TryParse(txtRecordCount.Text, out int onePageCount))
            {
                throw new Exception("每页记录数输入异常，应为大于1的整数！");
            }

            if (onePageCount < 1)
            {
                throw new Exception("每页记录数值不可小于1！");
            }

            pagination.PageSize = onePageCount;
        }

        /// <summary>
        /// 更新数据内容
        /// </summary>
        private void QueryDataCount()
        {
            int recordCount = 0;
            try
            {
                string strSql = $"select count(*) from {DbConfig.DEVICE_DIAGNOSISRECORDS_TABLE} where datatime>='{dtpStartTime.Value.ToString(DbConfig.DATETIME_FORMAT)}' and datatime<='{dtpEndTime.Value.ToString(DbConfig.DATETIME_FORMAT)}' and sourceid='{_device?.id}' order by datatime desc";
                recordCount = DbAccess.QueryRecordCount(strSql);
            }
            catch
            {
                throw new Exception("数据库连接异常!");
            }

            if (recordCount > 0)
            {
                pagination.TotalCount = recordCount;
            }
            else
            {
                throw new Exception("无当前条件下的查询数据!");
            }
        }

        /// <summary>
        /// 刷新页面显示
        /// </summary>
        private void FlushView()
        {
            if (pagination.ActivePage != 1)
            {
                pagination.ActivePage = 1;
            }
            else
            {
                pagination_PageChanged(null, null, 1, pagination.PageSize);
            }
        }

        #endregion

        #region 控件

        /// <summary>
        /// 初始化表头显示
        /// </summary>
        private void SetDataGridViewHead()
        {
            dgvData.ClearColumns();

            var col = dgvData.AddColumn("序号", "num");
            col.FillWeight = 30;
            col = dgvData.AddColumn("诊断时间", "datatime");
            col.FillWeight = 150;
            col = dgvData.AddColumn("诊断任务", "task");
            col.FillWeight = 200;
            col = dgvData.AddColumn("诊断结果", "result");
            col.FillWeight = 100;
        }

        /// <summary>
        /// 初始化DateTimePicker控件的值
        /// </summary>
        private void InitialDateTimePicker()
        {
            DateTime now = DateTime.Now;
            var startTime = new DateTime(now.Year, now.Month, now.Day, 0, 0, 0);
            dtpStartTime.Value = startTime;
            dtpEndTime.Value = startTime.AddDays(1);
        }

        #endregion

        #endregion
    }
}