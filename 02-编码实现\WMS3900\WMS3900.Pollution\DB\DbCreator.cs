﻿using Fpi.DB.Manager;
using Fpi.HB.Business.HisData;
using Fpi.Util.Interfaces.Initialize;

namespace Fpi.WMS3900.Pollution.DB
{
    /// <summary>
    /// 数据库创建
    /// </summary>
    public class DbCreator : IInitialization
    {
        #region 建表方法

        /// <summary>
        /// 创建污染源数据表
        /// </summary>
        private void CreatePollutionDataTable()
        {
            FpiTable table = new FpiTable(DbConfig.POLLUTION_MEASURE_DATA_TABLE);
            FpiColumn col = new FpiColumn("id", ColumnType.Int, true, true);
            table.AddColumn(col);

            // 数据时间
            col = new FpiColumn("datatime", ColumnType.Datetime, "idx_datatime_datatype");
            table.AddColumn(col);
            // 数据类型(ePollutionDataType)
            col = new FpiColumn("datatype", ColumnType.Int, "idx_datatime_datatype");
            table.AddColumn(col);
            // 此时段累计流量
            col = new FpiColumn(DbConfig.TOTAL_FLOW, ColumnType.Double);
            table.AddColumn(col);
            // 各统计量因子
            QueryGroup queryGroup = ReportManager.GetInstance().GetFirstQueryGroup();
            if(queryGroup != null)
            {
                foreach(QueryNode queryNode in queryGroup.QueryNodes)
                {
                    col = new FpiColumn(DbConfig.PREFIX_F + queryNode.id.Trim(), ColumnType.Double);
                    table.AddColumn(col);
                    col = new FpiColumn(DbConfig.PREFIX_F + queryNode.id + DbConfig.POSTFIX, ColumnType.Int, 2);
                    table.AddColumn(col);
                }
            }

            FpiDataBase.GetInstance().AddTable(table);
            table.CreateTable();
        }

        #endregion

        #region IInitialization 成员

        public void Initialize()
        {
            // 创建污染源数据表
            this.CreatePollutionDataTable();
        }

        #endregion
    }
}