﻿using Fpi.Data.FuncServer;
using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.Equipment.Interface;

namespace Fpi.WMS3000.Pollution.UI.FlowControls
{
    public class FuncService : BaseServer
    {
        /// <summary>
        /// 获取当前供样桶内水量
        /// </summary>
        /// <returns></returns>
        [ServiceAttribute("判断当前供样桶内水量是否够测量。水量单位ml。", "够用，返回ture;不够，返回false", "水量合格值")]
        public static bool GetSupplyBucketWaterVolume(int LowerLimit)
        {
            int waterVolume = 0;
            // 获取当前小时应使用的供样桶
            var _currentSupplyBucket = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.CurrentSupplyBucketSet;

            // 获取采样器桶内水量
            if(ExterEquipConfigManager.GetInstance().DeviceSelect.SampleDevice is IMixedSampleDeviceOperation sampleDevice)
            {
                waterVolume = sampleDevice.GetBucketWaterVolume(_currentSupplyBucket);
            }

            return waterVolume > LowerLimit;
        }
    }
}