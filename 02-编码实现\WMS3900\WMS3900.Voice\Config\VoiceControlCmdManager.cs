﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using Fpi.UI.PC.Config;
using Fpi.Util.Interfaces.Initialize;
using Fpi.WMS3000.Voice.Helper;
using Fpi.Xml;
using NLPutils;
using NLPutils.Command;

namespace Fpi.WMS3000.Voice.Config
{
    /// <summary>
    /// 语音控制命令管理模块
    /// </summary>
    public class VoiceControlCmdManager : BaseNode, IInitialization
    {
        #region 属性字段

        /// <summary>
        /// 是否启用语音控制
        /// </summary>
        public bool AutoStart;

        /// <summary>
        /// 命令列表
        /// </summary>
        public NodeList VoiceControlCmds = new NodeList();

        #endregion

        #region 单例

        private static object syncObj = new object();
        private static VoiceControlCmdManager instance = null;

        private VoiceControlCmdManager()
        {
            loadXml();
        }

        public static VoiceControlCmdManager GetInstance()
        {
            lock(syncObj)
            {
                if(instance == null)
                {
                    instance = new VoiceControlCmdManager();
                }
            }
            return instance;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 获取指定ID的原子操作
        /// </summary>
        /// <param name="cmdId"></param>
        /// <returns></returns>
        public VoiceControlCmd GetCmdById(string cmdId)
        {
            return VoiceControlCmds.FindNode(cmdId) as VoiceControlCmd;
        }

        /// <summary>
        /// 获取操作列表
        /// </summary>
        /// <returns></returns>
        public List<VoiceControlCmd> GetAllVoiceControlCmd()
        {
            var cmdlist = new List<VoiceControlCmd>();
            foreach(VoiceControlCmd cmd in VoiceControlCmds)
            {
                cmdlist.Add(cmd);
            }
            return cmdlist.OrderBy(cmd => cmd.name).ToList();
        }

        /// <summary>
        /// 启动服务
        /// </summary>
        public void StartVoiceServer()
        {
            UpdateCommandList();

            NLPapi.CommandEvent += ExecuteCommand;

            UIManager.GetInstance().MainForm.Invoke(new Action(() =>
            {
                if(!NLPapi.StartSpeechRecognition(out string msg))
                {
                    VoiceLogHelper.Info($"开启语音控制服务失败：{msg}");
                }
                else
                {

                    VoiceLogHelper.Info($"开启语音控制服务成功！");
                }
            }));
        }

        /// <summary>
        /// 停止服务
        /// </summary>
        public void StopVoiceServer()
        {
            NLPapi.CommandEvent -= ExecuteCommand;

            UIManager.GetInstance().MainForm.Invoke(new Action(() =>
            {
                if(!NLPapi.StopSpeechRecognition(out string msg))
                {
                    VoiceLogHelper.Info($"停止语音控制服务失败：{msg}");
                }
                else
                {
                    VoiceLogHelper.Info($"停止语音控制服务成功！");

                }
            }));
        }

        /// <summary>
        /// 更新语音控制命令集
        /// </summary>
        public void UpdateCommandList()
        {
            // 下发语音命令集合
            List<VoiceCommand> vcmds = GetInstance().GetAllVoiceControlCmd().Select(s => new VoiceCommand
            {
                SubCmdList = s.SubCmdList,
                id = s.id,
                name = s.name,
                isNeedConfirm = s.isNeedConfirm,
                MethodFull = null
            }).ToList();

            if(!NLPapi.SetVoiceCommandList(vcmds, out string errorMsg))
            {
                VoiceLogHelper.Info($"语音控制命令集下发失败：{errorMsg}");
            }
            else
            {
                VoiceLogHelper.Info($"语音控制命令集下发成功，下发数量：{vcmds.Count}");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 执行返回的命令
        /// </summary>
        /// <param name="command"></param>
        private void ExecuteCommand(ReturnCommand command)
        {
            try
            {
                string str = string.Empty;
                if(GetInstance().GetCmdById(command.id) is not VoiceControlCmd vcmd || vcmd.GetOperationImpl() is not CustomVoiceCmd customCmd)
                {
                    ShowSpeakText($"抱歉，{command.SubCmd}指令未找到");
                    return;
                }
                if(string.IsNullOrEmpty(command.Parameter))
                {
                    str = customCmd.CustomDo();
                }
                else if(int.TryParse(command.Parameter, out int param))
                {
                    str = customCmd.CustomDo(param);
                }

                if(!string.IsNullOrEmpty(str))
                {
                    ShowSpeakText(str);
                }
            }
            catch(Exception ex)
            {
                string txt = $"{command.SubCmd}指令控制失败";
                ShowSpeakText(txt);
                VoiceLogHelper.Info($"{txt}：" + ex);
            }
        }

        /// <summary>
        /// 显示&广播文字
        /// </summary>
        /// <param name="txt"></param>
        private static void ShowSpeakText(string txt)
        {
            NLPapi.SpeakText(StringTranslation(txt), true); //转换一些显示可说不一致的字符
        }

        /// <summary>
        /// 词语字符串转换
        /// </summary>
        /// <returns></returns>
        private static string StringTranslation(string input)
        {
            try
            {
                string output = input;
                //开→关
                output = output.Replace("状态【关→开】", "已打开");
                output = output.Replace("状态【开→关】", "已关闭");
                output = output.Replace("关→开", "已开启");
                output = output.Replace("开→关", "已关闭");

                //数字变化 箭头→
                output = Regex.Replace(output, @"(?:\d+\.?\d*|\.\d+)→", "");

                //→
                output = output.Replace("→", "变为");

                //v 电压伏
                output = Regex.Replace(output, @"V$|v$", "伏");

                //℃ 温度
                output = output.Replace("℃", "摄氏度");

                //MM/dd HH
                Match match = Regex.Match(output, @"(\d{2})/(\d{2}) (\d{2})");
                if(match.Success)
                {
                    int mm = int.Parse(match.Groups[1].Value);
                    int dd = int.Parse(match.Groups[2].Value);
                    int hh = int.Parse(match.Groups[3].Value);

                    output = output.Replace(match.Groups[0].Value, $"{mm}月{dd}日{hh}时"); //时间转换
                }

                output = output.Replace("NH4", "氨氮");
                output = output.Replace("CODMN", "高猛酸盐指数");
                output = output.Replace("TP", "总磷");
                output = output.Replace("TN", "总氮");

                return output;
            }
            catch(Exception)
            {
                return input;
            }
        }

        #endregion

        #region IInitialization

        public void Initialize()
        {
            try
            {
                if(AutoStart)
                {
                    StartVoiceServer();
                }
            }
            catch(Exception ex)
            {
                VoiceLogHelper.Info($"语音控制服务初始化失败：{ex.Message}");
            }
        }

        #endregion
    }
}