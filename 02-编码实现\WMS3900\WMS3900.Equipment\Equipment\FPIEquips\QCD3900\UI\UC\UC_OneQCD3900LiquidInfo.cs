﻿using System;
using System.Windows.Forms;
using Fpi.UI.Common.PC;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.Equipment.QCD3900;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    /// <summary>
    /// 单个试剂状态显示
    /// </summary>
    public partial class UC_OneQCD3900LiquidInfo : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 对应的试剂信息类
        /// </summary>
        private OneQCD3900LiquidParamInfo _liquidInfo;

        /// <summary>
        /// 对应设备
        /// </summary>
        private QCD3900Equip _device;

        private const string ErrorInfo = "— — — — —";

        #endregion

        #region 构造

        public UC_OneQCD3900LiquidInfo()
        {
            InitializeComponent();
        }

        public UC_OneQCD3900LiquidInfo(QCD3900Equip device, OneQCD3900LiquidParamInfo liquidInfo, bool IsWasteLiquid = false, bool isWater = false) : this()
        {
            _device = device;
            _liquidInfo = liquidInfo;

            if(_liquidInfo != null)
            {
                gbMain.Text = _liquidInfo.Name;
                if(IsWasteLiquid)
                {
                    lblLiquidResidualTitle.Text = "累积量";
                    lblPGTitle.Text = "已用时长";
                    txtGuaranteePeriod.Visible = btnUpdataGuaranteePeriod.Visible = false;
                }
                if(isWater)
                {
                    lblPGTitle.Text = "已用时长";
                    txtGuaranteePeriod.Visible = btnUpdataGuaranteePeriod.Visible = false;
                }
            }
            RefreshUI();
        }

        #endregion

        #region 公共方法

        internal void RefreshUI()
        {
            if(_liquidInfo != null)
            {
                lblChangeTime.Text = _liquidInfo.ChangeTime == DateTime.MinValue ? ErrorInfo :
                      _liquidInfo.ChangeTime.ToString(DbConfig.DATETIME_FORMAT);
                lblLiquidTotal.Text = float.IsNaN(_liquidInfo.Total) ? ErrorInfo : _liquidInfo.Total.ToString("F2") + "ml";
                lblLiquidResidual.Text = float.IsNaN(_liquidInfo.Residual) ? ErrorInfo : _liquidInfo.Residual.ToString("F2") + "ml";
                lblGuaranteePeriod.Text = _liquidInfo.GuaranteePeriod == -1 ? ErrorInfo : _liquidInfo.GuaranteePeriod.ToString() + "天";
            }
        }

        #endregion

        #region 事件

        private void btnReplacement_Click(object sender, EventArgs e)
        {
            try
            {
                if(FpiMessageBox.ShowQuestion($"确认更换{_liquidInfo.Name}，重置试剂使用信息？") == DialogResult.Yes)
                {
                    _device.WriteInt32ParamToDevice((byte)(_liquidInfo.StartIndex + 20), (int)DateTime.Now.SecondsSince1970());

                    FpiMessageBox.ShowInfo($"触发更换{_liquidInfo.Name}成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"触发更换{_liquidInfo.Name}出错:{ex.Message}");
            }
        }

        private void btnModifyLiquidTotal_Click(object sender, EventArgs e)
        {
            try
            {
                if(!int.TryParse(txtLiquidTotal.Text, out int total) || total <= 0)
                {
                    throw new Exception("总量信息输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置总量为{total:F2}？") == DialogResult.Yes)
                {
                    _device.WriteFloatParamToDevice((byte)(_liquidInfo.StartIndex + 10), total);

                    FpiMessageBox.ShowInfo($"修改{_liquidInfo.Name}总量成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"修改{_liquidInfo.Name}总量出错:{ex.Message}");
            }
        }

        /// <summary>
        /// 更改保质期
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnUpdataGuaranteePeriod_Click(object sender, EventArgs e)
        {
            try
            {
                if(!int.TryParse(txtGuaranteePeriod.Text, out int data) || data <= 0)
                {
                    throw new Exception("保质期信息输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置保质期为{data:F2}？") == DialogResult.Yes)
                {
                    _device.WriteInt16ParamToDevice((byte)(0x28 + (_liquidInfo.StartIndex - 0x28) / 2 + 0x1E), (short)data);

                    FpiMessageBox.ShowInfo($"修改{_liquidInfo.Name}保质期成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"修改{_liquidInfo.Name}保质期出错:{ex.Message}");
            }
        }

        #endregion    
    }
}