﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using Fpi.Data.Config;
using Fpi.Devices;
using Fpi.HB.Business.HisData;
using Fpi.Util.StatusForm;
using Fpi.WMS3000.Equipment.Interface;
using Fpi.WMS3000.UI.DataQuery;
using Fpi.WMS3900.Pollution.DB;

namespace Fpi.WMS3000.Pollution.UI.DataQuery
{
    /// <summary>
    /// 污染源日数据查询
    /// </summary>
    public partial class UC_QueryDayPollutionData : UC_QueryDataBase
    {
        #region 字段属性

        private List<QueryNode> _queryNodes = new List<QueryNode>();

        #endregion

        #region 构造

        public UC_QueryDayPollutionData()
        {
            InitializeComponent();

            TitleName = "日数据";
            DBTableName = DbConfig.POLLUTION_MEASURE_DATA_TABLE;
            AddWhereSqlStr = $" and datatype={(int)ePollutionDataType.日数据} ";

            QueryGroup queryGroup = ReportManager.GetInstance().GetFirstQueryGroup();
            if(queryGroup != null)
            {
                foreach(QueryNode queryNode in queryGroup.QueryNodes)
                {
                    this._queryNodes.Add(queryNode);
                }
            }

            SelectSqlStr = BuildSelectString();
        }

        #endregion

        #region 公共方法（重写）

        /// <summary>
        /// 设置表头
        /// </summary>
        protected override void SetDataGridViewHead()
        {
            dgvData.ClearColumns();

            var col = dgvData.AddColumn("序号", "num");
            col.Width = 50;
            col.Frozen = true;
            col = dgvData.AddColumn("数据时间", "datatime");
            col.Width = 200;
            col.Frozen = true;

            //数据列
            foreach(QueryNode queryNode in _queryNodes)
            {
                col = dgvData.AddColumn($"{queryNode.name}({queryNode.UnitId})", queryNode.id);
                col.Width = 180;

                // 将因子对应的设备绑定到对应的列
                foreach(Device dev in DeviceManager.GetInstance().GetDeviceList())
                {
                    string sourceid = dev.GetDevTargetPolNode()?.id;
                    if(!string.IsNullOrEmpty(sourceid) && sourceid.Equals(queryNode.id) && dev is IDeviceKeyParams)
                    {
                        col.Tag = dev;
                        break;
                    }
                }
            }

            col = dgvData.AddColumn("时段流量(m^3)", DbConfig.TOTAL_FLOW);
            col.Width = 200;
        }

        /// <summary>
        /// 表格中填充数据
        /// </summary>
        protected override void FillDataGridViewData(IDataReader reader, int curPage, int onePageCount)
        {
            try
            {
                // 打开进度条界面
                FpiStatusFormService.ShowStatusForm(onePageCount, "数据渲染中，请稍候...");

                // 进度条序号值
                int currentStep = 1;
                // 进度条满值
                int currentPageCount = onePageCount;
                // 若当前是最后一页，则数据不足onePageCount
                if(curPage * onePageCount > pagination.TotalCount)
                {
                    currentPageCount = pagination.TotalCount - (curPage - 1) * onePageCount;
                }

                // 表格中数据序号值
                int rowIndex = (curPage - 1) * onePageCount + 1;

                while(reader.Read())
                {
                    int index = dgvData.Rows.Add();
                    DataGridViewRow dr = dgvData.Rows[index];
                    dr.Cells[0].Value = rowIndex++;

                    dr.Cells[1].Value = Convert.ToDateTime(reader["datatime"]).ToString(DbConfig.DATETIME_FORMAT);

                    for(int i = 0; i < _queryNodes.Count; i++)
                    {
                        StringBuilder sb = new StringBuilder();

                        QueryNode queryNode = _queryNodes[i];
                        ValueNode valueNode = DataManager.GetInstance().GetValueNodeById(queryNode.id);

                        double value;
                        string valueString = string.Empty;
                        string valueState = string.Empty;
                        try
                        {
                            value = Convert.ToDouble(reader[$"{DbConfig.PREFIX_F}{valueNode.id}"]);

                            if(!string.IsNullOrEmpty(queryNode.UnitId)) //转换单位
                            {
                                value = UnitManager.GetInstance()
                                    .TransFromSelfUnitValue(valueNode, value, queryNode.UnitId);
                            }

                            valueString = value.ToString("F" + queryNode.Dec);
                        }
                        catch(Exception)
                        {
                            value = double.NaN;
                            valueString = "--";
                        }
                        sb.Append(valueString);

                        ///标志数据
                        try
                        {
                            valueState = Convert.ToInt32(reader[$"{DbConfig.PREFIX_F}{valueNode.id}{DbConfig.POSTFIX}"]).ToString();
                        }
                        catch(Exception)
                        {
                            valueState = string.Empty;
                        }

                        if(valueState != string.Empty)
                        {
                            string state = ((eValueNodeState)int.Parse(valueState)).ToString();
                            sb.Append("(").Append(state).Append(")");
                        }

                        dr.Cells[i + 2].Value = sb.ToString();
                    }

                    // 时段流量
                    string totalFlowString = string.Empty;
                    try
                    {
                        totalFlowString = Convert.ToDouble(reader[DbConfig.TOTAL_FLOW]).ToString("F3");
                    }
                    catch
                    {
                        totalFlowString = "--";
                    }
                    // 设置时段流量
                    dr.Cells[_queryNodes.Count + 2].Value = totalFlowString;

                    FpiStatusFormService.SetDescription($"数据渲染中[{currentStep++}/{currentPageCount}]......");
                    FpiStatusFormService.StepIt();
                }
            }
            finally
            {
                // 线程切换，防止最终进度界面无法关闭
                Thread.Sleep(100);
                // 隐藏进度条界面
                FpiStatusFormService.HideStatusForm();
            }
        }

        #endregion

        #region 私有方法

        private string BuildSelectString()
        {
            StringBuilder sb = new StringBuilder($"select datatime,");
            foreach(QueryNode node in _queryNodes)
            {
                sb.Append($"{DbConfig.PREFIX_F}{node.id},");
                sb.Append($"{DbConfig.PREFIX_F}{node.id}{DbConfig.POSTFIX},");

            }
            sb.Append(DbConfig.TOTAL_FLOW);
            return sb.ToString();
        }

        #endregion
    }
}