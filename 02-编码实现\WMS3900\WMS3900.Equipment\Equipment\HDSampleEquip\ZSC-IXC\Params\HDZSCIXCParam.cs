﻿using System;
using System.ComponentModel;
using Fpi.Communication.Converter;
using Fpi.WMS3000.Equipment.Common.CustomAttribute;

namespace Fpi.WMS3000.Equipment.HD
{
    /// <summary>
    /// 恒达采样器ZSC-IXC参数
    /// </summary>
    public class HDZSCIXCParam
    {
        #region 字段属性

        /// <summary>
        /// 设备型号
        /// </summary>
        [Description("设备型号")]
        public eDeviceVersionType DeviceVersion { get; set; } = (eDeviceVersionType)(-1);

        /// <summary>
        /// 设备状态
        /// </summary>
        [Description("设备状态")]
        public eDeviceStatusType DeviceStatus { get; set; } = (eDeviceStatusType)(-1);

        /// <summary>
        /// A桶状态
        /// </summary>
        [Description("A桶状态")]
        public eBucketStatusType BucketAStatus { get; set; } = (eBucketStatusType)(-1);

        /// <summary>
        /// B桶状态
        /// </summary>
        [Description("B桶状态")]
        public eBucketStatusType BucketBStatus { get; set; } = (eBucketStatusType)(-1);

        /// <summary>
        /// 当前采样量 单位ml
        /// </summary>
        [Description("当前采样量")]
        public int CurrentSamplingVolume { get; set; } = -1;

        /// <summary>
        /// 当前供样时间 单位分钟
        /// </summary>
        [Description("当前供样时间")]
        public int CurrentSupplyTime { get; set; } = -1;

        /// <summary>
        /// 当前留样量 单位ml
        /// </summary>
        [Description("当前留样量")]
        public int CurrentRetainVolume { get; set; } = -1;

        /// <summary>
        /// 当前瓶号
        /// </summary>
        [Description("当前瓶号")]
        public int CurrentBottleNumber { get; set; } = -1;

        /// <summary>
        /// 当前报警状态
        /// </summary>
        [Description("当前报警状态")]
        public eAlarmStatusType CurrentAlarmStatus { get; set; } = (eAlarmStatusType)(-1);

        /// <summary>
        /// 系统时间
        /// </summary>
        [Description("系统时间")]
        public DateTime SystemTime { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 门禁状态
        /// </summary>
        [Description("门禁状态")]
        public eDoorStatusType DoorStatus { get; set; } = (eDoorStatusType)(-1);

        /// <summary>
        /// 最近一次门禁操作时间
        /// </summary>
        [Description("最近一次门禁操作时间")]
        public DateTime LastDoorOpTime { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 最近一次门禁操作密码
        /// </summary>
        [Description("最近一次门禁操作密码")]
        public string LastDoorPw { get; set; }

        /// <summary>
        /// 当前门禁密码
        /// </summary>
        [Description("当前门禁密码")]
        //[Visible(false)]
        public string CurrentDoorPw { get; set; }

        /// <summary>
        /// 维护密码
        /// </summary>
        [Description("维护密码")]
        [Visible(false)]
        public string MaintainPw { get; set; }

        /// <summary>
        /// 冰柜温度 单位℃
        /// </summary>
        [Description("冰柜温度")]
        public double FreezerTemperature { get; set; } = double.NaN;

        /// <summary>
        /// 报警信息位标志
        /// </summary>
        [Description("报警信息位标志")]
        [Visible(false)]
        public int AlarmFlags { get; set; } = -1;

        /// <summary>
        /// A桶采水量
        /// </summary>
        [Description("A桶采水量")]
        public int BucketAWaterVolume { get; set; } = -1;

        /// <summary>
        /// A桶采水次数
        /// </summary>
        [Description("A桶采水次数")]
        public int BucketASampleTime { get; set; } = -1;

        /// <summary>
        /// B桶采水量
        /// </summary>
        [Description("B桶采水量")]
        public int BucketBWaterVolume { get; set; } = -1;

        /// <summary>
        /// B桶采水次数
        /// </summary>
        [Description("B桶采水次数")]
        public int BucketBSampleTime { get; set; } = -1;

        /// <summary>
        /// 最近一次采样时间
        /// </summary>
        [Description("最近一次采样时间")]
        public DateTime LastSamplingTime { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 最近一次供样时间
        /// </summary>
        [Description("最近一次供样时间")]
        public DateTime LastSupplyTime { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 最近一次留样时间
        /// </summary>
        [Description("最近一次留样时间")]
        public DateTime LastRetainTime { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 留样瓶号
        /// </summary>
        [Description("留样瓶号")]
        public int RetainBottleNumber { get; set; } = -1;

        #endregion

        #region 构造

        public HDZSCIXCParam()
        {
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 更新第一包参数
        /// </summary>
        /// <param name="data">数据字节数组</param>
        /// <param name="startIndex">起始索引</param>
        public void UpdateParamPackageOne(byte[] data, int startIndex)
        {
            if(data == null || data.Length < startIndex + 74)
            {
                throw new ArgumentException("读取第一包参数回应数据不完整！");
            }
            DeviceVersion = (eDeviceVersionType)data[startIndex + 1];
            DeviceStatus = (eDeviceStatusType)data[startIndex + 3];
            BucketAStatus = (eBucketStatusType)data[startIndex + 7];
            BucketBStatus = (eBucketStatusType)data[startIndex + 9];
            CurrentSamplingVolume = DataConverter.GetInstance().ToInt32(data, startIndex + 12);
            CurrentBottleNumber = DataConverter.GetInstance().ToInt32(data, startIndex + 14);
            CurrentSupplyTime = DataConverter.GetInstance().ToInt32(data, startIndex + 18);
            CurrentRetainVolume = DataConverter.GetInstance().ToInt32(data, startIndex + 20);
            CurrentAlarmStatus = (eAlarmStatusType)data[startIndex + 27];
            SystemTime = DataConvertHelper.ParseDateTime(data, startIndex + 28);
            DoorStatus = (eDoorStatusType)data[startIndex + 35];
            CurrentDoorPw = DataConverter.GetInstance().ToInt64(data, startIndex + 46).ToString();
            MaintainPw = DataConverter.GetInstance().ToInt64(data, startIndex + 50).ToString();
            FreezerTemperature = DataConverter.GetInstance().ToInt32(data, startIndex + 54) / 10.0;
            AlarmFlags = DataConverter.GetInstance().ToInt32(data, startIndex + 56);
            BucketAWaterVolume = DataConverter.GetInstance().ToInt32(data, startIndex + 66);
            BucketASampleTime = DataConverter.GetInstance().ToInt32(data, startIndex + 68);
            BucketBWaterVolume = DataConverter.GetInstance().ToInt32(data, startIndex + 70);
            BucketBSampleTime = DataConverter.GetInstance().ToInt32(data, startIndex + 72);

            var lastDoorOpTime = DataConvertHelper.ParseDateTime(data, startIndex + 36);
            var lastDoorPw = DataConverter.GetInstance().ToInt64(data, startIndex + 42).ToString();

            if(lastDoorOpTime != LastDoorOpTime)
            {
                LastDoorOpTime = lastDoorOpTime;
                LastDoorPw = lastDoorPw;
                SampleDoorLogHelper.WriteDoorLogToDb(lastDoorOpTime, lastDoorPw);
            }
        }

        /// <summary>
        /// 更新第二包参数
        /// </summary>
        /// <param name="data">数据字节数组</param>
        /// <param name="startIndex">起始索引</param>
        public void UpdateParamPackageTwo(byte[] data, int startIndex)
        {
            if(data == null || data.Length < startIndex + 20)
            {
                throw new ArgumentException("读取第二包参数回应数据不完整！");
            }

            LastSamplingTime = ParseDateTimeWithOutSecond(data, startIndex + 1);
            LastSupplyTime = ParseDateTimeWithOutSecond(data, startIndex + 7);
            LastRetainTime = ParseDateTimeWithOutSecond(data, startIndex + 13);
            RetainBottleNumber = DataConverter.GetInstance().ToInt32(data, startIndex + 18);
        }

        /// <summary>
        /// 解析时间，年到分
        /// </summary>
        /// <param name="data"></param>
        /// <param name="startIndex"></param>
        /// <returns></returns>
        private static DateTime ParseDateTimeWithOutSecond(byte[] data, int startIndex)
        {
            try
            {
                return new DateTime(data[startIndex] + 2000, data[startIndex + 1], data[startIndex + 2], data[startIndex + 3], data[startIndex + 4], 0);
            }
            catch
            {
                return DateTime.MinValue;
            }
        }

        #endregion
    }

    #region 枚举定义

    /// <summary>
    /// 设备版本信息
    /// </summary>
    public enum eDeviceVersionType
    {
        /// <summary>
        /// ZSC-VIB
        /// </summary>
        [Description("ZSC-VIB")]
        ZSC_VIB = 0,

        /// <summary>
        /// ZSC-VIC
        /// </summary>
        [Description("ZSC-VIC")]
        ZSC_VIC = 1,

        /// <summary>
        /// ZSC-IXC
        /// </summary>
        [Description("ZSC-IXC")]
        ZSC_IXC = 2,

        /// <summary>
        /// ZSC-VIIK
        /// </summary>
        [Description("ZSC-VIIK")]
        ZSC_VIIK = 3
    }

    /// <summary>
    /// 设备状态
    /// </summary>
    public enum eDeviceStatusType
    {
        /// <summary>
        /// 待机
        /// </summary>
        [Description("待机")]
        待机 = 0,

        /// <summary>
        /// 运行中
        /// </summary>
        [Description("运行中")]
        运行中 = 1
    }

    /// <summary>
    /// 桶状态
    /// </summary>
    public enum eBucketStatusType
    {
        /// <summary>
        /// 空闲
        /// </summary>
        [Description("空闲")]
        空闲 = 1,

        /// <summary>
        /// 清洗桶
        /// </summary>
        [Description("清洗桶")]
        清洗桶 = 2,

        /// <summary>
        /// 采样进水
        /// </summary>
        [Description("采样进水")]
        采样进水 = 3,

        /// <summary>
        /// 采样排样
        /// </summary>
        [Description("采样排样")]
        采样排样 = 4,

        /// <summary>
        /// 采样等待中
        /// </summary>
        [Description("采样等待中")]
        采样等待中 = 5,

        /// <summary>
        /// 供样中
        /// </summary>
        [Description("供样中")]
        供样中 = 6,

        /// <summary>
        /// 超标等待
        /// </summary>
        [Description("超标等待")]
        超标等待 = 7,

        /// <summary>
        /// 清洗留样瓶
        /// </summary>
        [Description("清洗留样瓶")]
        清洗留样瓶 = 8,

        /// <summary>
        /// 留样中
        /// </summary>
        [Description("留样中")]
        留样中 = 9,

        /// <summary>
        /// 留样管路排样
        /// </summary>
        [Description("留样管路排样")]
        留样管路排样 = 10,

        /// <summary>
        /// 管路排空
        /// </summary>
        [Description("管路排空")]
        管路排空 = 11,

        /// <summary>
        /// 供样等待中
        /// </summary>
        [Description("供样等待中")]
        供样等待中 = 12
    }

    /// <summary>
    /// 报警状态
    /// </summary>
    public enum eAlarmStatusType
    {
        /// <summary>
        /// 无报警信息
        /// </summary>
        [Description("无报警信息")]
        无报警信息 = 0,

        /// <summary>
        /// 有报警不影响运行
        /// </summary>
        [Description("有报警不影响运行")]
        有报警不影响运行 = 1,

        /// <summary>
        /// 有报警影响运行
        /// </summary>
        [Description("有报警影响运行")]
        有报警影响运行 = 2
    }

    /// <summary>
    /// 门禁状态
    /// </summary>
    public enum eDoorStatusType
    {
        /// <summary>
        /// 关门
        /// </summary>
        [Description("关门")]
        关门 = 0,

        /// <summary>
        /// 开门
        /// </summary>
        [Description("开门")]
        开门 = 1
    }

    #endregion
}