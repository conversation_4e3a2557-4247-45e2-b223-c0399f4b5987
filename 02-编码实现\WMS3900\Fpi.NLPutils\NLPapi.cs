﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using NLPutils.Audio;
using NLPutils.Command;
using NLPutils.iflytek;
using NLPutils.iflytek.MSCutil;

namespace NLPutils
{
    public class NLPapi
    {
        #region 对外接口
        public delegate void AwakenHandler();
        /// <summary>
        /// 唤醒事件
        /// </summary>
        public static event AwakenHandler AwakenEvent;

        public delegate void QuitAwakenHandler();
        /// <summary>
        /// 退出唤醒事件
        /// </summary>
        public static event QuitAwakenHandler QuitAwakenEvent;

        public delegate void AsrHandler(string text);
        /// <summary>
        /// 语音转文字事件
        /// </summary>
        public static event AsrHandler AsrEvent;

        public delegate void CommandHandler(ReturnCommand command);
        /// <summary>
        /// 命令识别事件
        /// </summary>
        public static event CommandHandler CommandEvent;

        /// <summary>
        /// 开启语音识别
        /// </summary>
        /// <param name="errMess">不为空则有报错</param>
        /// <returns>是否成功</returns>
        public static bool StartSpeechRecognition(out string errMess)
        {
            errMess = "";
            try
            {
                VoiceUIManager.Init();
                IflytekHelper.StartSpeechRecognition();
                return true;
            }
            catch (Exception ex)
            {
                errMess = ex.Message;
                Fpi.Log.LogUtil.Debug("NLPutils", "NLPapi  AudioUtil_MicrophoneData:" + ex.ToString());
                return false;
            }
        }

        /// <summary>
        /// 停止语音识别
        /// </summary>
        /// <param name="errMess">不为空则有报错</param>
        /// <returns>是否成功</returns>
        public static bool StopSpeechRecognition(out string errMess)
        {
            errMess = "";

            try
            {
                VoiceUIManager.UnInit();
                IflytekHelper.StopSpeechRecognition();
                return true;
            }
            catch (Exception ex)
            {
                errMess = ex.Message;
                Fpi.Log.LogUtil.Debug("NLPutils", "NLPapi  AudioUtil_MicrophoneData:" + ex.ToString());
                return false;
            }

        }

        /// <summary>
        /// 语音合成，读文本的声音
        /// </summary>
        /// <returns>ErrMess</returns>
        public static string SpeakText(string text, bool isNeedUiShow = false)
        {
            if (string.IsNullOrEmpty(text))
            {
                return "String Is Null Or Empty";
            }
            try
            {
                IflytekHelper.SpeakText(text);
                if (isNeedUiShow)
                {
                    VoiceUIManager.ShowText(text);
                }
                return "";
            }
            catch (Exception ex)
            {
                Fpi.Log.LogUtil.Debug("NLPutils", "NLPapi  SpeakText:" + ex.ToString());
                return ex.Message;
            }

        }

        /// <summary>
        /// 显示命令搜索界面
        /// </summary>
        /// <returns>ErrMess</returns>
        public static void ShowCmdSearchForm()
        {
            try
            {
                VoiceUIManager.ShowCmdSearchForm();
            }
            catch (Exception ex)
            {
                Fpi.Log.LogUtil.Debug("NLPutils", "ShowCmdSearchForm:" + ex.ToString());
            }

        }

        /// <summary>
        /// 设定命令
        /// </summary>
        /// <param name="voiceCommands">命令列表</param>
        /// <param name="errMess">如果有错误则不喂空</param>
        /// <returns></returns>
        public static bool SetVoiceCommandList(List<VoiceCommand> voiceCommands, out string errMess, string groupId = "")
        {
            errMess = "";

            try
            {
                VoiceCommandManager.GetInstance().SetVCmdList(voiceCommands, groupId);
                if (ASRaikitOffline.IsInit)
                {
                    ASRaikitOffline.UnInit();
                    ASRaikitOffline.Init();
                }
                
                return true;
            }
            catch (Exception ex)
            {
                errMess = ex.Message;
                Fpi.Log.LogUtil.Debug("NLPutils", "NLPapi SetVoiceCommandList:" + ex.ToString());
                return false;
            }

        }

        /// <summary>
        /// 播放一个.wav格式的文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        public static void PlayWavFile(string filePath)
        {
            if (File.Exists(filePath))
            {
                byte[] bytes = File.ReadAllBytes(filePath);
                AudioUtil.PlayWav(bytes);
            }
        }
        /// <summary>
        /// 播放一个.pcm格式的文件
        /// </summary>
        /// <param name="pcmPath">文件路径</param>
        public static void PlayPCMFile(string pcmPath)
        {
            if (File.Exists(pcmPath))
            {
                byte[] bytes = File.ReadAllBytes(pcmPath);
                AudioUtil.PlayPCM(bytes);
            }
        }

        /// <summary>
        /// 播放一个.wav格式的文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        public static byte[] ConvertPcmToWav(byte[] pcm)
        {
            return AudioUtil.ConvertPcmToWav(pcm.ToList()).ToArray();
        }
        /// <summary>
        /// 获取当前麦克风音量
        /// </summary>
        /// <returns></returns>
        public static float GetMicrophonePeak()
        {
            return AudioUtil.GetMicrophonePeak();
        }
        /// <summary>
        /// 设置语音识别麦克风音量阈值，小于这个阈值则认为是环境音量，不予采集
        /// </summary>
        /// <param name="volume">阈值[0-100]</param>
        /// <returns>ErrMess</returns>
        public static string SetEnvironmentVolume(float volume)
        {
            if (volume > 100 || volume < 0)
            {
                return "阈值[0-100]";
            }
            try
            {
                AudioUtil.SetEnvironmentVolume(volume);
                return "";
            }
            catch (Exception ex)
            {
                return ex.Message;
            }

        }

        /// <summary>
        /// 设置讲话的音量
        /// </summary>
        /// <param name="volume">音量[0-100]</param>
        /// <returns>ErrMess</returns>
        public static string SetSpeakVolume(int volume)
        {
            if (volume > 100 || volume < 0)
            {
                return "音量[0-100]";
            }
            try
            {
                IflytekHelper.SetVolume(volume);
                return "";
            }
            catch (Exception ex)
            {
                return ex.Message;
            }

        }


        #endregion

        internal static void OnAwaken()
        {
            try
            {
                if (AwakenEvent != null)
                {
                    AwakenEvent();
                    Fpi.Log.LogUtil.Debug("NLPutils", "OnAwaken");
                }
            }
            catch (Exception ex)
            {
                Fpi.Log.LogUtil.Debug("NLPutils", "OnAwaken"+ ex.ToString());
            }

        }

        internal static void OnAsr(string asr)
        {
            try
            {
                if (AsrEvent != null)
                {
                    AsrEvent(asr);
                    Fpi.Log.LogUtil.Debug("NLPutils", "OnAsr:" + asr);
                }
            }
            catch (Exception ex)
            {
                Fpi.Log.LogUtil.Debug("NLPutils", "OnAsr" + ex.ToString());

            }
        }

        internal static void OnQuitAwaken()
        {
            try
            {
                if (QuitAwakenEvent != null)
                {
                    QuitAwakenEvent();
                    Fpi.Log.LogUtil.Debug("NLPutils", "OnQuitAwaken");

                }
            }
            catch (Exception ex)
            {
                Fpi.Log.LogUtil.Debug("NLPutils", "OnQuitAwaken" + ex.ToString());

            }
        }
        internal static void OnCommand(ReturnCommand command)
        {
            try
            {
                if (CommandEvent != null)
                {
                    CommandEvent(command);
                    if (command != null)
                    {
                        Fpi.Log.LogUtil.Debug("NLPutils", "OnCommand" + command.id);
                    }

                }
            }
            catch (Exception ex)
            {
                Fpi.Log.LogUtil.Debug("NLPutils", "OnCommand" + ex.ToString());

            }
        }

    }
}
